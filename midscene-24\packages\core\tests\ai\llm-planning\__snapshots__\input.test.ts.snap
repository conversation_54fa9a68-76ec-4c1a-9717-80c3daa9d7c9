// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`automation - planning input > input value 1`] = `
[
  {
    "locate": {
      "id": "okgbn",
      "prompt": "",
    },
    "param": {
      "value": "learning english",
    },
    "thought": undefined,
    "type": "Input",
  },
]
`;

exports[`automation - planning input > input value 2`] = `
[
  {
    "locate": {
      "id": "okgbn",
      "prompt": "",
    },
    "param": {
      "value": "learning english",
    },
    "thought": undefined,
    "type": "Input",
  },
  {
    "locate": null,
    "param": {
      "value": "Enter",
    },
    "thought": undefined,
    "type": "KeyboardPress",
  },
]
`;

exports[`automation - planning input > input value Add, delete, correct and check 1`] = `
[
  {
    "locate": {
      "id": "okgbn",
      "prompt": "",
    },
    "param": {
      "value": "Learn English tomorrow",
    },
    "thought": undefined,
    "type": "Input",
  },
]
`;
