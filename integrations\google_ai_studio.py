"""
Google AI Studio integration for the Multi-Agent AI System.
"""
import os
from typing import Dict, Any, Optional
import google.generativeai as genai

class GoogleAIStudioClient:
    """Client for interacting with Google AI Studio models."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Google AI Studio client.

        Args:
            api_key: Google AI API key. If None, will use the GOOGLE_AI_API_KEY environment variable.
        """
        # Use provided API key or fall back to environment variable
        self.api_key = api_key or os.environ.get("GOOGLE_AI_API_KEY")
        if not self.api_key:
            raise ValueError("Google AI API key not provided and not found in environment variables")

        # Configure the client
        genai.configure(api_key=self.api_key)

    def generate_text(
        self,
        prompt: str,
        model_name: str = "gemini-pro",
        temperature: float = 0.7,
        max_output_tokens: int = 800
    ) -> str:
        """
        Generate text using a Google AI model.

        Args:
            prompt: The prompt to generate text from
            model_name: Name of the model to use
            temperature: Temperature for generation
            max_output_tokens: Maximum number of tokens to generate

        Returns:
            Generated text
        """
        model = genai.GenerativeModel(
            model_name=model_name,
            generation_config={
                "temperature": temperature,
                "max_output_tokens": max_output_tokens,
            }
        )

        response = model.generate_content(prompt)
        return response.text

    def export_code_snippet(
        self,
        prompt: str,
        model_name: str = "gemini-pro",
        language: str = "python",
        temperature: float = 0.2
    ) -> str:
        """
        Export a code snippet for using a prompt with Google AI.

        Args:
            prompt: The prompt to use in the code
            model_name: Name of the model to use
            language: Programming language for the snippet
            temperature: Temperature for generation

        Returns:
            Code snippet as a string
        """
        languages = {
            "python": "Python",
            "javascript": "JavaScript",
            "typescript": "TypeScript",
            "java": "Java",
            "go": "Go",
            "csharp": "C#",
        }

        lang = languages.get(language.lower(), "Python")

        model = genai.GenerativeModel(
            model_name=model_name,
            generation_config={
                "temperature": temperature,
                "max_output_tokens": 1000,
            }
        )

        code_prompt = f"""
        Generate {lang} code that uses the Google Generative AI library to send the following prompt to the {model_name} model:

        ```
        {prompt}
        ```

        Include all necessary imports, API key setup, and proper error handling. The code should be complete and runnable.
        Only output the code without any explanation.
        """

        response = model.generate_content(code_prompt)
        return response.text
