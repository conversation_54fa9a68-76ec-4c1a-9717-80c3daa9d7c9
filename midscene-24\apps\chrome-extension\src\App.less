.command-form {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.form-content {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
  min-height: 0;
  position: relative;
  gap: 24px;
}

.result-container {
  grid-row: 2;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
  overflow: auto;
}

.result-container>div {
  flex: 1;
  height: auto;
  min-height: 0;
}