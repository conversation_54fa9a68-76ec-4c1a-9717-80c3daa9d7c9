{"name": "webrover_frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "nodemon", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@types/react-syntax-highlighter": "^15.5.13", "clsx": "^2.1.1", "framer-motion": "^12.0.1", "geist": "^1.3.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "next": "15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "nodemon": "^3.1.9", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}