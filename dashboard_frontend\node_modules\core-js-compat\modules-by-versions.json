{"3.0": ["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.data-view", "es.date.now", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string", "es.function.bind", "es.function.has-instance", "es.function.name", "es.json.to-string-tag", "es.map", "es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-fixed", "es.number.to-precision", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "es.parse-float", "es.parse-int", "es.promise", "es.promise.finally", "es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "es.regexp.constructor", "es.regexp.exec", "es.regexp.flags", "es.regexp.to-string", "es.set", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.iterator", "es.string.match", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-string", "es.weak-map", "es.weak-set", "esnext.aggregate-error", "esnext.array.last-index", "esnext.array.last-item", "esnext.composite-key", "esnext.composite-symbol", "esnext.global-this", "esnext.map.delete-all", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.umulh", "esnext.number.from-string", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference", "esnext.set.union", "esnext.string.at", "esnext.string.code-points", "esnext.string.match-all", "esnext.string.replace-all", "esnext.symbol.dispose", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.dom-collections.for-each", "web.dom-collections.iterator", "web.immediate", "web.queue-microtask", "web.timers", "web.url", "web.url.to-json", "web.url-search-params"], "3.1": ["es.string.match-all", "es.symbol.match-all", "esnext.symbol.replace-all"], "3.2": ["es.promise.all-settled", "esnext.array.is-template-object", "esnext.map.update-or-insert", "esnext.symbol.async-dispose"], "3.3": ["es.global-this", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.map", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.map.upsert", "esnext.weak-map.upsert"], "3.4": ["es.json.stringify"], "3.5": ["esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values"], "3.6": ["es.regexp.sticky", "es.regexp.test"], "3.7": ["es.aggregate-error", "es.promise.any", "es.reflect.to-string-tag", "es.string.replace-all", "esnext.map.emplace", "esnext.weak-map.emplace"], "3.8": ["esnext.array.at", "esnext.array.filter-out", "esnext.array.unique-by", "esnext.bigint.range", "esnext.number.range", "esnext.typed-array.at", "esnext.typed-array.filter-out"], "3.9": ["esnext.array.find-last", "esnext.array.find-last-index", "esnext.typed-array.find-last", "esnext.typed-array.find-last-index", "esnext.typed-array.unique-by"], "3.11": ["esnext.object.has-own"], "3.12": ["esnext.symbol.matcher", "esnext.symbol.metadata"], "3.15": ["es.date.get-year", "es.date.set-year", "es.date.to-gmt-string", "es.escape", "es.regexp.dot-all", "es.string.substr", "es.unescape"], "3.16": ["esnext.array.filter-reject", "esnext.array.group-by", "esnext.typed-array.filter-reject", "esnext.typed-array.group-by"], "3.17": ["es.array.at", "es.object.has-own", "es.string.at-alternative", "es.typed-array.at"], "3.18": ["esnext.array.from-async", "esnext.typed-array.from-async"], "3.20": ["es.error.cause", "es.error.to-string", "es.aggregate-error.cause", "es.number.to-exponential", "esnext.array.group-by-to-map", "esnext.array.to-reversed", "esnext.array.to-sorted", "esnext.array.to-spliced", "esnext.array.with", "esnext.function.is-callable", "esnext.function.is-constructor", "esnext.function.un-this", "esnext.iterator.to-async", "esnext.string.cooked", "esnext.typed-array.to-reversed", "esnext.typed-array.to-sorted", "esnext.typed-array.to-spliced", "esnext.typed-array.with", "web.dom-exception.constructor", "web.dom-exception.stack", "web.dom-exception.to-string-tag", "web.structured-clone"], "3.21": ["web.atob", "web.btoa"], "3.23": ["es.array.find-last", "es.array.find-last-index", "es.array.push", "es.array.unshift", "es.typed-array.find-last", "es.typed-array.find-last-index", "esnext.array.group", "esnext.array.group-to-map", "esnext.symbol.metadata-key"], "3.24": ["esnext.async-iterator.indexed", "esnext.iterator.indexed"], "3.25": ["es.object.proto"], "3.26": ["esnext.string.is-well-formed", "esnext.string.to-well-formed", "web.self"], "3.27": ["esnext.suppressed-error.constructor", "esnext.async-disposable-stack.constructor", "esnext.async-iterator.async-dispose", "esnext.disposable-stack.constructor", "esnext.iterator.dispose", "esnext.set.difference.v2", "esnext.set.intersection.v2", "esnext.set.is-disjoint-from.v2", "esnext.set.is-subset-of.v2", "esnext.set.is-superset-of.v2", "esnext.set.symmetric-difference.v2", "esnext.set.union.v2", "esnext.string.dedent"], "3.28": ["es.array.to-reversed", "es.array.to-sorted", "es.array.to-spliced", "es.array.with", "es.typed-array.to-reversed", "es.typed-array.to-sorted", "es.typed-array.with", "esnext.array-buffer.detached", "esnext.array-buffer.transfer", "esnext.array-buffer.transfer-to-fixed-length", "esnext.function.demethodize", "esnext.iterator.range", "esnext.json.is-raw-json", "esnext.json.parse", "esnext.json.raw-json", "esnext.symbol.is-registered", "esnext.symbol.is-well-known"], "3.29": ["web.url-search-params.size"], "3.30": ["web.url.can-parse"], "3.31": ["es.string.is-well-formed", "es.string.to-well-formed", "esnext.function.metadata", "esnext.object.group-by", "esnext.promise.with-resolvers", "esnext.symbol.is-registered-symbol", "esnext.symbol.is-well-known-symbol", "web.url-search-params.delete", "web.url-search-params.has"], "3.32": ["esnext.data-view.get-float16", "esnext.data-view.get-uint8-clamped", "esnext.data-view.set-float16", "esnext.data-view.set-uint8-clamped", "esnext.math.f16round"], "3.33": ["esnext.regexp.escape"], "3.34": ["es.map.group-by", "es.object.group-by", "es.promise.with-resolvers", "esnext.uint8-array.from-base64", "esnext.uint8-array.from-hex", "esnext.uint8-array.to-base64", "esnext.uint8-array.to-hex"], "3.36": ["es.array-buffer.detached", "es.array-buffer.transfer", "es.array-buffer.transfer-to-fixed-length"], "3.37": ["es.set.difference.v2", "es.set.intersection.v2", "es.set.is-disjoint-from.v2", "es.set.is-subset-of.v2", "es.set.is-superset-of.v2", "es.set.symmetric-difference.v2", "es.set.union.v2", "esnext.math.sum-precise", "esnext.symbol.custom-matcher", "web.url.parse"], "3.38": ["esnext.uint8-array.set-from-base64", "esnext.uint8-array.set-from-hex"], "3.39": ["es.iterator.constructor", "es.iterator.drop", "es.iterator.every", "es.iterator.filter", "es.iterator.find", "es.iterator.flat-map", "es.iterator.for-each", "es.iterator.from", "es.iterator.map", "es.iterator.reduce", "es.iterator.some", "es.iterator.take", "es.iterator.to-array", "es.promise.try", "esnext.iterator.concat", "esnext.map.get-or-insert", "esnext.map.get-or-insert-computed", "esnext.weak-map.get-or-insert", "esnext.weak-map.get-or-insert-computed"], "3.40": ["esnext.error.is-error"], "3.41": ["es.data-view.get-float16", "es.data-view.set-float16", "es.math.f16round", "es.regexp.escape"], "3.43": ["es.array.from-async", "es.async-disposable-stack.constructor", "es.async-iterator.async-dispose", "es.disposable-stack.constructor", "es.error.is-error", "es.iterator.dispose", "es.suppressed-error.constructor", "es.symbol.async-dispose", "es.symbol.dispose", "esnext.iterator.chunks", "esnext.iterator.windows", "esnext.iterator.zip", "esnext.iterator.zip-keyed", "esnext.number.clamp"]}