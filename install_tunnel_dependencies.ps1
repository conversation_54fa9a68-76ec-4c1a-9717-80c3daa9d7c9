# PowerShell script to install the required packages for tunneling
# This script activates the .venvAI virtual environment and installs packages

# Change to the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Push-Location $scriptDir

# Check if .venvAI exists
if (-not (Test-Path ".\.venvAI\Scripts\activate.ps1")) {
    Write-Host "Virtual environment .venvAI not found. Please set up your environment first." -ForegroundColor Red
    exit 1
}

# Activate the virtual environment
Write-Host "Activating .venvAI virtual environment..." -ForegroundColor Blue
& ".\.venvAI\Scripts\activate.ps1"

# Install required packages
Write-Host "Installing required packages..." -ForegroundColor Green
pip install -r requirements-tunnel.txt

Write-Host "Dependencies installed successfully." -ForegroundColor Green
Write-Host "You can now run the dashboard with tunneling using 'start_dashboard_with_tunnel.bat'" -ForegroundColor Cyan

# Deactivate the virtual environment when done
deactivate

# Restore the previous location
Pop-Location
