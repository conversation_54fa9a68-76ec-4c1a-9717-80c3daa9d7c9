#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules/@modern-js/module-tools/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules/@modern-js/module-tools/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules/@modern-js/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules/@modern-js/module-tools/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules/@modern-js/module-tools/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules/@modern-js/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@modern-js/module-tools/bin/modern.js" "$@"
else
  exec node  "$basedir/../@modern-js/module-tools/bin/modern.js" "$@"
fi
