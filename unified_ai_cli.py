#!/usr/bin/env python3
"""
Unified AI CLI - Multi-Model AI Assistant
Supports Gemini, OpenAI, and Local LLMs (Ollama)
Uses your actual API keys from .env file
"""

import os
import sys
import argparse
import json
import requests
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import google.generativeai as genai
from openai import OpenAI
from llms.ollama_manager import OllamaManager

# Load environment variables
load_dotenv()

class UnifiedAICLI:
    def __init__(self):
        self.setup_apis()
        self.ollama = OllamaManager()

    def setup_apis(self):
        """Initialize API clients with actual keys from environment"""
        # Gemini API setup
        self.gemini_api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_AI_API_KEY')
        if self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-pro')
            print(f"✓ Gemini API initialized with key: {self.gemini_api_key[:20]}...")
        else:
            print("⚠ Gemini API key not found in environment")

        # OpenAI API setup
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if self.openai_api_key:
            self.openai_client = OpenAI(api_key=self.openai_api_key)
            print(f"✓ OpenAI API initialized with key: {self.openai_api_key[:20]}...")
        else:
            print("⚠ OpenAI API key not found in environment")

    def query_gemini(self, prompt: str, model: str = "gemini-pro") -> Dict[str, Any]:
        """Query Gemini using the correct Google Generative AI API"""
        if not self.gemini_api_key:
            return {"error": "Gemini API key not configured"}

        try:
            # Use the correct Google Generative AI URL
            base_url = os.getenv('GOOGLE_GENERATIVE_AI_URL', 'https://generativelanguage.googleapis.com/v1beta')
            url = f"{base_url}/models/{model}:generateContent"

            headers = {
                "Content-Type": "application/json",
                "x-goog-api-key": self.gemini_api_key
            }

            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                },
                "safetySettings": [
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                ]
            }

            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()

            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                text = result['candidates'][0]['content']['parts'][0]['text']
                return {
                    "model": model,
                    "response": text,
                    "usage": result.get('usageMetadata', {}),
                    "api_used": "google_generative_ai"
                }
            else:
                return {"error": "No response generated", "raw_response": result}

        except Exception as e:
            return {"error": f"Gemini API error: {str(e)}"}

    def query_openai(self, prompt: str, model: str = "gpt-3.5-turbo") -> Dict[str, Any]:
        """Query OpenAI API"""
        if not self.openai_api_key:
            return {"error": "OpenAI API key not configured"}

        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1024,
                temperature=0.7
            )

            return {
                "model": model,
                "response": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "api_used": "openai"
            }

        except Exception as e:
            return {"error": f"OpenAI API error: {str(e)}"}

    def query_ollama(self, prompt: str, model: str = "llama2") -> Dict[str, Any]:
        """Query local Ollama models"""
        try:
            # First try to list models to see what's available
            models = self.ollama.list_models()
            if models is None:
                return {"error": "Could not connect to Ollama. Make sure Ollama is running on localhost:11434"}

            # Query the model
            result = self.ollama.query_model(model, prompt)
            if result:
                return {
                    "model": model,
                    "response": result,
                    "api_used": "ollama_local"
                }
            else:
                return {"error": f"Failed to query Ollama model: {model}"}

        except Exception as e:
            return {"error": f"Ollama error: {str(e)}"}

    def list_available_models(self) -> Dict[str, List[str]]:
        """List available models for each API"""
        models = {
            "gemini": ["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro", "gemini-1.5-flash"] if self.gemini_api_key else [],
            "openai": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"] if self.openai_api_key else [],
            "ollama": []
        }

        # Try to get Ollama models
        try:
            ollama_models = self.ollama.list_models()
            if ollama_models and isinstance(ollama_models, dict) and 'models' in ollama_models:
                models["ollama"] = [model.get('name', 'unknown') for model in ollama_models['models']]
        except:
            models["ollama"] = ["Connection to Ollama failed - ensure Ollama is running"]

        return models

    def query(self, prompt: str, provider: str = "auto", model: str = None) -> Dict[str, Any]:
        """Query the specified AI provider"""
        if provider == "auto":
            # Try Gemini first, then OpenAI, then Ollama
            if self.gemini_api_key:
                provider = "gemini"
            elif self.openai_api_key:
                provider = "openai"
            else:
                provider = "ollama"

        if provider == "gemini":
            return self.query_gemini(prompt, model or "gemini-pro")
        elif provider == "openai":
            return self.query_openai(prompt, model or "gpt-3.5-turbo")
        elif provider == "ollama":
            return self.query_ollama(prompt, model or "llama2")
        else:
            return {"error": f"Unknown provider: {provider}"}

def main():
    parser = argparse.ArgumentParser(description="Unified AI CLI - Multi-Model AI Assistant")
    parser.add_argument("prompt", nargs="?", help="The prompt to send to the AI")
    parser.add_argument("-p", "--provider", choices=["auto", "gemini", "openai", "ollama"],
                       default="auto", help="AI provider to use")
    parser.add_argument("-m", "--model", help="Specific model to use")
    parser.add_argument("-l", "--list-models", action="store_true", help="List available models")
    parser.add_argument("-i", "--interactive", action="store_true", help="Interactive mode")
    parser.add_argument("-f", "--format", choices=["json", "text"], default="text", help="Output format")

    args = parser.parse_args()

    cli = UnifiedAICLI()

    if args.list_models:
        models = cli.list_available_models()
        print("\n🤖 Available Models:")
        for provider, model_list in models.items():
            print(f"\n{provider.upper()}:")
            for model in model_list:
                print(f"  • {model}")
        return

    if args.interactive:
        print("🤖 Unified AI CLI - Interactive Mode")
        print("Type 'exit' to quit, 'models' to list available models")
        print(f"Current provider: {args.provider}")
        print("-" * 50)

        while True:
            try:
                prompt = input("\n💬 You: ").strip()
                if prompt.lower() in ['exit', 'quit', 'q']:
                    break
                elif prompt.lower() == 'models':
                    models = cli.list_available_models()
                    for provider, model_list in models.items():
                        print(f"\n{provider.upper()}: {', '.join(model_list)}")
                    continue
                elif not prompt:
                    continue

                print(f"\n🔄 Querying {args.provider}...")
                result = cli.query(prompt, args.provider, args.model)

                if "error" in result:
                    print(f"❌ Error: {result['error']}")
                else:
                    print(f"\n🤖 {result.get('model', 'AI')}: {result['response']}")
                    if args.format == "json":
                        print(f"\n📊 Usage: {json.dumps(result.get('usage', {}), indent=2)}")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
        return

    if not args.prompt:
        parser.print_help()
        return

    print(f"🔄 Querying {args.provider}...")
    result = cli.query(args.prompt, args.provider, args.model)

    if args.format == "json":
        print(json.dumps(result, indent=2))
    else:
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"\n🤖 {result.get('model', 'AI')}: {result['response']}")
            if 'usage' in result:
                usage = result['usage']
                if 'total_tokens' in usage:
                    print(f"📊 Tokens used: {usage['total_tokens']}")

if __name__ == "__main__":
    main()
