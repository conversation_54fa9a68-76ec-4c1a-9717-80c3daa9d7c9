@echo off
echo 🚀 Starting WebRover Backend...
cd /d "C:\Users\<USER>\Documents\augment-projects\Ai Agent System\WebRover\backend"

REM Check if FastAPI is available
python -c "import fastapi; print('✓ FastAPI available')" 2>nul
if errorlevel 1 (
    echo ❌ FastAPI not found, installing...
    pip install fastapi uvicorn pydantic
)

echo 🌐 Starting WebRover Backend on http://localhost:8001
python simple_main.py
pause
