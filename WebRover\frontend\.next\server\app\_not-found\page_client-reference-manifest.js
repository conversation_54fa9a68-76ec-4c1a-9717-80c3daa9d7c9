globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/src/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ui/ParticlesBackground.tsx":{"*":{"id":"(ssr)/./src/components/ui/ParticlesBackground.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\font\\local\\target.css?{\"path\":\"node_modules\\\\geist\\\\dist\\\\font.js\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/geist-sans/Geist-Thin.woff2\",\"weight\":\"100\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-UltraLight.woff2\",\"weight\":\"200\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Light.woff2\",\"weight\":\"300\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Regular.woff2\",\"weight\":\"400\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Medium.woff2\",\"weight\":\"500\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-SemiBold.woff2\",\"weight\":\"600\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Bold.woff2\",\"weight\":\"700\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Black.woff2\",\"weight\":\"800\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-UltraBlack.woff2\",\"weight\":\"900\",\"style\":\"normal\"}],\"variable\":\"--font-geist-sans\",\"fallback\":[\"ui-sans-serif\",\"system-ui\",\"-apple-system\",\"BlinkMacSystemFont\",\"Inter\",\"Segoe UI\",\"Roboto\",\"sans-serif\",\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"]}],\"variableName\":\"GeistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\geist\\\\dist\\\\font.js\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/geist-sans/Geist-Thin.woff2\",\"weight\":\"100\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-UltraLight.woff2\",\"weight\":\"200\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Light.woff2\",\"weight\":\"300\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Regular.woff2\",\"weight\":\"400\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Medium.woff2\",\"weight\":\"500\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-SemiBold.woff2\",\"weight\":\"600\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Bold.woff2\",\"weight\":\"700\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-Black.woff2\",\"weight\":\"800\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-sans/Geist-UltraBlack.woff2\",\"weight\":\"900\",\"style\":\"normal\"}],\"variable\":\"--font-geist-sans\",\"fallback\":[\"ui-sans-serif\",\"system-ui\",\"-apple-system\",\"BlinkMacSystemFont\",\"Inter\",\"Segoe UI\",\"Roboto\",\"sans-serif\",\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"]}],\"variableName\":\"GeistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\font\\local\\target.css?{\"path\":\"node_modules\\\\geist\\\\dist\\\\font.js\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/geist-mono/GeistMono-Thin.woff2\",\"weight\":\"100\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-UltraLight.woff2\",\"weight\":\"200\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Light.woff2\",\"weight\":\"300\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Regular.woff2\",\"weight\":\"400\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Medium.woff2\",\"weight\":\"500\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-SemiBold.woff2\",\"weight\":\"600\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Bold.woff2\",\"weight\":\"700\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Black.woff2\",\"weight\":\"800\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-UltraBlack.woff2\",\"weight\":\"900\",\"style\":\"normal\"}],\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"]}],\"variableName\":\"GeistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"node_modules\\\\geist\\\\dist\\\\font.js\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/geist-mono/GeistMono-Thin.woff2\",\"weight\":\"100\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-UltraLight.woff2\",\"weight\":\"200\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Light.woff2\",\"weight\":\"300\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Regular.woff2\",\"weight\":\"400\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Medium.woff2\",\"weight\":\"500\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-SemiBold.woff2\",\"weight\":\"600\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Bold.woff2\",\"weight\":\"700\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-Black.woff2\",\"weight\":\"800\",\"style\":\"normal\"},{\"path\":\"./fonts/geist-mono/GeistMono-UltraBlack.woff2\",\"weight\":\"900\",\"style\":\"normal\"}],\"variable\":\"--font-geist-mono\",\"adjustFontFallback\":false,\"fallback\":[\"ui-monospace\",\"SFMono-Regular\",\"Roboto Mono\",\"Menlo\",\"Monaco\",\"Liberation Mono\",\"DejaVu Sans Mono\",\"Courier New\",\"monospace\"]}],\"variableName\":\"GeistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\src\\components\\ui\\ParticlesBackground.tsx":{"id":"(app-pages-browser)/./src/components/ui/ParticlesBackground.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\WebRover\\frontend\\src\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ParticlesBackground.tsx":{"*":{"id":"(rsc)/./src/components/ui/ParticlesBackground.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}