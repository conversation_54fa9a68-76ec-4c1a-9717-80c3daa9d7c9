"""
Convergence integration for the AI Agent System.
This module provides a client for the Convergence API for collaborative editing and real-time data synchronization.
"""
import os
import json
import asyncio
import websockets
import requests
from typing import Dict, List, Any, Optional, Callable
import logging

# Set up logger
logger = logging.getLogger("convergence_integration")

class ConvergenceClient:
    """Client for interacting with Convergence server."""

    def __init__(self, domain_url: str = None, username: str = None, password: str = None):
        """
        Initialize the Convergence client.

        Args:
            domain_url: URL of the Convergence domain
            username: Username for authentication
            password: Password for authentication
        """
        self.domain_url = domain_url or os.getenv("CONVERGENCE_DOMAIN_URL")
        self.username = username or os.getenv("CONVERGENCE_USERNAME")
        self.password = password or os.getenv("CONVERGENCE_PASSWORD")
        self.auth_token = None
        self.websocket = None
        self.message_handlers = {}
        self.models = {}
        self.connected = False
        self.message_id_counter = 1

        if not self.domain_url:
            raise ValueError("Convergence domain URL not provided")

    async def connect(self):
        """
        Connect to the Convergence server.
        """
        if self.connected:
            return

        # Get authentication token
        auth_url = f"{self.domain_url}/auth/authenticate"
        auth_data = {
            "username": self.username,
            "password": self.password
        }

        try:
            response = requests.post(auth_url, json=auth_data)
            response.raise_for_status()
            self.auth_token = response.json()["token"]

            # Connect to WebSocket
            ws_url = f"{self.domain_url.replace('http', 'ws')}/connection/websocket?token={self.auth_token}"
            self.websocket = await websockets.connect(ws_url)

            # Start the message handling loop
            asyncio.create_task(self._message_loop())

            self.connected = True
            logger.info("Connected to Convergence server")

        except Exception as e:
            logger.exception(f"Error connecting to Convergence server: {e}")
            raise

    async def _message_loop(self):
        """Handle incoming messages from the WebSocket."""
        if not self.websocket:
            raise ValueError("WebSocket connection not established")

        try:
            while True:
                message = await self.websocket.recv()
                message_data = json.loads(message)
                message_type = message_data.get("type")

                if message_type in self.message_handlers:
                    for handler in self.message_handlers[message_type]:
                        asyncio.create_task(handler(message_data))

        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
            self.connected = False
        except Exception as e:
            logger.exception(f"Error in message loop: {e}")
            self.connected = False

    async def disconnect(self):
        """Disconnect from the Convergence server."""
        if self.websocket:
            await self.websocket.close()
        self.connected = False
        logger.info("Disconnected from Convergence server")

    def register_message_handler(self, message_type: str, handler: Callable):
        """
        Register a handler for a specific message type.

        Args:
            message_type: Type of message to handle
            handler: Callback function that receives the message
        """
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)

    async def send_message(self, message: Dict[str, Any]):
        """
        Send a message to the server.

        Args:
            message: Message to send
        """
        if not self.websocket or not self.connected:
            raise ValueError("Not connected to Convergence server")

        message_id = self.message_id_counter
        self.message_id_counter += 1

        message["id"] = message_id
        await self.websocket.send(json.dumps(message))
        return message_id

    async def open_model(self, collection: str, model_id: str):
        """
        Open a model from the server.

        Args:
            collection: Collection of the model
            model_id: ID of the model

        Returns:
            The opened model
        """
        message = {
            "type": "openModel",
            "collection": collection,
            "modelId": model_id,
            "autoCreate": True
        }

        message_id = await self.send_message(message)

        # Wait for the model to be opened
        model_future = asyncio.get_event_loop().create_future()

        async def handle_open_model_response(msg):
            if msg.get("id") == message_id:
                model_data = msg.get("model", {})
                self.models[model_id] = model_data
                model_future.set_result(model_data)

        self.register_message_handler("openModelResponse", handle_open_model_response)

        return await model_future

    async def create_model(self, collection: str, model_id: str, data: Dict[str, Any]):
        """
        Create a new model on the server.

        Args:
            collection: Collection for the new model
            model_id: ID for the new model
            data: Initial data for the model

        Returns:
            The created model
        """
        message = {
            "type": "createModel",
            "collection": collection,
            "modelId": model_id,
            "data": data
        }

        message_id = await self.send_message(message)

        # Wait for the model to be created
        model_future = asyncio.get_event_loop().create_future()

        async def handle_create_model_response(msg):
            if msg.get("id") == message_id:
                model_data = msg.get("model", {})
                self.models[model_id] = model_data
                model_future.set_result(model_data)

        self.register_message_handler("createModelResponse", handle_create_model_response)

        return await model_future

    async def get_model_value(self, model_id: str, path: str):
        """
        Get a value from a model.

        Args:
            model_id: ID of the model
            path: Path to the value

        Returns:
            The value at the path
        """
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not opened")

        model = self.models[model_id]
        path_parts = path.split(".")

        value = model.get("data", {})
        for part in path_parts:
            if part in value:
                value = value[part]
            else:
                return None

        return value

    async def set_model_value(self, model_id: str, path: str, value: Any):
        """
        Set a value in a model.

        Args:
            model_id: ID of the model
            path: Path to the value
            value: New value
        """
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not opened")

        message = {
            "type": "setModelValue",
            "modelId": model_id,
            "path": path,
            "value": value
        }

        await self.send_message(message)
