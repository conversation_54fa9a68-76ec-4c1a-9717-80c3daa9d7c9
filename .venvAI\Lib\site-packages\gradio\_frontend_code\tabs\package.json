{"name": "@gradio/tabs", "version": "0.4.3", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "main_changeset": true, "main": "Index.svelte", "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./package.json": "./package.json"}, "dependencies": {"@gradio/utils": "workspace:^"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/tabs"}}