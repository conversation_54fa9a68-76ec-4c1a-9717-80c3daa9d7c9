!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.klona={})}(this,(function(e){e.klona=function e(t){var o,r,n;if(Array.isArray(t)){for(r=Array(o=t.length);o--;)r[o]=(n=t[o])&&"object"==typeof n?e(n):n;return r}if("[object Object]"===Object.prototype.toString.call(t)){for(o in r={},t)"__proto__"===o?Object.defineProperty(r,o,{value:e(t[o]),configurable:!0,enumerable:!0,writable:!0}):r[o]=(n=t[o])&&"object"==typeof n?e(n):n;return r}return t}}));