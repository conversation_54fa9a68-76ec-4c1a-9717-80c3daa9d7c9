{"name": "@surma/rollup-plugin-off-main-thread", "version": "2.2.3", "description": "Use Rollup with workers and ES6 modules today.", "main": "index.js", "scripts": {"fmt": "prettier --write 'tests/**/*.js' *.js *.md *.json", "test": "node ./run_tests.js"}, "author": "Surma <<EMAIL>>", "license": "Apache-2.0", "devDependencies": {"chai": "4.2.0", "chalk": "^2.4.2", "karma": "4.2.0", "karma-chai": "0.1.0", "karma-chrome-launcher": "3.0.0", "karma-firefox-launcher": "1.1.0", "karma-mocha": "1.3.0", "karma-safari-launcher": "1.0.0", "karma-safaritechpreview-launcher": "2.0.2", "mocha": "6.1.4", "prettier": "1.18.2", "rollup": "2.2.0"}, "repository": {"type": "git", "url": "https://github.com/surma/rollup-plugin-off-main-thread"}, "dependencies": {"ejs": "^3.1.6", "json5": "^2.2.0", "magic-string": "^0.25.0", "string.prototype.matchall": "^4.0.6"}}