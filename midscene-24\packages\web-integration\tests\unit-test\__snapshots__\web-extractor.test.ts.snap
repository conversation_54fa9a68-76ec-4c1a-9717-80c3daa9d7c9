// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`extractor > basic 1`] = `
[
  {
    "attributes": {
      "htmlTagName": "<h1>",
      "nodeType": "TEXT Node",
    },
    "content": "Data Record",
  },
  {
    "attributes": {
      "htmlTagName": "<h2>",
      "nodeType": "TEXT Node",
    },
    "content": "1970-01-01 19:25:01",
  },
  {
    "attributes": {
      "htmlTagName": "<h2>",
      "nodeType": "TEXT Node",
    },
    "content": "User Name: Stella",
  },
  {
    "attributes": {
      "htmlTagName": "<th>",
      "nodeType": "TEXT Node",
    },
    "content": "ID",
  },
  {
    "attributes": {
      "htmlTagName": "<th>",
      "nodeType": "TEXT Node",
    },
    "content": "Field 2",
  },
  {
    "attributes": {
      "htmlTagName": "<th>",
      "nodeType": "TEXT Node",
    },
    "content": "Field 3",
  },
  {
    "attributes": {
      "htmlTagName": "<th>",
      "nodeType": "TEXT Node",
    },
    "content": "Field 4",
  },
  {
    "attributes": {
      "htmlTagName": "<th>",
      "nodeType": "TEXT Node",
    },
    "content": "Field 5",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "30S",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Kace Cervantes",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Aylin Sawyer",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Jefferson Kirby",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Skyla Jefferson",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "70U",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Florence Davenport",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Dariel Acevedo",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Ashlynn Delacruz",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Memphis Leal",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "3AY",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Crystal Newman",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Anderson Brown",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Charlotte Griffith",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Franklin Everett",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "YPG",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Kori Payne",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Edward Blevins",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Aila Gill",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Matthias Reed",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "ZEN",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Magnolia Duke",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Kalel Glover",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Alessia Barton",
  },
  {
    "attributes": {
      "htmlTagName": "<td>",
      "nodeType": "TEXT Node",
    },
    "content": "Cassius Peck",
  },
  {
    "attributes": {
      "htmlTagName": "<h3>",
      "nodeType": "TEXT Node",
    },
    "content": "Form",
  },
  {
    "attributes": {
      "htmlTagName": "<label>",
      "nodeType": "TEXT Node",
    },
    "content": "Name:",
  },
  {
    "attributes": {
      "htmlTagName": "<input>",
      "id": "J_input",
      "nodeType": "FORM_ITEM Node",
      "placeholder": "Hello World This is Placeholder",
    },
    "content": "Hello World This is Placeholder",
  },
  {
    "attributes": {
      "htmlTagName": "<input>",
      "id": "J_input",
      "nodeType": "FORM_ITEM Node",
      "placeholder": "Hello World This is Placeholder",
      "value": "Now I am a value, instead of placeholder",
    },
    "content": "Now I am a value, instead of placeholder",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "BUTTON Node",
    },
    "content": "Click Me",
  },
  {
    "attributes": {
      "htmlTagName": "<label>",
      "nodeType": "TEXT Node",
    },
    "content": "Shape",
  },
  {
    "attributes": {
      "href": "https://www.google.com",
      "htmlTagName": "<div>",
      "nodeType": "Anchor Node",
    },
    "content": "Google",
  },
  {
    "attributes": {
      "href": "https://www.google.com",
      "htmlTagName": "<div>",
      "nodeType": "Anchor Node",
      "style": "width: 100px; height: 20px; vertical-align: middle; display: inline-block; background: #ccc;",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<input>",
      "nodeType": "FORM_ITEM Node",
      "placeholder": "You shouldn't see this placeholder.",
      "value": "Rectangle",
    },
    "content": "Rectangle",
  },
  {
    "attributes": {
      "htmlTagName": "<textarea>",
      "nodeType": "FORM_ITEM Node",
      "placeholder": "this_is_a_textarea",
    },
    "content": "this_is_a_textarea",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "English",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "中文",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "Tiếng Việt",
  },
  {
    "attributes": {
      "htmlTagName": "<label>",
      "nodeType": "TEXT Node",
    },
    "content": "Choose an option:",
  },
  {
    "attributes": {
      "htmlTagName": "<select>",
      "id": "options",
      "name": "options",
      "nodeType": "FORM_ITEM Node",
    },
    "content": "Option 1",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "This is zoomed content",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "Something Else",
  },
  {
    "attributes": {
      "htmlTagName": "<body>",
      "id": "J_resize",
      "nodeType": "CONTAINER Node",
      "style": "width:30px;height:30px;background: #AC0",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<label>",
      "nodeType": "TEXT Node",
    },
    "content": "输入搜索关键词",
  },
  {
    "attributes": {
      "aria-autocomplete": "list",
      "aria-expanded": "false",
      "aria-haspopup": "true",
      "aria-label": "搜索任何物品",
      "aria-owns": "ui-id-1",
      "autocapitalize": "off",
      "autocomplete": "off",
      "autocorrect": "off",
      "class": ".gh-tb.ui-autocomplete-input",
      "htmlTagName": "<input>",
      "id": "gh-ac",
      "maxlength": "300",
      "name": "_nkw",
      "nodeType": "FORM_ITEM Node",
      "placeholder": "搜索任何物品",
      "role": "combobox",
      "size": "50",
      "spellcheck": "false",
      "type": "text",
    },
    "content": "搜索任何物品",
  },
  {
    "attributes": {
      "class": ".life-core-input.life-core-input-size-md",
      "htmlTagName": "<input>",
      "nodeType": "FORM_ITEM Node",
      "placeholder": "验证码",
      "tabindex": "0",
      "type": "text",
    },
    "content": "验证码",
  },
  {
    "attributes": {
      "class": ".life-core-checkbox-icon",
      "htmlTagName": "<label>",
      "nodeType": "CONTAINER Node",
    },
    "content": "",
  },
  {
    "attributes": {
      "aria-label": "The phrase 'The quick brown fox jumps over the lazy dog' is a well-known English-language pangram, meaning it contains every letter of the alphabet at least once. This sentence has become a standard tool for various applications, particularly in typing practice, font display, and keyboard testing. T...",
      "htmlTagName": "<body>",
      "nodeType": "CONTAINER Node",
      "style": "width: 100px; height: 100px; background-color: #ccc;",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<body>",
      "nodeType": "TEXT Node",
    },
    "content": "phrase "The quick brown fox jumps over the lazy dog" is a well-known English-language pangram, meaning it contains every letter of the alphabet at least once. This sentence has become a standard tool for various applications, particularly     in typing practice, font display, and keyboard testing. The phrase has permeated popular culture and is referenced in various media, including literature and film. Its simplicity and utility have made it a staple in educational contexts and beyond.     For instance, it was famously used as the first message sent over the Moscow–Washington hotline in 19634.",
  },
  {
    "attributes": {
      "aria-label": "Search",
      "class": ".btn",
      "htmlTagName": "<body>",
      "nodeType": "BUTTON Node",
      "tabindex": "0",
      "type": "submit",
    },
    "content": " ",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "content 000",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "CONTAINER Node",
      "style": "height: 30px;width: 100px;",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "content AAA",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "IMG Node",
      "src": "image",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "BUTTON Node",
      "very-long-attr": "width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;",
    },
    "content": "long-style-content",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "Click me",
  },
  {
    "attributes": {
      "aria-controls": "semi-select-5yxiyng",
      "class": ".widget",
      "htmlTagName": "<body>",
      "nodeType": "CONTAINER Node",
    },
    "content": "",
  },
  {
    "attributes": {
      "aria-labelledby": "eval_object.object_type-label",
      "class": ".widget",
      "htmlTagName": "<body>",
      "nodeType": "CONTAINER Node",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "Content 1",
  },
  {
    "attributes": {
      "class": ".child-container-without-content-2-1",
      "htmlTagName": "<div>",
      "nodeType": "CONTAINER Node",
      "style": "width: 100px; height: 100px; background-color: #ccc;",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "Content 2",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "Nested Content 3",
  },
  {
    "attributes": {
      "class": ".child-container-4",
      "htmlTagName": "<div>",
      "nodeType": "CONTAINER Node",
      "style": "width: 100px; height: 100px; background-color: #ccc;",
    },
    "content": "",
  },
  {
    "attributes": {
      "class": ".child-container-5",
      "htmlTagName": "<body>",
      "nodeType": "IMG Node",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "IMG Node",
      "svgContent": "true",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "hidden label",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "i am fixed child content",
  },
  {
    "attributes": {
      "htmlTagName": "<span>",
      "nodeType": "TEXT Node",
    },
    "content": "abcd efg",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "content editable div content. We should collect the parent.",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "absolute child content",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "content Right",
  },
  {
    "attributes": {
      "class": ".two-columns",
      "htmlTagName": "<body>",
      "nodeType": "CONTAINER Node",
    },
    "content": "",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "AAA",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "TEXT Node",
    },
    "content": "This should be collected",
  },
  {
    "attributes": {
      "htmlTagName": "",
      "nodeType": "TEXT Node",
    },
    "content": "Child Page",
  },
  {
    "attributes": {
      "htmlTagName": "",
      "nodeType": "TEXT Node",
    },
    "content": "This is a child page.",
  },
  {
    "attributes": {
      "htmlTagName": "",
      "nodeType": "TEXT Node",
    },
    "content": "Click me",
  },
]
`;

exports[`extractor > basic 2`] = `
{
  "children": [
    {
      "children": [
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<h1>",
                  "nodeType": "TEXT Node",
                },
                "content": "Data Record",
                "indexId": 0,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<h2>",
                  "nodeType": "TEXT Node",
                },
                "content": "1970-01-01 19:25:01",
                "indexId": 1,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<h2>",
                  "nodeType": "TEXT Node",
                },
                "content": "User Name: Stella",
                "indexId": 2,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<th>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "ID",
                            "indexId": 3,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<th>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Field 2",
                            "indexId": 4,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<th>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Field 3",
                            "indexId": 5,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<th>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Field 4",
                            "indexId": 6,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<th>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Field 5",
                            "indexId": 7,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "30S",
                            "indexId": 8,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Kace Cervantes",
                            "indexId": 9,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Aylin Sawyer",
                            "indexId": 10,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Jefferson Kirby",
                            "indexId": 11,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Skyla Jefferson",
                            "indexId": 12,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "70U",
                            "indexId": 13,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Florence Davenport",
                            "indexId": 14,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Dariel Acevedo",
                            "indexId": 15,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Ashlynn Delacruz",
                            "indexId": 16,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Memphis Leal",
                            "indexId": 17,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "3AY",
                            "indexId": 18,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Crystal Newman",
                            "indexId": 19,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Anderson Brown",
                            "indexId": 20,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Charlotte Griffith",
                            "indexId": 21,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Franklin Everett",
                            "indexId": 22,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "YPG",
                            "indexId": 23,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Kori Payne",
                            "indexId": 24,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Edward Blevins",
                            "indexId": 25,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Aila Gill",
                            "indexId": 26,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Matthias Reed",
                            "indexId": 27,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "ZEN",
                            "indexId": 28,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Magnolia Duke",
                            "indexId": 29,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Kalel Glover",
                            "indexId": 30,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Alessia Barton",
                            "indexId": 31,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": {
                            "attributes": {
                              "htmlTagName": "<td>",
                              "nodeType": "TEXT Node",
                            },
                            "content": "Cassius Peck",
                            "indexId": 32,
                          },
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<h3>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "Form",
                    "indexId": 33,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<label>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "Name:",
                    "indexId": 34,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<input>",
                  "id": "J_input",
                  "nodeType": "FORM_ITEM Node",
                  "placeholder": "Hello World This is Placeholder",
                },
                "content": "Hello World This is Placeholder",
                "indexId": 35,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<input>",
                  "id": "J_input",
                  "nodeType": "FORM_ITEM Node",
                  "placeholder": "Hello World This is Placeholder",
                  "value": "Now I am a value, instead of placeholder",
                },
                "content": "Now I am a value, instead of placeholder",
                "indexId": 36,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "BUTTON Node",
                },
                "content": "Click Me",
                "indexId": 37,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<label>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "Shape",
                    "indexId": 38,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "href": "https://www.google.com",
                  "htmlTagName": "<div>",
                  "nodeType": "Anchor Node",
                },
                "content": "Google",
                "indexId": 39,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "href": "https://www.google.com",
                  "htmlTagName": "<div>",
                  "nodeType": "Anchor Node",
                  "style": "width: 100px; height: 20px; vertical-align: middle; display: inline-block; background: #ccc;",
                },
                "content": "",
                "indexId": 40,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<input>",
                  "nodeType": "FORM_ITEM Node",
                  "placeholder": "You shouldn't see this placeholder.",
                  "value": "Rectangle",
                },
                "content": "Rectangle",
                "indexId": 41,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<textarea>",
                  "nodeType": "FORM_ITEM Node",
                  "placeholder": "this_is_a_textarea",
                },
                "content": "this_is_a_textarea",
                "indexId": 42,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": null,
                        },
                        {
                          "children": [
                            {
                              "children": [
                                {
                                  "children": [],
                                  "node": {
                                    "attributes": {
                                      "htmlTagName": "<span>",
                                      "nodeType": "TEXT Node",
                                    },
                                    "content": "English",
                                    "indexId": 43,
                                  },
                                },
                              ],
                              "node": null,
                            },
                          ],
                          "node": null,
                        },
                        {
                          "children": [],
                          "node": null,
                        },
                        {
                          "children": [
                            {
                              "children": [
                                {
                                  "children": [],
                                  "node": {
                                    "attributes": {
                                      "htmlTagName": "<span>",
                                      "nodeType": "TEXT Node",
                                    },
                                    "content": "中文",
                                    "indexId": 44,
                                  },
                                },
                              ],
                              "node": null,
                            },
                          ],
                          "node": null,
                        },
                        {
                          "children": [],
                          "node": null,
                        },
                        {
                          "children": [
                            {
                              "children": [
                                {
                                  "children": [],
                                  "node": {
                                    "attributes": {
                                      "htmlTagName": "<span>",
                                      "nodeType": "TEXT Node",
                                    },
                                    "content": "Tiếng Việt",
                                    "indexId": 45,
                                  },
                                },
                              ],
                              "node": null,
                            },
                            {
                              "children": [],
                              "node": null,
                            },
                          ],
                          "node": null,
                        },
                        {
                          "children": [],
                          "node": null,
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<label>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "Choose an option:",
                    "indexId": 46,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<select>",
                  "id": "options",
                  "name": "options",
                  "nodeType": "FORM_ITEM Node",
                },
                "content": "Option 1",
                "indexId": 47,
              },
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "This is zoomed content",
                    "indexId": 48,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "TEXT Node",
                },
                "content": "Something Else",
                "indexId": 49,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "htmlTagName": "<body>",
              "id": "J_resize",
              "nodeType": "CONTAINER Node",
              "style": "width:30px;height:30px;background: #AC0",
            },
            "content": "",
            "indexId": 50,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<label>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "输入搜索关键词",
                    "indexId": 51,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "aria-autocomplete": "list",
                  "aria-expanded": "false",
                  "aria-haspopup": "true",
                  "aria-label": "搜索任何物品",
                  "aria-owns": "ui-id-1",
                  "autocapitalize": "off",
                  "autocomplete": "off",
                  "autocorrect": "off",
                  "class": ".gh-tb.ui-autocomplete-input",
                  "htmlTagName": "<input>",
                  "id": "gh-ac",
                  "maxlength": "300",
                  "name": "_nkw",
                  "nodeType": "FORM_ITEM Node",
                  "placeholder": "搜索任何物品",
                  "role": "combobox",
                  "size": "50",
                  "spellcheck": "false",
                  "type": "text",
                },
                "content": "搜索任何物品",
                "indexId": 52,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "class": ".life-core-input.life-core-input-size-md",
                  "htmlTagName": "<input>",
                  "nodeType": "FORM_ITEM Node",
                  "placeholder": "验证码",
                  "tabindex": "0",
                  "type": "text",
                },
                "content": "验证码",
                "indexId": 53,
              },
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "class": ".life-core-checkbox-icon",
                      "htmlTagName": "<label>",
                      "nodeType": "CONTAINER Node",
                    },
                    "content": "",
                    "indexId": 54,
                  },
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "aria-label": "The phrase 'The quick brown fox jumps over the lazy dog' is a well-known English-language pangram, meaning it contains every letter of the alphabet at least once. This sentence has become a standard tool for various applications, particularly in typing practice, font display, and keyboard testing. T...",
              "htmlTagName": "<body>",
              "nodeType": "CONTAINER Node",
              "style": "width: 100px; height: 100px; background-color: #ccc;",
            },
            "content": "",
            "indexId": 55,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "htmlTagName": "<body>",
              "nodeType": "TEXT Node",
            },
            "content": "phrase "The quick brown fox jumps over the lazy dog" is a well-known English-language pangram, meaning it contains every letter of the alphabet at least once. This sentence has become a standard tool for various applications, particularly     in typing practice, font display, and keyboard testing. The phrase has permeated popular culture and is referenced in various media, including literature and film. Its simplicity and utility have made it a staple in educational contexts and beyond.     For instance, it was famously used as the first message sent over the Moscow–Washington hotline in 19634.",
            "indexId": 56,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "aria-label": "Search",
              "class": ".btn",
              "htmlTagName": "<body>",
              "nodeType": "BUTTON Node",
              "tabindex": "0",
              "type": "submit",
            },
            "content": " ",
            "indexId": 57,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "content 000",
                    "indexId": 58,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "CONTAINER Node",
                  "style": "height: 30px;width: 100px;",
                },
                "content": "",
                "indexId": 59,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "content AAA",
                    "indexId": 60,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "IMG Node",
                  "src": "image",
                },
                "content": "",
                "indexId": 61,
              },
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "BUTTON Node",
                  "very-long-attr": "width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;",
                },
                "content": "long-style-content",
                "indexId": 62,
              },
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "TEXT Node",
                },
                "content": "Click me",
                "indexId": 63,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "aria-controls": "semi-select-5yxiyng",
              "class": ".widget",
              "htmlTagName": "<body>",
              "nodeType": "CONTAINER Node",
            },
            "content": "",
            "indexId": 64,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "aria-labelledby": "eval_object.object_type-label",
              "class": ".widget",
              "htmlTagName": "<body>",
              "nodeType": "CONTAINER Node",
            },
            "content": "",
            "indexId": 65,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "Content 1",
                    "indexId": 66,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "class": ".child-container-without-content-2-1",
                      "htmlTagName": "<div>",
                      "nodeType": "CONTAINER Node",
                      "style": "width: 100px; height: 100px; background-color: #ccc;",
                    },
                    "content": "",
                    "indexId": 67,
                  },
                },
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": null,
                        },
                        {
                          "children": [
                            {
                              "children": [],
                              "node": {
                                "attributes": {
                                  "htmlTagName": "<span>",
                                  "nodeType": "TEXT Node",
                                },
                                "content": "Content 2",
                                "indexId": 68,
                              },
                            },
                          ],
                          "node": null,
                        },
                        {
                          "children": [],
                          "node": null,
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": null,
                    },
                    {
                      "children": [
                        {
                          "children": [],
                          "node": null,
                        },
                        {
                          "children": [
                            {
                              "children": [],
                              "node": {
                                "attributes": {
                                  "htmlTagName": "<span>",
                                  "nodeType": "TEXT Node",
                                },
                                "content": "Nested Content 3",
                                "indexId": 69,
                              },
                            },
                          ],
                          "node": null,
                        },
                        {
                          "children": [],
                          "node": null,
                        },
                      ],
                      "node": null,
                    },
                    {
                      "children": [],
                      "node": null,
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "class": ".child-container-4",
                  "htmlTagName": "<div>",
                  "nodeType": "CONTAINER Node",
                  "style": "width: 100px; height: 100px; background-color: #ccc;",
                },
                "content": "",
                "indexId": 70,
              },
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "class": ".child-container-5",
              "htmlTagName": "<body>",
              "nodeType": "IMG Node",
            },
            "content": "",
            "indexId": 71,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<span>",
                  "nodeType": "IMG Node",
                  "svgContent": "true",
                },
                "content": "",
                "indexId": 72,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<span>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "hidden label",
                    "indexId": 73,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
                {
                  "children": [
                    {
                      "children": [],
                      "node": {
                        "attributes": {
                          "htmlTagName": "<div>",
                          "nodeType": "TEXT Node",
                        },
                        "content": "i am fixed child content",
                        "indexId": 74,
                      },
                    },
                  ],
                  "node": null,
                },
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<span>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "abcd efg",
                    "indexId": 75,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "content editable div content. We should collect the parent.",
                    "indexId": 76,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "absolute child content",
                    "indexId": 77,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "content Right",
                    "indexId": 78,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": {
            "attributes": {
              "class": ".two-columns",
              "htmlTagName": "<body>",
              "nodeType": "CONTAINER Node",
            },
            "content": "",
            "indexId": 79,
          },
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "AAA",
                    "indexId": 80,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": null,
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
            {
              "children": [
                {
                  "children": [],
                  "node": {
                    "attributes": {
                      "htmlTagName": "<div>",
                      "nodeType": "TEXT Node",
                    },
                    "content": "This should be collected",
                    "indexId": 81,
                  },
                },
              ],
              "node": null,
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
      ],
      "node": null,
    },
    {
      "children": [
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "",
                  "nodeType": "TEXT Node",
                },
                "content": "Child Page",
                "indexId": 83,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "",
                  "nodeType": "TEXT Node",
                },
                "content": "This is a child page.",
                "indexId": 84,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "",
                  "nodeType": "TEXT Node",
                },
                "content": "Click me",
                "indexId": 85,
              },
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
      ],
      "node": null,
    },
  ],
  "node": null,
}
`;

exports[`extractor > getElementInfoByXpath by evaluateJavaScript 1`] = `
{
  "attributes": {
    "htmlTagName": "<span>",
    "nodeType": "TEXT Node",
  },
  "center": [
    556,
    46,
  ],
  "content": "中文",
  "id": "emaam",
  "indexId": 0,
  "locator": "",
  "nodeHashId": "emaam",
  "nodeType": "TEXT Node",
  "rect": {
    "height": 18,
    "left": 540,
    "top": 37,
    "width": 32,
    "zoom": 1,
  },
  "zoom": 1,
}
`;

exports[`extractor > getElementInfoByXpath from button node by evaluateJavaScript 1`] = `
{
  "aria-label": "Search",
  "class": ".btn",
  "htmlTagName": "<body>",
  "nodeType": "BUTTON Node",
  "tabindex": "0",
  "type": "submit",
}
`;

exports[`extractor > getElementInfoByXpath from div node by evaluateJavaScript 1`] = `
{
  "attributes": {
    "aria-label": "Search",
    "class": ".btn",
    "htmlTagName": "<body>",
    "nodeType": "BUTTON Node",
    "tabindex": "0",
    "type": "submit",
  },
  "center": [
    283,
    1160,
  ],
  "content": " ",
  "id": "kohcf",
  "indexId": 0,
  "locator": "[_midscene_retrieve_task_id='kohcf']",
  "nodeHashId": "kohcf",
  "nodeType": "BUTTON Node",
  "rect": {
    "height": 210,
    "left": 73,
    "top": 1055,
    "width": 420,
    "zoom": 1,
  },
  "zoom": 1,
}
`;

exports[`extractor > getElementInfoByXpath from text node by evaluateJavaScript 1`] = `
{
  "htmlTagName": "<span>",
  "nodeType": "TEXT Node",
}
`;

exports[`extractor > merge children rects of button 1`] = `
[
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "BUTTON Node",
      "style": "width: 20px; height: 20px",
    },
    "content": "Click Me(span)",
  },
  {
    "attributes": {
      "htmlTagName": "<div>",
      "nodeType": "BUTTON Node",
      "style": "width: 20px; height: 20px",
    },
    "content": "Click Me(text)",
  },
]
`;

exports[`extractor > merge children rects of button 2`] = `
{
  "children": [
    {
      "children": [
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "BUTTON Node",
                  "style": "width: 20px; height: 20px",
                },
                "content": "Click Me(span)",
                "indexId": 0,
              },
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
        {
          "children": [
            {
              "children": [],
              "node": null,
            },
            {
              "children": [],
              "node": {
                "attributes": {
                  "htmlTagName": "<div>",
                  "nodeType": "BUTTON Node",
                  "style": "width: 20px; height: 20px",
                },
                "content": "Click Me(text)",
                "indexId": 1,
              },
            },
            {
              "children": [],
              "node": null,
            },
          ],
          "node": null,
        },
        {
          "children": [],
          "node": null,
        },
      ],
      "node": null,
    },
  ],
  "node": null,
}
`;
