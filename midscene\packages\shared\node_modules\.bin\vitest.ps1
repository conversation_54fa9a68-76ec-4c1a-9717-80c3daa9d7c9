#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0\node_modules\vitest\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../vitest/vitest.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../vitest/vitest.mjs" $args
  } else {
    & "node$exe"  "$basedir/../vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
