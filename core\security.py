"""
Security utilities for the multi-agent system.
"""
import os
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer

# Initialize OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Get JWT settings from environment or config
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-for-development")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
JWT_EXPIRATION_MINUTES = int(os.getenv("JWT_EXPIRATION_MINUTES", "60"))

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.

    Args:
        data: Data to encode in the token
        expires_delta: Optional token expiration time

    Returns:
        Encoded JW<PERSON> token
    """
    to_encode = data.copy()

    # Set expiration time
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=JWT_EXPIRATION_MINUTES)

    to_encode.update({"exp": expire})

    # Encode and return the token
    return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    Validate and decode a JWT token to get the current user.

    Args:
        token: JWT token from the request

    Returns:
        User data from the token

    Raises:
        HTTPException: If token is invalid or expired
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Decode the token
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

        # Extract user data
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception

        # Return user data
        return {"username": username, "roles": payload.get("roles", [])}

    except jwt.PyJWTError:
        raise credentials_exception

def has_role(required_role: str):
    """
    Dependency to check if user has a specific role.

    Args:
        required_role: Role required to access the endpoint

    Returns:
        Dependency function that validates the user's role

    Raises:
        HTTPException: If user doesn't have the required role
    """
    async def role_checker(user: Dict[str, Any] = Depends(get_current_user)):
        roles = user.get("roles", [])

        if required_role not in roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied. Role '{required_role}' is required."
            )

        return user

    return role_checker
