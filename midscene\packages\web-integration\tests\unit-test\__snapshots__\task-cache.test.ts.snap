// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TaskCache > save and retrieve cache from file 1`] = `
[
  {
    "prompt": "test",
    "type": "plan",
    "yamlWorkflow": "test-yaml-workflow",
  },
  {
    "prompt": "test-locate",
    "type": "locate",
    "xpaths": [
      "test-xpath-1",
      "test-xpath-2",
    ],
  },
]
`;

exports[`TaskCache > save and retrieve cache from file 2`] = `
[
  {
    "prompt": "test",
    "type": "plan",
    "yamlWorkflow": "test-yaml-workflow",
  },
  {
    "prompt": "test-locate",
    "type": "locate",
    "xpaths": [
      "test-xpath-3",
      "test-xpath-4",
    ],
  },
]
`;

exports[`TaskCache > save and retrieve cache from file 3`] = `
"midsceneVersion: 0.16.11
cacheId: cacheId
caches:
  - type: plan
    prompt: test
    yamlWorkflow: test-yaml-workflow
  - type: locate
    prompt: test-locate
    xpaths:
      - test-xpath-3
      - test-xpath-4
"
`;

exports[`TaskCache > should create cache file 1`] = `
"midsceneVersion: 0.16.11
cacheId: cacheId
caches:
  - type: plan
    prompt: test
    yamlWorkflow: test
"
`;

exports[`TaskCache > update or append cache record - should not match cache added in same run 1`] = `
[
  {
    "prompt": "test-prompt",
    "type": "plan",
    "yamlWorkflow": "test-yaml-workflow",
  },
  {
    "prompt": "test-prompt",
    "type": "plan",
    "yamlWorkflow": "test-yaml-workflow-2",
  },
]
`;
