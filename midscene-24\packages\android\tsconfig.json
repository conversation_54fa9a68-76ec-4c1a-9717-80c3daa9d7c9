{"compilerOptions": {"allowJs": true, "baseUrl": ".", "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["DOM", "ESNext"], "moduleResolution": "node", "resolveJsonModule": true, "rootDir": "src", "skipLibCheck": true, "strict": true, "module": "ES2020", "target": "es2020", "types": ["node"]}, "exclude": ["**/node_modules"], "include": ["src"]}