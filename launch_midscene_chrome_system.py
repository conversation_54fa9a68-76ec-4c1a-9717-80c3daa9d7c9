#!/usr/bin/env python3
"""
Complete Midscene Chrome Extension Integration System
Launches the Midscene Chrome Extension with full AI Agent System integration
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import time
import webbrowser
from pathlib import Path
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MidsceneChromeSystemLauncher:
    """Complete Midscene Chrome Extension System Launcher"""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.midscene_path = self.project_root / "midscene"
        self.extension_path = self.midscene_path / "apps" / "chrome-extension"
        self.extension_dist_path = self.extension_path / "dist"
        self.extension_zip_path = self.extension_path / "extension_output" / "midscene-extension-v0.12.4.zip"

        # Chrome paths
        self.chrome_paths = [
            "C:/Program Files/Google/Chrome/Application/chrome.exe",
            "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
            "chrome.exe",
            "google-chrome",
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]

        self.chrome_exe = None
        self.chrome_process = None

    def find_chrome_executable(self) -> Optional[str]:
        """Find Chrome executable path"""
        logger.info("🔍 Looking for Chrome browser...")

        for path in self.chrome_paths:
            if os.path.exists(path):
                logger.info(f"✅ Found Chrome at: {path}")
                return path

        # Try to find Chrome in PATH
        try:
            result = subprocess.run(["where", "chrome"], capture_output=True, text=True)
            if result.returncode == 0:
                chrome_path = result.stdout.strip().split('\n')[0]
                logger.info(f"✅ Found Chrome in PATH: {chrome_path}")
                return chrome_path
        except:
            pass

        logger.error("❌ Chrome browser not found. Please install Google Chrome.")
        return None

    def check_extension_built(self) -> bool:
        """Check if the extension is properly built"""
        logger.info("🔍 Checking Midscene extension build status...")

        required_files = [
            self.extension_dist_path / "manifest.json",
            self.extension_dist_path / "index.html",
            self.extension_dist_path / "icon128.png"
        ]

        for file_path in required_files:
            if not file_path.exists():
                logger.error(f"❌ Missing required extension file: {file_path}")
                return False

        if not self.extension_zip_path.exists():
            logger.warning(f"⚠️ Extension zip not found: {self.extension_zip_path}")
        else:
            logger.info(f"✅ Extension zip available: {self.extension_zip_path}")

        logger.info("✅ Midscene extension build is complete")
        return True

    def install_python_dependencies(self):
        """Install required Python dependencies for integration"""
        logger.info("📦 Installing Python dependencies for Midscene integration...")

        required_packages = [
            "websockets",
            "aiohttp",
            "openai",
            "asyncio"
        ]

        for package in required_packages:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package],
                             check=True, capture_output=True)
                logger.info(f"✅ Installed {package}")
            except subprocess.CalledProcessError as e:
                logger.warning(f"⚠️ Failed to install {package}: {e}")

    def launch_chrome_with_extension(self) -> bool:
        """Launch Chrome with the Midscene extension loaded"""
        logger.info("🚀 Launching Chrome with Midscene extension...")

        if not self.chrome_exe:
            self.chrome_exe = self.find_chrome_executable()
            if not self.chrome_exe:
                return False

        # Chrome arguments for extension development
        chrome_args = [
            self.chrome_exe,
            f"--load-extension={self.extension_dist_path}",
            "--remote-debugging-port=9222",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--user-data-dir=" + str(self.project_root / "chrome-user-data"),
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-popup-blocking",
            "--enable-automation",
            "--window-size=1920,1080"
        ]

        try:
            logger.info("🌐 Starting Chrome with Midscene extension...")
            self.chrome_process = subprocess.Popen(chrome_args)
            time.sleep(3)  # Give Chrome time to start

            logger.info("✅ Chrome launched successfully with Midscene extension")
            logger.info("🔧 Chrome debugging available on port 9222")
            logger.info("📱 You can now use the Midscene extension in Chrome")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to launch Chrome: {e}")
            return False

    def start_python_bridge_server(self):
        """Start the Python bridge server for integration"""
        logger.info("🌉 Starting Python-Chrome bridge server...")

        try:
            # Import and start the chrome bridge
            from integrations.midscene.chrome_bridge import initialize_chrome_bridge

            async def run_bridge():
                result = await initialize_chrome_bridge()
                if result["status"] == "success":
                    logger.info("✅ Chrome bridge server started successfully")
                    logger.info("🔗 Bridge available for automation tasks")
                else:
                    logger.error(f"❌ Failed to start bridge: {result.get('error')}")

            # Run the bridge initialization
            asyncio.create_task(run_bridge())

        except ImportError as e:
            logger.warning(f"⚠️ Could not import chrome bridge: {e}")
        except Exception as e:
            logger.error(f"❌ Failed to start bridge server: {e}")

    def open_extension_popup(self):
        """Open the extension popup in Chrome"""
        logger.info("🎯 Opening Midscene extension popup...")

        # The extension should be available in Chrome's extension bar
        # Users can click on it or access it via chrome://extensions/
        extension_url = "chrome://extensions/?id=midscene-extension"

        try:
            webbrowser.open(extension_url)
            logger.info("✅ Extension management page opened")
        except Exception as e:
            logger.warning(f"⚠️ Could not open extension page: {e}")

    def display_usage_instructions(self):
        """Display comprehensive usage instructions"""
        instructions = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🎯 MIDSCENE CHROME EXTENSION READY 🎯                     ║
╚══════════════════════════════════════════════════════════════════════════════╝

🚀 GETTING STARTED:

1. 📱 FIND THE EXTENSION:
   • Look for the Midscene icon in Chrome's extension toolbar
   • Click on the extension icon to open the popup interface
   • Or go to chrome://extensions/ to manage the extension

2. 🎮 USING THE EXTENSION:
   • Click the Midscene extension icon in any webpage
   • Use natural language to describe what you want to automate
   • Examples:
     - "Click on the login button"
     - "Fill out the contact form with my information"
     - "Navigate to the pricing page"
     - "Take a screenshot of this page"

3. 🤖 AI AUTOMATION FEATURES:
   • Natural language interaction with web elements
   • Intelligent element detection and clicking
   • Form filling with AI assistance
   • Page navigation and automation
   • Screenshot and data extraction capabilities

4. 🔧 INTEGRATION WITH YOUR SYSTEM:
   • Chrome DevTools debugging on port 9222
   • Python bridge server for advanced automation
   • Integration with your existing AI agent dashboard
   • OpenAI GPT-4o integration for intelligent automation

5. 📚 ADVANCED USAGE:
   • Use the Python bridge for complex automation workflows
   • Integrate with your existing agent systems
   • Create custom automation scripts using the Midscene API

╔══════════════════════════════════════════════════════════════════════════════╗
║                         🎯 TROUBLESHOOTING TIPS 🎯                          ║
╚══════════════════════════════════════════════════════════════════════════════╝

• If extension doesn't appear: Refresh Chrome or reload the extension
• For debugging: Open Chrome DevTools and check console for errors
• Extension permissions: Make sure to allow the extension on websites
• For advanced features: Ensure your OpenAI API key is configured

🌟 Happy Automating! The Midscene extension is now integrated with your system!
        """

        print(instructions)

    def run_integration_test(self):
        """Run a basic integration test"""
        logger.info("🧪 Running integration test...")

        try:
            # Test if Chrome DevTools API is accessible
            import aiohttp
            import asyncio

            async def test_chrome_connection():
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get("http://localhost:9222/json") as resp:
                            if resp.status == 200:
                                tabs = await resp.json()
                                logger.info(f"✅ Chrome DevTools accessible - {len(tabs)} tabs detected")
                                return True
                            else:
                                logger.warning("⚠️ Chrome DevTools not responding properly")
                                return False
                except Exception as e:
                    logger.warning(f"⚠️ Chrome DevTools connection test failed: {e}")
                    return False

            # Run the async test
            if asyncio.run(test_chrome_connection()):
                logger.info("✅ Integration test passed - System ready for automation")
            else:
                logger.warning("⚠️ Integration test inconclusive - Extension may still work")

        except ImportError:
            logger.warning("⚠️ aiohttp not available for testing - installing...")
            self.install_python_dependencies()

    async def launch_complete_system(self):
        """Launch the complete integrated system"""
        logger.info("🚀 Launching Complete Midscene Chrome Integration System...")

        # Step 1: Check prerequisites
        if not self.check_extension_built():
            logger.error("❌ Extension not properly built. Please run: cd midscene && pnpm install && pnpm run build")
            return False

        # Step 2: Install Python dependencies
        self.install_python_dependencies()

        # Step 3: Launch Chrome with extension
        if not self.launch_chrome_with_extension():
            return False

        # Step 4: Start bridge server
        self.start_python_bridge_server()

        # Step 5: Wait a moment for everything to initialize
        await asyncio.sleep(5)

        # Step 6: Run integration test
        self.run_integration_test()

        # Step 7: Display usage instructions
        self.display_usage_instructions()

        logger.info("🎉 Midscene Chrome Integration System launched successfully!")
        return True

    def cleanup(self):
        """Clean up processes and resources"""
        logger.info("🧹 Cleaning up system resources...")

        if self.chrome_process:
            try:
                self.chrome_process.terminate()
                self.chrome_process.wait(timeout=5)
                logger.info("✅ Chrome process terminated")
            except:
                try:
                    self.chrome_process.kill()
                    logger.info("✅ Chrome process killed")
                except:
                    logger.warning("⚠️ Could not terminate Chrome process")

def main():
    """Main launcher function"""
    launcher = MidsceneChromeSystemLauncher()

    try:
        # Run the complete system launch
        asyncio.run(launcher.launch_complete_system())

        # Keep the system running
        logger.info("🔄 System running... Press Ctrl+C to stop")
        while True:
            time.sleep(10)

    except KeyboardInterrupt:
        logger.info("🛑 Shutdown requested by user")
    except Exception as e:
        logger.error(f"❌ System error: {e}")
    finally:
        launcher.cleanup()
        logger.info("👋 Midscene Chrome Integration System stopped")

if __name__ == "__main__":
    main()
