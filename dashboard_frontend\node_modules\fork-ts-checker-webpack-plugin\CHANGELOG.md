## ⚠️ Next versions are available only on the [GitHub Releases](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/releases) page ⚠️

# [1.3.0-beta.1](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/compare/v1.2.0...v1.3.0-beta.1@beta) (2019-04-30)

### Bug Fixes

- **tests:** fix linter tests that were doing nothing ([d078278](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/d078278))
- **tests:** linter tests - useTypescriptIncrementalApi usage ([e0020d6](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/e0020d6))
- **tests:** rework vue integration tests ([5ad2568](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/5ad2568))

### Features

- **apiincrementalchecker:** improve generation of diagnostics ([ae80e5f](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/ae80e5f)), closes [#257](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/issues/257)

# [1.2.0](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/compare/v1.1.1...v1.2.0) (2019-04-22)

### Bug Fixes

- semantic-release update `CHANGELOG.md` on the git repo ([8ad58af](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/8ad58af))

### Features

- add semantic-release integration ([5fe0653](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/5fe0653))

# [1.2.0-beta.4](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/compare/v1.2.0-beta.3@beta...v1.2.0-beta.4@beta) (2019-04-23)

### Bug Fixes

- **tests:** fix linter tests that were doing nothing ([d078278](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/d078278))
- **tests:** linter tests - useTypescriptIncrementalApi usage ([e0020d6](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/e0020d6))

### Features

- **apiincrementalchecker:** improve generation of diagnostics ([ae80e5f](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/ae80e5f)), closes [#257](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/issues/257)

# [1.2.0-beta.3](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/compare/v1.2.0-beta.2@beta...v1.2.0-beta.3@beta) (2019-04-22)

### Bug Fixes

- **tests:** rework vue integration tests ([5ad2568](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/5ad2568))

# [1.2.0-beta.2](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/compare/v1.2.0-beta.1@beta...v1.2.0-beta.2@beta) (2019-04-22)

### Bug Fixes

- semantic-release update `CHANGELOG.md` on the git repo ([8ad58af](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/8ad58af))

# [1.2.0-beta.1](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/compare/v1.1.0...v1.2.0-beta.1@beta) (2019-04-22)

### Features

- add semantic-release integration ([5fe0653](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/commit/5fe0653))

## v1.1.1

- [Fix a regression w/ plugins like tsconfig-paths-webpack-plugin](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/260)

## v1.1.0

- [Add new custom resolution options](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/250)

## v1.0.4

- [gracefully handle error thrown from the service](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/249)

## v1.0.3

- [use worker-rpc library for inter-process communication](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/231)

## v1.0.2

- [Fix ignoreLintWarning mark warnings as errors](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/243)

## v1.0.1

- [Apply rounding to compilation time](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/235)

## v1.0.0

- [Going 1.0](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/218)

This is the first major version of `fork-ts-checker-webpack-plugin`. A long time coming :-)

There are actually no breaking changes that we're aware of; users of 0.x `fork-ts-checker-webpack-plugin` should be be able to upgrade without any drama. Users of TypeScript 3+ may notice a performance improvement as by default the plugin now uses the [incremental watch API](https://github.com/Microsoft/TypeScript/pull/20234) in TypeScript. Should this prove problematic you can opt out of using it by supplying `useTypescriptIncrementalApi: false`.

We are aware of an [issue with Vue and the incremental API](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/issues/219). We hope it will be fixed soon - a generous member of the community is taking a look. In the meantime, we will _not_ default to using the incremental watch API when in Vue mode.

The plugin supports webpack 2, 3, 4 and 5 alpha and TypeScript 2.1+ alongside tslint 4+.

See also: https://blog.johnnyreilly.com/2019/03/the-big-one-point-oh.html

## v1.0.0-alpha.10

- [Fix incremental api to work with TS 3+ by default](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/223)

## v1.0.0-alpha.9

- [Default to incremental api usage to true if TS 3+](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/217)

## v1.0.0-alpha.8

- [Respect tslint configs hierarchical order](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/214)

## v1.0.0-alpha.7

- [Add ignoreLintWarnings option](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/213)

## v1.0.0-alpha.6

- [don't directly depend upon typescript](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/208)

## v1.0.0-alpha.5

- [can now provide path where typescript can be found](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/204)

## v1.0.0-alpha.4

- [make node 6 compatible](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/202)

## v1.0.0-alpha.3

- [replace peerDeps with runtime checks](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/201)

## v1.0.0-alpha.2

- [Add `useTypescriptIncrementalApi`](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/198) (#196)

## v1.0.0-alpha.1

- [Use object-spread instead of `Object.assign`](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/194) (#194)

## v1.0.0-alpha.0

- [Add support for webpack 5](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/166)

### Breaking Changes

Version `1.x` additionally supports webpack 5 alongside webpack 4, whose hooks are now tapped differently:

```diff
-  compiler.hooks.forkTsCheckerDone.tap(...args)
+  const forkTsCheckerHooks = ForkTsCheckerWebpackPlugin.getCompilerHooks(compiler)
+  forkTsCheckerHooks.done.tap(...args)
```

v1.0.0-alpha.0 drops support for node 6.

## v0.5.2

- [Fix erroneous error on diagnostics at 0 line; remove deprecated fs.existsSync](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/190) (#190)

## v0.5.1

- [Make the checker compile with TypeScript 3.2](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/189)

## 0.5.0

- Removed unused dependency `resolve`.
- Replace `lodash` usage with native calls.
- ** Breaking Changes**:
  - Removed all getters from `NormalizedMessage`, use direct property access instead.
- **Internal**:
  - Test against ts-loader v5
  - Enable all strict type checks
  - Update dev dependencies

## v0.4.15

- [Add `tslintAutoFix` option to be passed on to tslint to auto format typescript files](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/174) (#174)

## v0.4.14

- [Add support for `reportFiles` option](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/179) (#179)

## v0.4.13

- [Merge in `compilerOptions` prior to calling `parseJsonConfigFileContent`](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/176) (#176)

## v0.4.12

- [Add `compilerOptions` option](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/173) (#173)

## v0.4.11

- [Fix os.cpus is not a function](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/172) (#172)

## v0.4.10

- [Allow fork-ts-checker-webpack-plugin to be imported in .ts files using ESM import syntax](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/163) (#163)

## v0.4.9

- [Set "compilationDone" before resolving "forkTsCheckerServiceBeforeStart"](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/146) (#146)

## v0.4.8

- [Fix(types collision): update webpack](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/151) (#142)

## v0.4.7

- [Fix(types collision): update chalk and chokidar](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/147) (#142)
- [Fix(logger): Don't limit Options.logger to Console type](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/143)

## v0.4.6

- [Fix(types): Make options Partial<Options>](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/141) (#140)

## v0.4.5

- [Fix(types): Add types to the plugin](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/139) (#137)
- [Fix(vue): Avoid false positive of no-consecutive-blank-lines TSLint rule in Vue file](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/138) (#130)

## v0.4.4

- [Fix(vue): resolve src attribute on the script block on Vue files](https://github.com/TypeStrong/fork-ts-checker-webpack-plugin/pull/130) (#111, #85)
- Add TypeScript ^3.0.0 to peerDependencies

## v0.4.3

- Fix "File system lag can cause Invalid source file errors to slip through" (#127)

## v0.4.2

- Format messages when `async` is false

## v0.4.1

- Fix webpack 4 hooks bug

## v0.4.0

- Support webpack 4

## v0.3.0

- Add `vue` support

## v0.2.10

- Fix #80 "Cannot read property 'getLineAndCharacterOfPosition' of undefined"
- Fix #76 "TypeError: Cannot read property '0' of undefined"

## v0.2.9

- Make errors formatting closer to `ts-loader` style
- Handle tslint exclude option

## v0.2.8

- Add `checkSyntacticErrors` option
- Fix `process.env` pass to the child process
- Add `fork-ts-checker-service-before-start` hook

## v0.2.7

- Fix service is not killed when webpack watch is done

## v0.2.6

- Add diagnostics/lints formatters - `formatter` and `formatterOptions` option

## v0.2.5

- Add `async` option - more information in `README.md`

## v0.2.4

- Fix `ESLint: "fork-ts-checker-webpack-plugin" is not published.` issue

## v0.2.3

- Add support for webpack 3 as peerDependency

## v0.2.2

- Force `isolatedModule: false` in checker compiler for better performance

## v0.2.1

- Fix for `tslint: true` option issue

## v0.2.0

- tsconfig.json and tslint.json path are not printed anymore.
- `watch` option is not used on 'build' mode
- Handle case with no options object (`new ForkTsCheckerWebpacPlugin()`)
- Basic integration tests (along units)
- **Breaking changes**:
  - tslint is not enabled by default - you have to set `tslint: true` or `tslint: './path/to/tslint.json'` to enable it.
  - `blockEmit` option is removed - it choose automatically - blocks always on 'build' mode, never on 'watch' mode.

## v0.1.5

- Disable tslint if module is not installed and no tslint path is passed
- Improve README.md

## v0.1.4

- Fix send to closed channel case
- Fix removed files case
- Add `fork-ts-checker-service-start-error` hook

## v0.1.3

- Fix "Cannot read property 'mtime' of undefined on OSX"

## v0.1.2

- Workers mode works correctly (fixed typo)

## v0.1.1

- Support memory limit in multi-process mode
- Handle already closed channel case on sending ipc message

## v0.1.0

- Initial release - not production ready.
