/* Dashboard styles */
body {
    padding-top: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Browser view */
.browser-view {
    border: 1px solid #ccc;
    border-radius: 4px;
    height: 600px;
    background-color: #f5f5f5;
    position: relative;
    overflow: hidden;
}

.browser-view img {
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: 0 auto;
}

/* Agent status */
.agent-status {
    border-left: 4px solid;
    padding-left: 10px;
    margin-bottom: 15px;
}

.agent-status.active {
    border-color: #198754;
}

.agent-status.idle {
    border-color: #6c757d;
}

.agent-status.busy {
    border-color: #fd7e14;
}

.agent-status.error {
    border-color: #dc3545;
}

/* Log window */
.log-window {
    height: 200px;
    overflow-y: auto;
    background-color: #212529;
    color: #f8f9fa;
    padding: 10px;
    font-family: 'Courier New', Courier, monospace;
    border-radius: 4px;
}

.log-entry {
    margin-bottom: 5px;
}

.log-time {
    color: #6c757d;
}

.log-level-info {
    color: #0dcaf0;
}

.log-level-warning {
    color: #ffc107;
}

.log-level-error {
    color: #dc3545;
}

/* Cards */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
}

/* Buttons */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .browser-view {
        height: 400px;
    }
}
