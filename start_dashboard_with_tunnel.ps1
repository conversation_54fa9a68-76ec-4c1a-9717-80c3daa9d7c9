# PowerShell script to start the Multi-Agent AI System with Cloudflare tunneling
# This script activates the .venvAI virtual environment and runs the dashboard launcher

# Change to the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Push-Location $scriptDir

# Check if .venvAI exists
if (-not (Test-Path ".\.venvAI\Scripts\activate.ps1")) {
    Write-Host "Virtual environment .venvAI not found. Please set up your environment first." -ForegroundColor Red
    exit 1
}

# Activate the virtual environment
Write-Host "Activating .venvAI virtual environment..." -ForegroundColor Blue
& ".\.venvAI\Scripts\activate.ps1"

# Run the dashboard launcher with tunnel
Write-Host "Starting Multi-Agent AI System with Cloudflare tunneling..." -ForegroundColor Green
python dashboard_launcher_with_tunnel.py

# Deactivate the virtual environment when done (will only execute if the Python script exits normally)
deactivate

# Restore the previous location
Pop-Location
