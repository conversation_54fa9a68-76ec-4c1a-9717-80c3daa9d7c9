{"name": "http-proxy-middleware", "version": "2.0.9", "description": "The one-liner node.js proxy middleware for connect, express and browser-sync", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "rm -rf dist && rm -rf coverage", "lint": "yarn prettier && yarn eslint", "lint:fix": "yarn prettier:fix && yarn eslint:fix", "eslint": "eslint '{src,test}/**/*.ts'", "eslint:fix": "yarn eslint --fix", "prettier": "prettier --list-different \"**/*.{js,ts,md,yml,json,html}\"", "prettier:fix": "prettier --write \"**/*.{js,ts,md,yml,json,html}\"", "prebuild": "yarn clean", "build": "tsc", "pretest": "yarn build", "test": "jest", "precoverage": "yarn build", "coverage": "jest --coverage --coverageReporters=lcov", "prepare": "husky install", "prepack": "yarn build && rm dist/tsconfig.tsbuildinfo", "spellcheck": "npx --yes cspell --show-context --show-suggestions '**/*.*'"}, "repository": {"type": "git", "url": "https://github.com/chimurai/http-proxy-middleware.git"}, "keywords": ["reverse", "proxy", "middleware", "http", "https", "connect", "express", "fastify", "polka", "browser-sync", "gulp", "grunt-contrib-connect", "websocket", "ws", "cors"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/chimurai/http-proxy-middleware/issues"}, "homepage": "https://github.com/chimurai/http-proxy-middleware#readme", "devDependencies": {"@commitlint/cli": "16.2.1", "@commitlint/config-conventional": "16.2.1", "@types/express": "4.17.13", "@types/is-glob": "4.0.2", "@types/jest": "27.4.0", "@types/micromatch": "4.0.2", "@types/node": "17.0.18", "@types/supertest": "2.0.11", "@types/ws": "8.2.2", "@typescript-eslint/eslint-plugin": "5.12.0", "@typescript-eslint/parser": "5.12.0", "body-parser": "1.19.2", "browser-sync": "2.27.7", "connect": "3.7.0", "eslint": "8.9.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "express": "4.17.3", "get-port": "5.1.1", "husky": "7.0.4", "jest": "27.5.1", "lint-staged": "12.3.4", "mockttp": "2.6.0", "open": "8.4.0", "prettier": "2.5.1", "supertest": "6.2.2", "ts-jest": "27.1.3", "typescript": "4.5.5", "ws": "8.5.0"}, "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}, "engines": {"node": ">=12.0.0"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}