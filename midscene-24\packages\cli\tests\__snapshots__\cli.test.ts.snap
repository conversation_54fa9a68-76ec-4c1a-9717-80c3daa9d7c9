// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`cli parse args > all args 1`] = `
{
  "env": {
    "headed": false,
    "serve": false,
    "url": "https://www.baidu.com/",
    "userAgent": "Cli",
    "viewportHeight": 768,
    "viewportScale": 2,
    "viewportWidth": 1024,
  },
  "flow": [],
}
`;

exports[`cli parse args > basic 1`] = `
{
  "env": {
    "headed": false,
    "serve": false,
    "url": "https://www.baidu.com/",
    "userAgent": false,
    "viewportHeight": false,
    "viewportScale": false,
    "viewportWidth": false,
  },
  "flow": [],
}
`;

exports[`cli utils > match exact file 1`] = `
[
  "tests/midscene_scripts/foo.yml",
]
`;

exports[`cli utils > match files 1`] = `
[
  "tests/midscene_scripts/foo.yml",
]
`;

exports[`cli utils > match folder 1`] = `
[
  "tests/midscene_scripts/foo.yml",
  "tests/midscene_scripts/sub/bar.yaml",
]
`;
