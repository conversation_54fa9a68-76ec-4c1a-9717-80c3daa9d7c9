# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# OS X temporary files
.DS_Store

# Rush temporary files
common/deploy/
common/temp/
common/autoinstallers/*/.npmrc
**/.rush/temp/

# Heft
.heft

# PIA temp files
.pia

# Build assets
dist
packages/speedy-plugin-pia/lib

# Rush files
common/changes/
common/scripts/
common/config/
common/_templates/
CHANGELOG.*

# Emo files
.eden-mono


# Package manager files
pnpm-lock.yaml
yarn.lock
package-lock.json
shrinkwrap.json
.pnpm-store/

# Build outputs
dist
lib

# Prettier reformats code blocks inside Markdown, which affects rendered output
*.md

vendors/**/index.d.ts
vendors/**/index.js

tests/configs/src/errors/**