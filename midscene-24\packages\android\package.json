{"name": "@midscene/android", "version": "0.17.2", "description": "Android automation library for Midscene", "keywords": ["Android UI automation", "Android AI testing", "Android automation library", "Android automation tool", "Android use"], "main": "./dist/lib/index.js", "types": "./dist/types/index.d.ts", "files": ["bin", "dist", "README.md"], "exports": {".": {"types": "./dist/types/index.d.ts", "default": "./dist/lib/index.js"}}, "scripts": {"dev": "modern dev", "build": "modern build -c ./modern.config.ts", "build:watch": "modern build -w -c ./modern.config.ts", "test": "vitest --run", "test:u": "vitest --run -u", "test:ai": "MIDSCENE_CACHE=true npm run test"}, "dependencies": {"@midscene/core": "workspace:*", "@midscene/shared": "workspace:*", "@midscene/web": "workspace:*", "appium-adb": "12.12.1"}, "devDependencies": {"@modern-js/module-tools": "2.60.6", "@types/node": "^18.0.0", "dotenv": "16.4.5", "typescript": "^5.8.2", "vitest": "3.0.5"}, "license": "MIT"}