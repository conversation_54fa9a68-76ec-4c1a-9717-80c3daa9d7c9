@import './components/common.less';

@layout-space: 22px;

html,
body {
  padding: 0;
  margin: 0;
  // font-size: 3.3rem;
  line-height: 1;
}



.rspress-nav {
  transition: .2s;
}

// local debug

:root {
  --modern-sidebar-width: 0 !important;
  --modern-aside-width: 0 !important;
  --modern-preview-padding:0 !important;
}

.modern-doc-layout,
.modern-doc {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100vh;
}

.modern-sidebar,
header.w-full {
  display: none !important;
}
.modern-doc-container{
  padding: 0 !important;
}

footer.mt-8{
  display: none;
}
// ----------

.page-container {

  blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
    margin: 0;
  }

  display: flex;
  flex-direction: column;
  height: 100%;
  color: @main-text;
  font-family: -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji";
  font-size: 14px;
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
  font-synthesis: style;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  line-height: 1.5;
}

.page-side {
  border-right: 1px solid @border-color;
  height: 100%;
}

.page-nav {
  height: 30px;
  padding: 10px;
  border-bottom: 1px solid @border-color;
  display: flex;
  flex-direction: row;
  background: @side-bg;

  .page-nav-left {
    display: flex;
    flex-direction: row;
  }

  .page-nav-toolbar {
    margin-left: 20px;

    .ant-btn {
      background: @toolbar-btn-bg;
    }
  }

  .playwright-case-selector {
    margin-left: 16px;
    line-height: 30px;
  }
}

.cost-str {
  color: @weak-text;
}

.ant-layout {
  flex-grow: 1;
  height: 100%;
}

.main-right {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // padding: @layout-space;

  .replay-all-mode-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: @layout-space;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .main-content{
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    overflow: hidden;
    background: #FFF;
  }
  
  &.uploader-wrapper {
    box-sizing: border-box;
    margin: auto;
    max-width: 800px;
    flex-direction: column;
    justify-content: center;

    .uploader {
      width: 100%;
    }

    .demo-loader {
      width: 100%;
      text-align: center;
      margin-top: 10px;
    }
  }
  
  .main-content-container {
    flex-grow: 1;
    height: 100%;
    background: #ffffff;
    overflow: hidden;
    border-right: 1px solid @border-color;
  }
    
  .main-side {
    box-sizing: border-box;
    overflow-y: scroll;
    height: 100%;
  }

  .json-content {
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}
