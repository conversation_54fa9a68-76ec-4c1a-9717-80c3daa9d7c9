"""
Prompt template utilities for LangChain-based agents and chains.
"""
import os
from typing import Dict, List, Any, Optional, Union
import json

from langchain.prompts import (
    PromptTemplate,
    FewShotPromptTemplate,
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate
)


def load_prompt_templates(template_dir: str) -> Dict[str, PromptTemplate]:
    """
    Load multiple prompt templates from a directory.

    Args:
        template_dir: Directory containing prompt template files (.txt or .json)

    Returns:
        Dictionary of prompt templates by name
    """
    templates = {}
    template_dir = os.path.abspath(template_dir)

    if not os.path.exists(template_dir):
        raise FileNotFoundError(f"Template directory not found: {template_dir}")

    for filename in os.listdir(template_dir):
        if not (filename.endswith('.txt') or filename.endswith('.json')):
            continue

        template_name = os.path.splitext(filename)[0]
        file_path = os.path.join(template_dir, filename)

        if filename.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as f:
                template_str = f.read()

            # Try to detect input variables with {variable} syntax
            import re
            input_variables = re.findall(r'{([^{}]*)}', template_str)

            templates[template_name] = PromptTemplate(
                template=template_str,
                input_variables=input_variables
            )

        elif filename.endswith('.json'):
            with open(file_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)

            if 'type' not in template_data or 'template' not in template_data:
                continue

            if template_data['type'] == 'prompt':
                templates[template_name] = PromptTemplate(
                    template=template_data['template'],
                    input_variables=template_data.get('input_variables', [])
                )

            elif template_data['type'] == 'chat':
                messages = []

                for msg in template_data.get('messages', []):
                    if msg['role'] == 'system':
                        messages.append(SystemMessagePromptTemplate.from_template(msg['content']))
                    elif msg['role'] == 'human':
                        messages.append(HumanMessagePromptTemplate.from_template(msg['content']))

                templates[template_name] = ChatPromptTemplate.from_messages(messages)

            elif template_data['type'] == 'few_shot':
                examples = template_data.get('examples', [])
                example_prompt = PromptTemplate(
                    template=template_data.get('example_template', ''),
                    input_variables=template_data.get('example_variables', [])
                )

                templates[template_name] = FewShotPromptTemplate(
                    examples=examples,
                    example_prompt=example_prompt,
                    prefix=template_data.get('prefix', ''),
                    suffix=template_data.get('suffix', ''),
                    input_variables=template_data.get('input_variables', [])
                )

    return templates


def custom_prompt_template(template_type: str, **kwargs) -> Union[PromptTemplate, ChatPromptTemplate]:
    """
    Create a customized prompt template.

    Args:
        template_type: Type of template ('standard', 'chat', or 'few_shot')
        **kwargs: Arguments specific to the template type

    Returns:
        A prompt template instance
    """
    if template_type == 'standard':
        return PromptTemplate(
            template=kwargs.get('template', ''),
            input_variables=kwargs.get('input_variables', [])
        )

    elif template_type == 'chat':
        messages = []

        system_template = kwargs.get('system_template')
        if system_template:
            messages.append(SystemMessagePromptTemplate.from_template(system_template))

        human_template = kwargs.get('human_template')
        if human_template:
            messages.append(HumanMessagePromptTemplate.from_template(human_template))

        return ChatPromptTemplate.from_messages(messages)

    elif template_type == 'few_shot':
        examples = kwargs.get('examples', [])
        example_prompt = PromptTemplate(
            template=kwargs.get('example_template', ''),
            input_variables=kwargs.get('example_variables', [])
        )

        return FewShotPromptTemplate(
            examples=examples,
            example_prompt=example_prompt,
            prefix=kwargs.get('prefix', ''),
            suffix=kwargs.get('suffix', ''),
            input_variables=kwargs.get('input_variables', [])
        )

    else:
        raise ValueError(f"Unknown template type: {template_type}")
