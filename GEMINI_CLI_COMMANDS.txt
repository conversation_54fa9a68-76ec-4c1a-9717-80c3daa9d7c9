🚀 GEMINI CLI - COPY & PASTE COMMANDS

=== OPTION 1: Use the batch file (RECOMMENDED) ===
run_gemini_direct.bat

=== OPTION 2: Manual PowerShell commands ===
$env:GEMINI_API_KEY="AIzaSyBlM3-NO7PISCYYg6GWyrfUhaQr6R3dhhk"
$env:GEMINI_MODEL="gemini-2.0-flash-exp"
$env:GEMINI_SANDBOX="false"
$env:NODE_OPTIONS="--max-old-space-size=8192"
cd "C:\Users\<USER>\Documents\augment-projects\Ai Agent System\gemini-cli"
node bundle\gemini.js

=== OPTION 3: One-line PowerShell command ===
$env:GEMINI_API_KEY="AIzaSyBlM3-NO7PISCYYg6GWyrfUhaQr6R3dhhk"; $env:GEMINI_MODEL="gemini-2.0-flash-exp"; $env:GEMINI_SANDBOX="false"; cd "C:\Users\<USER>\Documents\augment-projects\Ai Agent System\gemini-cli"; node bundle\gemini.js

=== OPTION 4: CMD commands ===
set GEMINI_API_KEY=AIzaSyBlM3-NO7PISCYYg6GWyrfUhaQr6R3dhhk
set GEMINI_MODEL=gemini-2.0-flash-exp
set GEMINI_SANDBOX=false
cd "C:\Users\<USER>\Documents\augment-projects\Ai Agent System\gemini-cli"
node bundle\gemini.js

✅ All of these should work! The bundle is already built and ready to use.
