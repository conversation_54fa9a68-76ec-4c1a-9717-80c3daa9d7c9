#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/packages/web-integration/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/packages/web-integration/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/packages/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/node_modules:/mnt/c/Users/<USER>/Documents/node_modules:/mnt/c/Users/<USER>/node_modules:/mnt/c/Users/<USER>/mnt/c/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/packages/web-integration/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/packages/web-integration/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/packages/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/node_modules:/mnt/c/Users/<USER>/Documents/node_modules:/mnt/c/Users/<USER>/node_modules:/mnt/c/Users/<USER>/mnt/c/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@midscene/web/bin/midscene-playground" "$@"
else
  exec node  "$basedir/../@midscene/web/bin/midscene-playground" "$@"
fi
