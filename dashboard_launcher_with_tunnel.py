"""
Launcher script for the Multi-Agent AI System with Cloudflare tunneling
"""
import asyncio
import logging
from datetime import datetime
import os
import sys
import subprocess
import signal

# Add the current directory to the Python path to ensure all modules are accessible
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_manager import AgentManager
from orchestrator import Orchestrator
from web_interface import start_web_interface

# Set up logging
logger = setup_logger("dashboard_launcher")

# Global process handle for the tunnel
tunnel_process = None

async def start_tunnel(host, port):
    """Start the Cloudflare tunnel in a separate process"""
    global tunnel_process

    logger.info("Starting Cloudflare tunnel...")

    # Use the Python executable from the virtual environment
    python_exe = sys.executable

    # Start the tunnel process
    tunnel_process = subprocess.Popen(
        [python_exe, "start_tunnel.py"],
        # Pass the current environment variables
        env=os.environ.copy()
    )

    logger.info("Tunnel process started")

async def main():
    """
    Main function to start the system with tunneling.
    """
    logger.info(f"Starting {config.SYSTEM_NAME} v{config.VERSION} with Cloudflare tunneling")

    # Initialize the state manager
    state_manager = StateManager()

    # Initialize system state
    await state_manager.set_state("system", {
        "start_time": datetime.now().isoformat(),
        "version": config.VERSION
    })

    # Initialize the agent manager
    agent_manager = AgentManager(state_manager)

    # Initialize the orchestrator
    orchestrator = Orchestrator(state_manager)

    # Register the orchestrator with the agent manager
    agent_manager.register_agent("orchestrator", orchestrator)

    # Set human-in-the-loop callback
    orchestrator.set_hitl_callback(agent_manager.handle_hitl_event)

    # Start the web interface
    web_host = os.getenv("WEB_HOST", "127.0.0.1")
    web_port = int(os.getenv("WEB_PORT", "8000"))

    logger.info(f"Starting web interface on {web_host}:{web_port}")

    # Create a task for starting the tunnel
    tunnel_task = asyncio.create_task(start_tunnel(web_host, web_port))

    # Wait a moment for the tunnel to initialize
    await asyncio.sleep(2)

    # Create a task for the web interface
    web_task = asyncio.create_task(
        start_web_interface(
            agent_manager=agent_manager,
            state_manager=state_manager,
            host=web_host,
            port=web_port,
            debug=config.DEBUG
        )
    )

    try:
        # Keep the system running
        await asyncio.gather(web_task, tunnel_task)
    except KeyboardInterrupt:
        logger.info("Received shutdown signal. Shutting down gracefully...")
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
    finally:
        # Clean up resources
        if tunnel_process is not None:
            logger.info("Shutting down tunnel process...")
            tunnel_process.send_signal(signal.SIGINT)
            tunnel_process.wait(timeout=5)
        logger.info("Shutting down...")

if __name__ == "__main__":
    asyncio.run(main())
