{"editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "cSpell.words": ["AITEST", "<PERSON><PERSON>", "aweme", "bbox", "bytedance", "deepseek", "do<PERSON>o", "do<PERSON><PERSON>", "fkill", "httpbin", "iconfont", "modelcontextprotocol", "openrouter", "qwen", "<PERSON><PERSON><PERSON>", "targetcreated", "Volcengine", "xpaths", "<PERSON><PERSON><PERSON>"], "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[plaintext]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}