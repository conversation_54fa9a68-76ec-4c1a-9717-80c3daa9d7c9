#!/usr/bin/env python3
"""
Modern Dark AI Agent System Central Hub
Complete control center for all AI agents and services
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import subprocess
import time
import json
from pathlib import Path
import psutil
import webbrowser
from datetime import datetime

class ModernDarkTheme:
    """Modern dark theme configuration"""
    # Dark color scheme
    BG_DARK = "#1e1e1e"
    BG_DARKER = "#141414"
    BG_PANEL = "#2d2d30"
    BG_HOVER = "#3e3e42"
    FG_PRIMARY = "#ffffff"
    FG_SECONDARY = "#b3b3b3"
    ACCENT_BLUE = "#0078d4"
    ACCENT_GREEN = "#16c60c"
    ACCENT_RED = "#e74c3c"
    ACCENT_ORANGE = "#ff8c00"
    ACCENT_PURPLE = "#8b5cf6"
    BORDER = "#3e3e42"

class ModernAIAgentHub:
    """
    Modern AI Agent System Central Hub
    Complete control center with dark theme and comprehensive features
    """

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_theme()

        # System state
        self.processes = {}
        self.system_stats = {}        # All AI agents and services - all set to active running state
        self.agents = {
            "JARVIS": {
                "script": "start_jarvis_with_alphaevolve.py",
                "description": "Main AI Assistant with AlphaEvolve",
                "status": "Running",
                "port": None,
                "color": "#0078d4"
            },
            "IRIS": {
                "script": "start_iris_assistant.py",
                "description": "Advanced AI Vision Assistant",
                "status": "Running",
                "port": None,
                "color": "#8b5cf6"
            },
            "UI-TARS": {
                "script": "ui_tars/main.py",
                "description": "UI Automation & Browser Control",
                "status": "Running",
                "port": 8080,
                "color": "#16c60c"
            },
            "AlphaEvolve": {
                "script": "alpha_evolve_monitor.py",
                "description": "Self-Evolving AI System",
                "status": "Running",
                "port": None,
                "color": "#ff8c00"
            },
            "Borg Cluster": {
                "script": "run_borg_cluster.py",
                "description": "Distributed AI Processing",
                "status": "Running",
                "port": None,
                "color": "#e74c3c"
            },
            "Web Interface": {
                "script": "run_web_interface.py",
                "description": "Web-based Control Panel",
                "status": "Running",
                "port": 8000,
                "color": "#00bcd4"
            },
            "System Monitor": {
                "script": "monitor.py",
                "description": "Real-time System Monitoring",
                "status": "Running",
                "port": None,
                "color": "#4caf50"
            },
            "Email Agent": {
                "script": "agent_email_demo.py",
                "description": "Automated Email Processing",
                "status": "Running",
                "port": None,
                "color": "#ff5722"
            },
            "Gmail Integration": {
                "script": "agent_gmail_integration.py",
                "description": "Gmail Automation System",
                "status": "Running",
                "port": None,
                "color": "#f44336"
            },
            "Browser Automation": {
                "script": "browser_automation.py",
                "description": "Web Browser Control",
                "status": "Running",
                "port": None,
                "color": "#9c27b0"
            },
            "WebRover": {
                "script": "WebRover/backend/start.py",
                "description": "Advanced Web Navigation & Automation",
                "status": "Running",
                "port": 5000,
                "color": "#3498db"
            },
            "Advanced Memory": {
                "script": "core/advanced_memory.py",
                "description": "AI Memory & Data Storage System",
                "status": "Running",
                "port": None,
                "color": "#2ecc71"
            },
            "Voice Assistant": {
                "script": "services/voice_calling_service.py",
                "description": "Voice Recognition & Speech Synthesis",
                "status": "Running",
                "port": None,
                "color": "#e67e22"
            },
            "Mobile Camera": {
                "script": "services/mobile_camera_qr.py",
                "description": "Mobile Phone as Camera via QR Code",
                "status": "Running",
                "port": 8888,
                "color": "#1abc9c"
            },
            "Gemma 3 Vision": {
                "script": "llms/gemma_vision.py",
                "description": "Google's Gemma 3 Multimodal LLM",
                "status": "Running",
                "port": 8501,
                "color": "#4a69bd"
            },
            "Llama 3.2 Vision": {
                "script": "llms/llama_vision.py",
                "description": "Meta's Llama 3.2 Vision Model",
                "status": "Running",
                "port": 8502,
                "color": "#6a89cc"
            },
            "Qwen 2.5 VL": {
                "script": "llms/qwen_vision.py",
                "description": "Alibaba's Qwen 2.5 Vision-Language Model",
                "status": "Running",
                "port": 8503,
                "color": "#82ccdd"
            },
            "Phi-4 Multimodal": {
                "script": "llms/phi4_multimodal.py",
                "description": "Microsoft's Phi-4 Multimodal Model",
                "status": "Running",
                "port": 8504,
                "color": "#60a3bc"
            },
            "Local LLM Manager": {
                "script": "llms/ollama_manager.py",
                "description": "Manages Local LLMs via Ollama",
                "status": "Running",
                "port": 11434,
                "color": "#3c6382"
            }
        }

        self.setup_interface()
        self.start_monitoring()

    def setup_window(self):
        """Setup main window"""
        self.root.title("🚀 AI Agent System - Central Command Hub")
        self.root.geometry("1600x1000")
        self.root.minsize(1400, 900)
        self.root.configure(bg=ModernDarkTheme.BG_DARK)

        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (1000 // 2)
        self.root.geometry(f"1600x1000+{x}+{y}")

        # Make window prominent
        self.root.lift()
        self.root.focus_force()
        self.root.attributes('-topmost', True)
        self.root.after(1000, lambda: self.root.attributes('-topmost', False))

    def setup_theme(self):
        """Setup modern dark theme"""
        style = ttk.Style()
        style.theme_use('clam')

        # Configure dark theme
        style.configure('Dark.TFrame', background=ModernDarkTheme.BG_DARK)
        style.configure('Panel.TFrame', background=ModernDarkTheme.BG_PANEL, relief='flat', borderwidth=1)
        style.configure('Dark.TLabel', background=ModernDarkTheme.BG_DARK, foreground=ModernDarkTheme.FG_PRIMARY)
        style.configure('Title.TLabel', background=ModernDarkTheme.BG_DARK, foreground=ModernDarkTheme.ACCENT_BLUE, font=('Segoe UI', 24, 'bold'))
        style.configure('Header.TLabel', background=ModernDarkTheme.BG_PANEL, foreground=ModernDarkTheme.FG_PRIMARY, font=('Segoe UI', 12, 'bold'))
        style.configure('Status.TLabel', background=ModernDarkTheme.BG_PANEL, foreground=ModernDarkTheme.FG_SECONDARY, font=('Segoe UI', 10))

        # Button styles
        style.configure('Accent.TButton', background=ModernDarkTheme.ACCENT_BLUE, foreground='white', font=('Segoe UI', 10, 'bold'))
        style.configure('Success.TButton', background=ModernDarkTheme.ACCENT_GREEN, foreground='white', font=('Segoe UI', 10, 'bold'))
        style.configure('Danger.TButton', background=ModernDarkTheme.ACCENT_RED, foreground='white', font=('Segoe UI', 10, 'bold'))
        style.configure('Warning.TButton', background=ModernDarkTheme.ACCENT_ORANGE, foreground='white', font=('Segoe UI', 10, 'bold'))

        # Notebook style
        style.configure('Dark.TNotebook', background=ModernDarkTheme.BG_DARK, borderwidth=0)
        style.configure('Dark.TNotebook.Tab', background=ModernDarkTheme.BG_PANEL, foreground=ModernDarkTheme.FG_PRIMARY, padding=[20, 10])

    def setup_interface(self):
        """Setup the main interface"""
        # Main container
        main_container = ttk.Frame(self.root, style='Dark.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title bar
        self.setup_title_bar(main_container)

        # Main content area
        content_frame = ttk.Frame(main_container, style='Dark.TFrame')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # Left panel - Agent controls
        left_panel = ttk.Frame(content_frame, style='Panel.TFrame')
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # Right panel - System info and controls
        right_panel = ttk.Frame(content_frame, style='Panel.TFrame')
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_panel.configure(width=400)

        # Setup panels
        self.setup_agent_panel(left_panel)
        self.setup_system_panel(right_panel)

        # Bottom status bar
        self.setup_status_bar(main_container)

    def setup_title_bar(self, parent):
        """Setup title bar with controls"""
        title_frame = ttk.Frame(parent, style='Dark.TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # Main title
        title_label = ttk.Label(title_frame, text="🚀 AI AGENT SYSTEM", style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        subtitle_label = ttk.Label(title_frame, text="Central Command Hub",
                                  background=ModernDarkTheme.BG_DARK,
                                  foreground=ModernDarkTheme.FG_SECONDARY,
                                  font=('Segoe UI', 12))
        subtitle_label.pack(side=tk.LEFT, padx=(20, 0))

        # Global controls
        controls_frame = ttk.Frame(title_frame, style='Dark.TFrame')
        controls_frame.pack(side=tk.RIGHT)

        ttk.Button(controls_frame, text="🚀 START ALL", style='Success.TButton',
                  command=self.start_all_agents).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="⛔ STOP ALL", style='Danger.TButton',
                  command=self.stop_all_agents).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="🔄 REFRESH", style='Accent.TButton',
                  command=self.refresh_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="🌐 CLOUD", style='Warning.TButton',
                  command=self.open_cloud_services).pack(side=tk.LEFT, padx=5)

    def setup_agent_panel(self, parent):
        """Setup agent control panel"""
        # Panel header
        header_frame = ttk.Frame(parent, style='Panel.TFrame')
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        ttk.Label(header_frame, text="🤖 AI AGENTS & SERVICES", style='Header.TLabel').pack(side=tk.LEFT)

        # Agent grid
        self.agent_grid_frame = ttk.Frame(parent, style='Panel.TFrame')
        self.agent_grid_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        self.create_agent_cards()

    def create_agent_cards(self):
        """Create modern agent control cards"""
        # Clear existing cards
        for widget in self.agent_grid_frame.winfo_children():
            widget.destroy()

        row = 0
        col = 0
        max_cols = 3

        for agent_name, agent_info in self.agents.items():
            # Create card frame
            card = tk.Frame(self.agent_grid_frame,
                           bg=ModernDarkTheme.BG_DARKER,
                           relief='flat',
                           borderwidth=2,
                           highlightbackground=agent_info['color'],
                           highlightthickness=2)
            card.grid(row=row, column=col, padx=10, pady=10, sticky='nsew', ipadx=15, ipady=15)

            # Configure grid weights
            self.agent_grid_frame.grid_rowconfigure(row, weight=1)
            self.agent_grid_frame.grid_columnconfigure(col, weight=1)

            # Agent header
            header_frame = tk.Frame(card, bg=ModernDarkTheme.BG_DARKER)
            header_frame.pack(fill=tk.X, pady=(0, 10))

            # Status indicator
            status_color = ModernDarkTheme.ACCENT_GREEN if agent_info['status'] == 'Running' else ModernDarkTheme.ACCENT_RED
            status_dot = tk.Label(header_frame, text="●", fg=status_color, bg=ModernDarkTheme.BG_DARKER, font=('Segoe UI', 16))
            status_dot.pack(side=tk.LEFT)

            # Agent name
            name_label = tk.Label(header_frame, text=agent_name,
                                 fg=agent_info['color'], bg=ModernDarkTheme.BG_DARKER,
                                 font=('Segoe UI', 14, 'bold'))
            name_label.pack(side=tk.LEFT, padx=(10, 0))

            # Description
            desc_label = tk.Label(card, text=agent_info['description'],
                                 fg=ModernDarkTheme.FG_SECONDARY, bg=ModernDarkTheme.BG_DARKER,
                                 font=('Segoe UI', 10), wraplength=200)
            desc_label.pack(pady=(0, 10))

            # Port info if available
            if agent_info['port']:
                port_label = tk.Label(card, text=f"Port: {agent_info['port']}",
                                     fg=ModernDarkTheme.FG_SECONDARY, bg=ModernDarkTheme.BG_DARKER,
                                     font=('Segoe UI', 9))
                port_label.pack()

            # Control buttons
            button_frame = tk.Frame(card, bg=ModernDarkTheme.BG_DARKER)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # Start/Stop button
            if agent_info['status'] == 'Running':
                control_btn = tk.Button(button_frame, text="STOP",
                                       bg=ModernDarkTheme.ACCENT_RED, fg='white',
                                       font=('Segoe UI', 9, 'bold'), relief='flat',
                                       command=lambda name=agent_name: self.stop_agent(name))
            else:
                control_btn = tk.Button(button_frame, text="START",
                                       bg=ModernDarkTheme.ACCENT_GREEN, fg='white',
                                       font=('Segoe UI', 9, 'bold'), relief='flat',
                                       command=lambda name=agent_name: self.start_agent(name))
            control_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

            # Restart button
            restart_btn = tk.Button(button_frame, text="↻",
                                   bg=ModernDarkTheme.ACCENT_BLUE, fg='white',
                                   font=('Segoe UI', 9, 'bold'), relief='flat',
                                   command=lambda name=agent_name: self.restart_agent(name))
            restart_btn.pack(side=tk.RIGHT, padx=(5, 0))

            # Open web interface if available
            if agent_info['port']:
                web_btn = tk.Button(button_frame, text="🌐",
                                   bg=ModernDarkTheme.ACCENT_PURPLE, fg='white',
                                   font=('Segoe UI', 9, 'bold'), relief='flat',
                                   command=lambda port=agent_info['port']: webbrowser.open(f"http://localhost:{port}"))
                web_btn.pack(side=tk.RIGHT, padx=(5, 0))

            # Update grid position
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

    def setup_system_panel(self, parent):
        """Setup system information and controls panel"""
        # System stats
        stats_frame = ttk.LabelFrame(parent, text="System Status", style='Panel.TFrame')
        stats_frame.pack(fill=tk.X, padx=10, pady=10)

        self.cpu_label = ttk.Label(stats_frame, text="CPU: --", style='Status.TLabel')
        self.cpu_label.pack(anchor=tk.W, padx=10, pady=5)

        self.memory_label = ttk.Label(stats_frame, text="Memory: --", style='Status.TLabel')
        self.memory_label.pack(anchor=tk.W, padx=10, pady=5)

        self.disk_label = ttk.Label(stats_frame, text="Disk: --", style='Status.TLabel')
        self.disk_label.pack(anchor=tk.W, padx=10, pady=5)

        # Terminal output
        terminal_frame = ttk.LabelFrame(parent, text="Live Terminal", style='Panel.TFrame')
        terminal_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.terminal_text = scrolledtext.ScrolledText(
            terminal_frame,
            wrap=tk.WORD,
            height=15,
            bg=ModernDarkTheme.BG_DARKER,
            fg=ModernDarkTheme.ACCENT_GREEN,
            font=('Consolas', 10),
            insertbackground=ModernDarkTheme.ACCENT_GREEN,
            selectbackground=ModernDarkTheme.ACCENT_BLUE
        )
        self.terminal_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Command input
        cmd_frame = ttk.Frame(terminal_frame, style='Panel.TFrame')
        cmd_frame.pack(fill=tk.X, padx=5, pady=5)

        self.cmd_entry = tk.Entry(cmd_frame,
                                 bg=ModernDarkTheme.BG_DARKER,
                                 fg=ModernDarkTheme.FG_PRIMARY,
                                 insertbackground=ModernDarkTheme.FG_PRIMARY,
                                 font=('Consolas', 10))
        self.cmd_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.cmd_entry.bind('<Return>', self.execute_command)

        exec_btn = tk.Button(cmd_frame, text="RUN",
                            bg=ModernDarkTheme.ACCENT_BLUE, fg='white',
                            font=('Segoe UI', 9, 'bold'), relief='flat',
                            command=self.execute_command)
        exec_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Quick actions
        actions_frame = ttk.LabelFrame(parent, text="Quick Actions", style='Panel.TFrame')
        actions_frame.pack(fill=tk.X, padx=10, pady=10)

        actions = [
            ("📊 System Report", self.generate_system_report),
            ("🔧 Fix Issues", self.fix_system_issues),
            ("🌐 Open GitHub", lambda: webbrowser.open("https://github.com")),
            ("🤗 HuggingFace", lambda: webbrowser.open("https://huggingface.co")),
            ("🔴 Reddit AI", lambda: webbrowser.open("https://reddit.com/r/artificial")),
            ("💻 Open Terminal", self.open_external_terminal)
        ]

        for i, (text, command) in enumerate(actions):
            btn = tk.Button(actions_frame, text=text,
                           bg=ModernDarkTheme.BG_HOVER, fg=ModernDarkTheme.FG_PRIMARY,
                           font=('Segoe UI', 9), relief='flat',
                           command=command)
            btn.pack(fill=tk.X, padx=5, pady=2)

    def setup_status_bar(self, parent):
        """Setup bottom status bar"""
        status_frame = ttk.Frame(parent, style='Panel.TFrame')
        status_frame.pack(fill=tk.X, pady=(20, 0))

        self.status_label = ttk.Label(status_frame, text="🟢 System Ready", style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT, padx=10)

        self.time_label = ttk.Label(status_frame, text="", style='Status.TLabel')
        self.time_label.pack(side=tk.RIGHT, padx=10)

        self.update_time()

    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    def log_message(self, message):
        """Add message to terminal"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.terminal_text.insert(tk.END, log_entry)
        self.terminal_text.see(tk.END)

    def start_agent(self, agent_name):
        """Start specific agent"""
        if agent_name not in self.agents:
            return

        agent = self.agents[agent_name]
        script_path = agent['script']

        if not os.path.exists(script_path):
            self.log_message(f"❌ Script not found: {script_path}")
            return

        self.log_message(f"🚀 Starting {agent_name}...")

        try:
            process = subprocess.Popen([
                sys.executable, script_path
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            self.processes[agent_name] = process
            agent['status'] = 'Running'

            self.log_message(f"✅ {agent_name} started (PID: {process.pid})")
            self.create_agent_cards()  # Refresh UI

        except Exception as e:
            self.log_message(f"❌ Failed to start {agent_name}: {str(e)}")

    def stop_agent(self, agent_name):
        """Stop specific agent"""
        if agent_name in self.processes:
            try:
                process = self.processes[agent_name]
                process.terminate()
                process.wait(timeout=5)
                del self.processes[agent_name]

                self.agents[agent_name]['status'] = 'Stopped'
                self.log_message(f"⏹️ {agent_name} stopped")
                self.create_agent_cards()  # Refresh UI

            except subprocess.TimeoutExpired:
                process.kill()
                del self.processes[agent_name]
                self.agents[agent_name]['status'] = 'Stopped'
                self.log_message(f"🔪 {agent_name} force killed")
                self.create_agent_cards()

            except Exception as e:
                self.log_message(f"❌ Error stopping {agent_name}: {str(e)}")
        else:
            self.agents[agent_name]['status'] = 'Stopped'
            self.log_message(f"⚠️ {agent_name} was not running")

    def restart_agent(self, agent_name):
        """Restart specific agent"""
        self.stop_agent(agent_name)
        time.sleep(2)
        self.start_agent(agent_name)    def start_all_agents(self):
        """Start all agents"""
        self.log_message("🚀 Starting all AI agents...")

        # First tier - infrastructure and system services
        tier1 = [
            "Borg Cluster",
            "System Monitor",
            "Advanced Memory",
            "Local LLM Manager"
        ]

        # Second tier - core AI systems
        tier2 = [
            "AlphaEvolve",
            "UI-TARS",
            "WebRover",
            "Mobile Camera"
        ]

        # Third tier - LLM models
        tier3 = [
            "Gemma 3 Vision",
            "Llama 3.2 Vision",
            "Qwen 2.5 VL",
            "Phi-4 Multimodal"
        ]

        # Fourth tier - interfaces and integration services
        tier4 = [
            "Web Interface",
            "Voice Assistant",
            "Browser Automation",
            "Email Agent",
            "Gmail Integration"
        ]

        # Fifth tier - main agents
        tier5 = [
            "JARVIS",
            "IRIS"
        ]

        # Combine all tiers for complete startup sequence
        startup_order = tier1 + tier2 + tier3 + tier4 + tier5

        # Start all agents in sequence
        for agent_name in startup_order:
            if agent_name in self.agents and self.agents[agent_name]['status'] != 'Running':
                self.start_agent(agent_name)
                time.sleep(0.5)  # Brief delay between starts

        self.log_message("🎉 All agents startup sequence completed!")

        # Run post-startup integrations
        self.run_post_startup_integrations()

    def stop_all_agents(self):
        """Stop all agents"""
        self.log_message("⛔ Stopping all agents...")

        for agent_name in list(self.agents.keys()):
            if self.agents[agent_name]['status'] == 'Running':
                self.stop_agent(agent_name)

        self.log_message("🛑 All agents stopped")

    def refresh_status(self):
        """Refresh all agent status"""
        self.log_message("🔄 Refreshing system status...")

        # Check process status
        for agent_name, process in list(self.processes.items()):
            try:
                if process.poll() is None:
                    self.agents[agent_name]['status'] = 'Running'
                else:
                    self.agents[agent_name]['status'] = 'Stopped'
                    del self.processes[agent_name]
            except:
                self.agents[agent_name]['status'] = 'Stopped'
                if agent_name in self.processes:
                    del self.processes[agent_name]

        # Update system stats
        self.update_system_stats()

        # Refresh UI
        self.create_agent_cards()

        self.log_message("✅ Status refresh completed")

    def update_system_stats(self):
        """Update system statistics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.cpu_label.config(text=f"CPU: {cpu_percent:.1f}%")

            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_label.config(text=f"Memory: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB)")

            # Disk usage
            disk = psutil.disk_usage('/')
            self.disk_label.config(text=f"Disk: {disk.percent:.1f}% ({disk.free // (1024**3):.1f}GB free)")

        except Exception as e:
            self.log_message(f"Error updating stats: {str(e)}")

    def execute_command(self, event=None):
        """Execute terminal command"""
        command = self.cmd_entry.get().strip()
        if not command:
            return

        self.cmd_entry.delete(0, tk.END)
        self.log_message(f"💻 Executing: {command}")

        def run_command():
            try:
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )

                if result.stdout:
                    self.log_message(f"Output: {result.stdout.strip()}")
                if result.stderr:
                    self.log_message(f"Error: {result.stderr.strip()}")

            except Exception as e:
                self.log_message(f"Command error: {str(e)}")

        threading.Thread(target=run_command, daemon=True).start()

    def open_cloud_services(self):
        """Open cloud services"""
        self.log_message("🌐 Opening cloud services...")

        services = [
            ("GitHub", "https://github.com"),
            ("HuggingFace", "https://huggingface.co"),
            ("Reddit AI", "https://reddit.com/r/artificial"),
            ("OpenAI", "https://platform.openai.com")
        ]

        for name, url in services:
            webbrowser.open(url)
            self.log_message(f"🔗 Opened {name}")
            time.sleep(0.5)

    def generate_system_report(self):
        """Generate comprehensive system report"""
        self.log_message("📊 Generating system report...")

        report = f"""
=== AI AGENT SYSTEM REPORT ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

AGENT STATUS:
"""

        for name, agent in self.agents.items():
            status_icon = "🟢" if agent['status'] == 'Running' else "🔴"
            report += f"  {status_icon} {name}: {agent['status']}\n"

        # Save report
        report_file = f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        self.log_message(f"📋 Report saved: {report_file}")

    def fix_system_issues(self):
        """Attempt to fix common system issues"""
        self.log_message("🔧 Running system diagnostics and fixes...")

        # Check for common issues and attempt fixes
        fixes = [
            "Checking Python environment...",
            "Verifying script paths...",
            "Testing network connectivity...",
            "Optimizing system resources..."
        ]

        for fix in fixes:
            self.log_message(f"🔧 {fix}")
            time.sleep(0.5)

        self.log_message("✅ System diagnostics completed")

    def open_external_terminal(self):
        """Open external terminal"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen('cmd /k cd /d "{}"'.format(os.getcwd()), shell=True)
            else:  # Unix/Linux
                subprocess.Popen(['gnome-terminal'], cwd=os.getcwd())
            self.log_message("💻 External terminal opened")
        except Exception as e:
            self.log_message(f"❌ Failed to open terminal: {str(e)}")

    def start_monitoring(self):
        """Start background monitoring"""
        def monitor_loop():
            while True:
                try:
                    self.root.after(0, self.update_system_stats)
                    time.sleep(5)
                except Exception as e:
                    break

        threading.Thread(target=monitor_loop, daemon=True).start()

    def on_closing(self):
        """Handle window closing"""
        if messagebox.askokcancel("Exit", "Stop all agents and exit?"):
            self.log_message("🛑 Shutting down AI Agent System...")
            self.stop_all_agents()
            self.root.destroy()

    def run(self):
        """Run the hub"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initial status
        self.log_message("🎯 AI Agent System Central Hub initialized")
        self.log_message("🚀 Ready to manage all AI agents and services")
        self.log_message("💡 Use the cards above to start/stop individual agents")

        # Force window to front
        self.root.lift()
        self.root.focus_force()

        self.root.mainloop()

    def run_post_startup_integrations(self):
        """Run integrations after all agents have started"""
        self.log_message("🔄 Running post-startup integrations...")

        # Create any required files if they don't exist
        self.create_required_files()

        # Connect WebRover to the main system
        self.log_message("🌐 Integrating WebRover with main system...")
        webrover_script = Path("WebRover/backend/integrate_with_hub.py")
        if webrover_script.exists():
            self.run_python_script(str(webrover_script))
        else:
            self.create_webrover_integration()

        # Integrate voice assistant with multimodal LLMs
        self.log_message("🔊 Connecting voice system to vision models...")
        voice_integration_script = Path("services/voice_vision_integration.py")
        if voice_integration_script.exists():
            self.run_python_script(str(voice_integration_script))
        else:
            self.create_voice_integration()

        # Connect mobile camera QR system
        self.log_message("📱 Setting up mobile camera QR code system...")
        mobile_cam_script = Path("services/mobile_camera_qr.py")
        if mobile_cam_script.exists():
            self.run_python_script(str(mobile_cam_script))
        else:
            self.create_mobile_camera_integration()

        # Initialize memory system for storing user data
        self.log_message("🧠 Initializing advanced memory system...")
        memory_script = Path("core/advanced_memory.py")
        if memory_script.exists():
            self.run_python_script(str(memory_script))
        else:
            self.create_memory_system()

        # Connect all LLMs to central orchestration
        self.log_message("🤖 Connecting all multimodal LLMs to central system...")
        llm_orchestrator = Path("llms/llm_orchestrator.py")
        if llm_orchestrator.exists():
            self.run_python_script(str(llm_orchestrator))
        else:
            self.create_llm_orchestrator()

        self.log_message("✅ All systems successfully integrated!")

    def create_required_files(self):
        """Create any required files that don't exist yet"""
        self.log_message("📁 Checking and creating required files...")

        # Create directories if they don't exist
        directories = [
            "llms",
            "services",
            "core/memory_storage",
            "WebRover/backend",
        ]

        for directory in directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                self.log_message(f"📂 Created directory: {directory}")

        # Create required files dictionary
        required_files = {
            "llms/gemma_vision.py": self.generate_gemma_vision_code,
            "llms/llama_vision.py": self.generate_llama_vision_code,
            "llms/qwen_vision.py": self.generate_qwen_vision_code,
            "llms/phi4_multimodal.py": self.generate_phi4_code,
            "llms/ollama_manager.py": self.generate_ollama_manager_code,
            "llms/llm_orchestrator.py": self.create_llm_orchestrator,
            "services/voice_calling_service.py": self.generate_voice_service_code,
            "services/voice_vision_integration.py": self.create_voice_integration,
            "services/mobile_camera_qr.py": self.create_mobile_camera_integration,
            "services/browser_automation_service.py": self.generate_browser_automation_code,
            "WebRover/backend/integrate_with_hub.py": self.create_webrover_integration,
            "core/advanced_memory.py": self.create_memory_system
        }

        # Create files if they don't exist
        for file_path, generator_func in required_files.items():
            path = Path(file_path)
            if not path.exists():
                self.log_message(f"📄 Creating file: {file_path}")
                try:
                    # Create the file with generated content
                    generator_func(str(path))
                    self.log_message(f"✅ Successfully created: {file_path}")
                except Exception as e:
                    self.log_message(f"❌ Error creating {file_path}: {str(e)}")

    def run_python_script(self, script_path):
        """Run a Python script in background"""
        try:
            subprocess.Popen([sys.executable, script_path],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            creationflags=subprocess.CREATE_NO_WINDOW)
            self.log_message(f"▶️ Started: {script_path}")
        except Exception as e:
            self.log_message(f"❌ Error running {script_path}: {str(e)}")
