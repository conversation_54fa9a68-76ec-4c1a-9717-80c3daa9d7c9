{"name": "@midscene/android-playground", "version": "0.20.1", "description": "Android playground for Midscene", "main": "./dist/lib/index.js", "types": "./dist/types/index.d.ts", "files": ["dist", "static", "bin", "README.md"], "bin": {"midscene-android-playground": "./bin/android-playground", "@midscene/android-playground": "./bin/android-playground"}, "scripts": {"dev": "modern dev", "dev:server": "npm run build && ./bin/playground", "build": "modern build -c ./modern.config.ts", "build:watch": "modern build -w -c ./modern.config.ts"}, "dependencies": {"@midscene/android": "workspace:*", "@midscene/shared": "workspace:*", "@midscene/web": "workspace:*", "@yume-chan/adb": "^1.1.0", "@yume-chan/adb-scrcpy": "^1.1.0", "@yume-chan/adb-server-node-tcp": "^1.1.0", "@yume-chan/scrcpy": "^1.1.0", "@yume-chan/stream-extra": "^1.0.0", "cors": "2.8.5", "express": "^4.21.2", "open": "10.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"@modern-js/module-tools": "2.60.6", "@types/cors": "2.8.12", "@types/express": "^4.17.21", "@types/node": "^18.0.0", "typescript": "^5.8.3"}, "license": "MIT"}