#!/usr/bin/env python3
"""
Quick Launch for Chrome Bridge System
Runs on port 8081 to avoid conflicts
"""

import subprocess
import sys
import webbrowser
import time
import threading

def open_browser_delayed():
    """Open browser after 5 seconds"""
    time.sleep(5)
    webbrowser.open("http://localhost:8081")

def main():
    print("🚀 Starting Unified AI Control Hub with Chrome Bridge")
    print("🧠 Using OpenAI o1-preview reasoning model")
    print("🌐 Dashboard will be available at: http://localhost:8081")
    print("🔗 Chrome bridge will auto-initialize")
    print("")

    # Start browser in background
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()

    # Run the unified hub on port 8081
    try:
        subprocess.run([sys.executable, "unified_ai_control_hub.py", "--port", "8081"])
    except KeyboardInterrupt:
        print("\n🛑 System shutdown")

if __name__ == "__main__":
    main()
