<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample HTML Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        table {
            border-collapse: collapse;
            width: 50%;
            margin-top: 20px;
        }
        
        th,
        td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        .child-container-5 {
            font-family: awesome-iconfont;
        }
    </style>
</head>

<body>
    <h1>Data Record</h1>
    <h2>1970-01-01 19:25:01</h2>
    <h2>User Name: Stella</h2>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Field 2</th>
                <th>Field 3</th>
                <th>Field 4</th>
                <th>Field 5</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>30S</td>
                <td><PERSON><PERSON></td>
                <td><PERSON><PERSON><PERSON></td>
                <td><PERSON></td>
                <td><PERSON></td>
            </tr>
            <tr>
                <td>70U</td>
                <td>Florence Davenport</td>
                <td>Dariel Acevedo</td>
                <td>Ashlynn Delacruz</td>
                <td>Memphis Leal</td>
            </tr>
            <tr>
                <td>3AY</td>
                <td>Crystal Newman</td>
                <td>Anderson Brown</td>
                <td>Charlotte Griffith</td>
                <td>Franklin Everett</td>
            </tr>
            <tr>
                <td>YPG</td>
                <td>Kori Payne</td>
                <td>Edward Blevins</td>
                <td>Aila Gill</td>
                <td>Matthias Reed</td>
            </tr>
            <tr>
                <td>ZEN</td>
                <td>Magnolia Duke</td>
                <td>Kalel Glover</td>
                <td>Alessia Barton</td>
                <td>Cassius Peck</td>
            </tr>
        </tbody>
    </table>
    <div>
        <h3>Form</h3>
        <label for="name">Name:</label>
        <input id="J_input" placeholder="Hello World This is Placeholder" />
        <input id="J_input" placeholder="Hello World This is Placeholder" value="Now I am a value, instead of placeholder" />
        <button>Click Me</button>
        <label>Shape</label>
        <a href="https://www.google.com">Google</a>
        <a href="https://www.google.com" style="width: 100px; height: 20px; vertical-align: middle; display: inline-block; background: #ccc;"></a>
        <input placeholder="You shouldn't see this placeholder." value="Rectangle" />
        <pre id="J_keyRecord"></pre>
        <textarea placeholder="this_is_a_textarea"></textarea>
        <img src="https://lf3-static.bytednsdoc.com/obj/eden-cn/vhaeh7vhabf/midscene.png" width="2" height="2" alt="small_img" />
    </div>

    <!-- Global popup to test invisible -->
    <div style="position: absolute; top: 0px; left: 500px; width: 100%;">
        <div>
            <div style="left: 59px; top: 46px;">
                <ul>
                    <li><span>English</span></li>
                    <li><span>中文</span></li>
                    <li><span>Tiếng Việt</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- form -->
    <form action="/submit-form" method="post">
        <label for="options">Choose an option:</label>
        <select id="options" name="options">
            <option value="option1-value">Option 1</option>
            <option value="option2-value">Option 2</option>
        </select>
    </form>

    <!-- div with css zoom -->
    <!-- From chrome v128, the API would return differently https://docs.google.com/document/d/1AcnDShjT-kEuRaMchZPm5uaIgNZ4OiYtM4JI9qiV8Po/edit -->
    <div style="zoom: 1.2;width: 200px;height: 40px;">
        <div style="width: 160px;height: 40px;background-color: #ccc;zoom: 1.5;">
            This is zoomed content
        </div>
        Something Else
    </div>

    <div style="width:1px;height:120px"></div>

    <div style="width:30px;height:30px;background: #AC0" id="J_resize"></div>
    <script type="text/javascript">
        if (location.href.indexOf('resize-after-3s') > 0) {
            setTimeout(() => {
                const element = document.getElementById('J_resize');
                element.style.height = "50px";
            }, 3000);
        }
    </script>

    <!-- a label with sibiling input -->
    <div id="gh-ac-box2">
        <label for="gh-ac" class="gh-ar-hdn">输入搜索关键词</label>
        <input type="text" class="gh-tb ui-autocomplete-input" aria-autocomplete="list" aria-expanded="false" size="50" maxlength="300" aria-label="搜索任何物品" placeholder="搜索任何物品" id="gh-ac" name="_nkw" autocapitalize="off" autocorrect="off" spellcheck="false" autocomplete="off"
            aria-haspopup="true" role="combobox" aria-owns="ui-id-1">
        <input style="display:none">
    </div>

    <!-- a label with nested input -->
    <label class="life-core-input-inner__wrapper life-core-input-inner__wrapper-border life-core-input-inner__wrapper-size-md life-core-input-inner__wrapper-add-suffix">
        <input placeholder="验证码" tabindex="0" type="text" class="life-core-input life-core-input-size-md" value="">
    </label>

    <!-- a checkbox drawn by div -->
    <style>
        .life-core-check-wrapper {
            display: none;
        }
        
        .life-core-checkbox-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 1px solid #333;
        }
    </style>
    <span class="life-core-popper-trigger life-core-popper-trigger-focus life-core-tooltip">
        <label
            class="life-core-checkbox life-core-checkbox-md src-pages-Login-components-LoginCard-index-module__checkbox--Npo2V--212e2">
            <input class="life-core-check-wrapper" type="checkbox">
            <span class="life-core-checkbox-icon">
            </span>
    <span class="life-core-checkbox-label life-core-checkbox-label-no">
            </span>
    </label>
    </span>

    <!-- some very long text -->
    <div aria-label="The phrase 'The quick brown fox jumps over the lazy dog' is a well-known English-language pangram, meaning it contains every letter of the alphabet at least once. This sentence has become a standard tool for various applications, particularly in typing practice, font display, and keyboard testing. The phrase has permeated popular culture and is referenced in various media, including literature and film. Its simplicity and utility have made it a staple in educational contexts and beyond. For instance, it was famously used as the first message sent over the Moscow–Washington hotline in 19634."
        style="width: 100px; height: 100px; background-color: #ccc;"></div>
    <span></span> phrase "The quick brown fox jumps over the lazy dog" is a well-known English-language pangram, meaning it contains every letter of the alphabet at least once. This sentence has become a standard tool for various applications, particularly
    in typing practice, font display, and keyboard testing. The phrase has permeated popular culture and is referenced in various media, including literature and film. Its simplicity and utility have made it a staple in educational contexts and beyond.
    For instance, it was famously used as the first message sent over the Moscow–Washington hotline in 19634.
    </span>

    <!-- a button with children -->
    <button tabindex="0" class="btn" type="submit" aria-label="Search">
        <img class="search-icon-dark" src="./assets/search-dark.svg" alt="Search">
        <img class="search-icon-light" src="./assets/search.svg" alt="Search">
    </button>

    <!-- item hidden by css overflow: hidden  -->
    <style>
        .item-hidden-poc {
            width: 100%;
            height: 100px;
            overflow: hidden;
        }
    </style>
    <div class="item-hidden-poc">
        <div style="height: 30px">content 000</div>
        <div style="height: 30px;width: 100px;"></div>
        <div style="height: 120px">content AAA</div>
        <div style="height: 120px">content BBBB</div>
    </div>

    <!-- item with long attribute -->
    <div>
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNyIgdmlld0JveD0iMCAwIDEwIDciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMS42MjQ3IDYuNzc2NDhDMC45Njk5MzIgNy4zMDYyNSAwIDYuODM0NzcgMCA1Ljk4NjcyVjBMMTAgMS4wNjEwMWUtMDZMMS42MjQ3IDYuNzc2NDhaIiBmaWxsPSIjMjIyMjIyIiBmaWxsLW9wYWNpdHk9IjAuOTgiLz4KPC9zdmc+Cg=="
        />
        <button very-long-attr="width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;width: 100px; height: 100px; background-color: #ccc;">long-style-content</button>

    </div>

    <!-- widget wrapper defined by aria -->
    <style>
        .widget {
            width: 100px;
            height: 100px;
            background-color: #ccc;
            margin-top: 10px;
        }
    </style>
    <div class="widget" role="button" aria-label="Click me">Click me</div>
    <div class="widget" aria-controls="semi-select-5yxiyng"></div>
    <div class="widget" aria-labelledby="eval_object.object_type-label"></div>
    <!-- div with container and content children -->
    <style>
        .parent-container {
            width: 100px;
        }
    </style>
    <div class="parent-container">
        <div class="child-container">Content 1</div>
        <div class="child-container-2">
            <div class="child-container-without-content-2-1" style="width: 100px; height: 100px; background-color: #ccc;"></div>
            <div>
                <div>
                    <span>Content 2</span>
                </div>
            </div>
        </div>
        <div class="child-container-3">
            <div>
                <div>
                    <span>Nested Content 3</span>
                </div>
            </div>
        </div>
        <div class="child-container-4" style="width: 100px; height: 100px; background-color: #ccc;">
            <!-- empty element -->
            <span></span>
            <span></span>
            <span></span>
            <div foo="bar">
                <div foo="bar2"></div>
                <div foo="bar3"></div>
            </div>
        </div>
    </div>
    <span class="child-container-5">x</span>
    <!-- svg item -->
    <span role="img" aria-label="code" tabindex="-1" class="anticon anticon-code code-box-expand-trigger"><svg
            viewBox="64 64 896 896" focusable="false" data-icon="code" width="1em" height="1em" fill="currentColor"
            aria-hidden="true">
            <path
                d="M516 673c0 4.4 3.4 8 7.5 8h185c4.1 0 7.5-3.6 7.5-8v-48c0-4.4-3.4-8-7.5-8h-185c-4.1 0-7.5 3.6-7.5 8v48zm-194.9 6.1l192-161c3.8-3.2 3.8-9.1 0-12.3l-192-160.9A7.95 7.95 0 00308 351v62.7c0 2.4 1 4.6 2.9 6.1L420.7 512l-109.8 92.2a8.1 8.1 0 00-2.9 6.1V673c0 6.8 7.9 10.5 13.1 6.1zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z">
            </path>
        </svg></span>


    <!-- hidden label -->
    <style>
        .visually-hidden {
            clip: rect(0 0 0 0);
            border: 0;
            clip-path: inset(50%);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            white-space: nowrap;
            width: 1px;
        }
    </style>
    <label class="visually-hidden">
        <span>hidden label</span>
    </label>

    <!-- element under position: fixed -->
    <div style="position: absolute; top: -100px; left: -100px; width: 100px; height: 100px; background-color: #ccc; overflow: hidden;">
        <div style="position: fixed; top: 200px; right: 40px; width: 200px; height: 100px; background-color: #EEE;">
            <div>i am fixed child content</div>
        </div>
    </div>

    <!-- div with content editable -->
    <div contenteditable="true" class="content-editable-div" style="position: relative; width: 300px; height: 100px; background-color: #ccc;">
        <!-- I am a content editable div. -->
        <span>abcd efg</span>
        <div style="position: absolute; left: 0px; bottom: 0; width: 100%; height: 50px; background-color: #EEE;">
            content editable div content. We should collect the parent.
        </div>
    </div>


    <!-- absolute parent with width 0 -->
    <div style="position: absolute; top: 0px; left: 0; width: 100%; height: 0;">
        <div>absolute child content</div>
    </div>

    <!-- covered child -->
    <div style="position: relative; width: 100%; height: 100px;">
        <div style="position: absolute; top: 0px; left: 0; width: 30%;">
            content Left
        </div>
        <div style="position: absolute; top: 0px; left: 0; width: 100%;text-align: right;">
            content Right
        </div>
    </div>

    <!-- two columns -->
    <style>
        .two-columns {
            display: flex;
            flex-direction: row;
        }
        
        .column {
            margin-left: 10px;
            width: 40%;
            background-color: #ccc;
        }
    </style>
    <div class="two-columns">
        <div class="column">
            <!-- same-origin iframe -->
            <iframe src="child.html" frameborder="0" width="400px" height="200px"></iframe>
        </div>
        <div class="column">
            <!-- cross-origin iframe-->
            <iframe src="https://www.bytedance.com" frameborder="0" width="400px" height="200px"></iframe>
        </div>
    </div>


    <div style="overflow: hidden; width: 100px; height: 100px; background-color: #ccc;">
        <div style="width: 120px; height: 120px; background-color: #EEE;">AAA</div>
        <div style="width: 120px; height: 120px; background-color: #EEE;">BBB</div>
        <div style="position: absolute; bottom: 200px; right: 0; width: 120px; height: 120px; background-color: #CCC;">
            This should be collected
        </div>


    </div>
</body>

</html>