"""
Orchestrator for the AI Agent System.
Uses LangGraph for state management and workflow orchestration.
"""
import os
import json
import asyncio
import base64
import re
from typing import Dict, Any, Optional, Callable
import logging
from datetime import datetime
from io import BytesIO

from PIL import Image

# Import LangGraph components
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.sqlite import SqliteSaver
import google.generativeai as genai

from config import flofaction_config
from core.browser_control import BrowserController
from core.state_manager import StateManager

# Set up logger
logger = logging.getLogger("orchestrator")

# Configure the Vision model
genai.configure(api_key=os.environ.get("GOOGLE_AI_API_KEY"))

# For LangGraph state
class SimpleNamespace:
    """A simple class for LangGraph state."""
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

class Orchestrator:
    """Orchestrator agent using LangGraph for workflow management."""

    def __init__(self, state_manager: StateManager):
        """
        Initialize the orchestrator agent.

        Args:
            state_manager: State manager for persistent storage
        """
        self.state_manager = state_manager
        self.browser_controller = BrowserController()

        # Initialize checkpoint saver for workflow state persistence
        self.checkpoint_saver = SqliteSaver.from_conn_string("./data/checkpoints.db")

        # Initialize the workflow graph
        self.graph = self._build_workflow_graph()
        self.current_state = None
        self.hitl_required = False
        self.hitl_callback = None

    def _build_workflow_graph(self) -> StateGraph:
        """Build the workflow graph using LangGraph."""
        # Define the graph
        builder = StateGraph(SimpleNamespace)

        # Add nodes to the graph
        builder.add_node("plan", self._plan_task)
        builder.add_node("perceive", self._perceive_browser)
        builder.add_node("analyze", self._analyze_perception)
        builder.add_node("decide", self._decide_next_action)
        builder.add_node("act", self._execute_action)
        builder.add_node("verify", self._verify_result)
        builder.add_node("hitl", self._handle_hitl)

        # Build the graph with conditional edges
        builder.add_edge("plan", "perceive")
        builder.add_edge("perceive", "analyze")
        builder.add_edge("analyze", "decide")
        builder.add_conditional_edges(
            "decide",
            self._route_after_decision,
            {
                "act": "act",
                "hitl": "hitl",
                "done": END
            }
        )
        builder.add_edge("act", "verify")
        builder.add_conditional_edges(
            "verify",
            self._route_after_verification,
            {
                "continue": "perceive",
                "retry": "act",
                "done": END
            }
        )
        builder.add_conditional_edges(
            "hitl",
            self._route_after_hitl,
            {
                "continue": "perceive",
                "done": END
            }
        )

        # Set the entry point
        builder.set_entry_point("plan")

        # Compile and return the graph
        return builder.compile(checkpointer=self.checkpoint_saver)

    def set_hitl_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set the callback for HITL events."""
        self.hitl_callback = callback

    async def execute_task(self, task_description: str) -> Dict[str, Any]:
        """
        Execute a task using the workflow graph.

        Args:
            task_description: Description of the task to execute

        Returns:
            Result of the task execution
        """
        # Initialize the state
        initial_state = {
            "task": task_description,
            "plan": None,
            "current_step": 0,
            "browser_state": None,
            "perception": None,
            "analysis": None,
            "next_action": None,
            "action_result": None,
            "verification": None,
            "status": "running",
            "result": None,
            "error": None,
            "hitl_state": {
                "required": False,
                "reason": None,
                "input": None
            },
            "timestamp": datetime.now().isoformat()
        }

        # Create a new checkpoint or continue from existing one
        try:
            # Start or resume the graph execution
            self.current_state = initial_state

            # Connect to browser
            await self.browser_controller.connect_to_opera_neon()

            # Use a unique thread_id for each task execution to persist state correctly
            config = {"configurable": {"thread_id": f"task-{datetime.now().timestamp()}"}}

            # Execute the graph asynchronously
            async for event in self.graph.astream(initial_state, config=config):
                # The last event will contain the final state
                self.current_state = event

                # If HITL is required, pause execution and wait for input
                if self.current_state.get("__end__", {}).get("hitl_state", {}).get("required", False):
                    self.hitl_required = True

                    # Call the HITL callback if provided
                    if self.hitl_callback:
                        self.hitl_callback(self.current_state["__end__"])

                    # Wait until HITL input is received
                    while self.hitl_required:
                        await asyncio.sleep(1)

            return self.current_state

        except Exception as e:
            logger.exception(f"Error executing task: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def resume_hitl(self, user_input: Optional[str] = None):
        """Resume execution after HITL intervention."""
        if self.hitl_required and self.current_state:
            # Update the state with user input if provided
            if user_input:
                self.current_state["hitl_state"]["input"] = user_input

            # Mark HITL as completed
            self.current_state["hitl_state"]["required"] = False
            self.hitl_required = False

    # Graph node implementations
    async def _plan_task(self, state):
        """Plan the task execution."""
        task = state["task"]

        # Use Gemini to create a plan
        model = genai.GenerativeModel(flofaction_config.GEMINI_TEXT_MODEL)
        prompt = f"""
        You are an AI assistant that creates detailed step-by-step plans for browser automation tasks.

        TASK: {task}

        Create a detailed plan with specific, executable steps.
        Format the response as a JSON array of step objects, each with:
        1. 'step_number': The step number
        2. 'description': A clear description of what this step does
        3. 'action_type': One of [navigate, click, type, wait, screenshot, evaluate]
        4. 'action_params': Any parameters needed (like URL, selector, text, etc.)

        IMPORTANT: Be specific with selectors and actions. Use CSS selectors when possible.
        """

        try:
            response = await model.generate_content_async(prompt)
            plan = json.loads(response.text)

            return {
                **state,
                "plan": plan,
                "current_step": 0
            }
        except Exception as e:
            logger.exception(f"Error planning task: {e}")
            return {
                **state,
                "error": f"Error planning task: {str(e)}"
            }

    async def _perceive_browser(self, state):
        """Take a screenshot and perceive the current browser state."""
        try:
            # Take a screenshot of the browser
            await self.browser_controller.take_screenshot()

            # Update the state with the screenshot
            return {
                **state,
                "perception": {
                    "screenshot": self.browser_controller.get_last_screenshot_base64(),
                    "timestamp": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.exception(f"Error perceiving browser: {e}")
            return {
                **state,
                "error": f"Error perceiving browser: {str(e)}"
            }

    async def _analyze_perception(self, state):
        """Analyze the screenshot using the vision model."""
        if not state["perception"] or not state["perception"]["screenshot"]:
            return {
                **state,
                "error": "No screenshot available for analysis"
            }

        try:
            # Get the current step
            current_step = state["current_step"]
            plan = state["plan"]

            if current_step >= len(plan):
                # All steps completed
                return {
                    **state,
                    "analysis": {
                        "status": "completed",
                        "message": "All steps completed"
                    }
                }

            current_step_data = plan[current_step]

            # Create a prompt for the vision model
            model = genai.GenerativeModel(flofaction_config.GEMINI_PRO_VISION_MODEL)

            # Convert base64 to image for the vision model
            screenshot_data = base64.b64decode(state["perception"]["screenshot"])
            image = Image.open(BytesIO(screenshot_data))

            prompt = f"""
            Analyze this browser screenshot and help me complete the following step:

            {current_step_data['description']}

            I need to perform a {current_step_data['action_type']} action.

            Provide the following information in JSON format:
            1. 'element_found': true/false if you can identify the target element
            2. 'element_description': Description of the target element
            3. 'coordinates': [x, y] coordinates if clicking is needed
            4. 'selector': CSS selector for the element if available
            5. 'text_content': Text content of the element if relevant
            6. 'sensitive_fields': true/false if you see password fields or other sensitive input
            7. 'analysis': Your overall analysis of the situation
            """

            response = await model.generate_content_async([prompt, image])

            # Extract JSON from the response
            # Note: The regex `r'\{.*\}'` can be greedy. Consider more robust JSON extraction if issues arise.
            json_match = re.search(r'```json\n(.*?)\n```', response.text, re.DOTALL)
            if json_match:
                analysis_json = json.loads(json_match.group(1))
            else:
                # Try to extract any JSON object
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    analysis_json = json.loads(json_match.group(0))
                else:
                    analysis_json = {"error": "Could not parse JSON from response"}

            return {
                **state,
                "analysis": analysis_json
            }
        except Exception as e:
            logger.exception(f"Error analyzing perception: {e}")
            return {
                **state,
                "error": f"Error analyzing perception: {str(e)}"
            }

    async def _decide_next_action(self, state):
        """Decide the next action based on the analysis."""
        if not state["analysis"]:
            return {
                **state,
                "next_action": {
                    "decision": "error",
                    "error": "No analysis available"
                }
            }

        try:
            analysis = state["analysis"]

            # Check for sensitive fields
            has_sensitive_fields = analysis.get("sensitive_fields", False)

            if has_sensitive_fields:
                # Require HITL intervention
                return {
                    **state,
                    "next_action": {
                        "decision": "hitl",
                        "reason": "Sensitive fields detected"
                    },
                    "hitl_state": {
                        "required": True,
                        "reason": "Sensitive fields detected. Human input required.",
                        "input": None
                    }
                }

            # Get the current step
            current_step = state["current_step"]
            plan = state["plan"]

            if current_step >= len(plan):
                # All steps completed
                return {
                    **state,
                    "next_action": {
                        "decision": "done",
                        "message": "All steps completed"
                    },
                    "status": "completed"
                }

            current_step_data = plan[current_step]

            # Determine the action based on the step and analysis
            action = {
                "decision": "act",
                "action_type": current_step_data["action_type"],
                "action_params": {
                    **current_step_data["action_params"],
                }
            }

            # If coordinates are provided in the analysis, use them
            if "coordinates" in analysis:
                action["action_params"]["x"] = analysis["coordinates"][0]
                action["action_params"]["y"] = analysis["coordinates"][1]

            # If a selector is provided in the analysis, use it
            if "selector" in analysis and analysis["selector"]:
                action["action_params"]["selector"] = analysis["selector"]

            return {
                **state,
                "next_action": action
            }
        except Exception as e:
            logger.exception(f"Error deciding next action: {e}")
            return {
                **state,
                "error": f"Error deciding next action: {str(e)}"
            }

    def _route_after_decision(self, state):
        """Route to the next node based on the decision."""
        if "next_action" not in state or not state["next_action"]:
            return "done"

        decision = state["next_action"].get("decision")

        if decision == "hitl":
            return "hitl"
        elif decision == "done":
            return "done"
        else:
            return "act"

    async def _execute_action(self, state):
        """Execute the decided action."""
        if "next_action" not in state or not state["next_action"]:
            return {
                **state,
                "error": "No action to execute"
            }

        action = state["next_action"]

        try:
            result = {
                "success": False,
                "message": "Action execution failed"
            }

            # Execute the action based on its type
            if action["action_type"] == "navigate":
                url = action["action_params"].get("url")
                success = await self.browser_controller.navigate(url)
                result = {
                    "success": success,
                    "message": f"Navigation to {url}: {'successful' if success else 'failed'}"
                }

            elif action["action_type"] == "click":
                # Check if we have a selector
                selector = action["action_params"].get("selector")

                if selector:
                    # Use selector-based clicking
                    success = await self.browser_controller.click_element(selector)
                    result = {
                        "success": success,
                        "message": f"Click on selector '{selector}': {'successful' if success else 'failed'}"
                    }
                else:
                    # Use coordinate-based clicking
                    x = action["action_params"].get("x")
                    y = action["action_params"].get("y")

                    if x is not None and y is not None:
                        success = await self.browser_controller.click_coordinates(x, y)
                        result = {
                            "success": success,
                            "message": f"Click at coordinates ({x}, {y}): {'successful' if success else 'failed'}"
                        }
                    else:
                        result = {
                            "success": False,
                            "message": "No selector or coordinates provided for click action"
                        }

            elif action["action_type"] == "type":
                text = action["action_params"].get("text")
                selector = action["action_params"].get("selector")

                success = await self.browser_controller.type_text(text, selector)
                result = {
                    "success": success,
                    "message": f"Type text '{text}': {'successful' if success else 'failed'}"
                }

            elif action["action_type"] == "wait":
                delay = action["action_params"].get("delay", 1)
                await asyncio.sleep(delay)
                result = {
                    "success": True,
                    "message": f"Waited for {delay} seconds"
                }

            elif action["action_type"] == "evaluate":
                script = action["action_params"].get("script")
                js_result = await self.browser_controller.evaluate_javascript(script)
                result = {
                    "success": True,
                    "message": "JavaScript evaluation completed",
                    "js_result": js_result
                }

            else:
                result = {
                    "success": False,
                    "message": f"Unknown action type: {action['action_type']}"
                }

            return {
                **state,
                "action_result": result
            }

        except Exception as e:
            logger.exception(f"Error executing action: {e}")
            return {
                **state,
                "action_result": {
                    "success": False,
                    "message": f"Error executing action: {str(e)}"
                },
                "error": f"Error executing action: {str(e)}"
            }

    async def _verify_result(self, state):
        """Verify the result of the action."""
        if "action_result" not in state or not state["action_result"]:
            return {
                **state,
                "verification": {
                    "status": "failed",
                    "message": "No action result to verify"
                }
            }

        result = state["action_result"]

        if result["success"]:
            # Action was successful, move to the next step
            current_step = state["current_step"]

            return {
                **state,
                "current_step": current_step + 1,
                "verification": {
                    "status": "success",
                    "message": result["message"]
                }
            }
        else:
            # Action failed, retry or handle error
            return {
                **state,
                "verification": {
                    "status": "failed",
                    "message": result["message"]
                }
            }

    def _route_after_verification(self, state):
        """Route to the next node based on verification result."""
        if "verification" not in state or not state["verification"]:
            return "retry"

        verification = state["verification"]

        if verification["status"] == "success":
            # Check if all steps are completed
            current_step = state["current_step"]
            plan = state["plan"]

            if current_step >= len(plan):
                return "done"
            else:
                return "continue"
        else:
            # Retry the action
            return "retry"

    async def _handle_hitl(self, state):
        """Handle human-in-the-loop intervention."""
        # This function doesn't actively do anything - it's a placeholder for
        # the external HITL process to interact with. The actual pause happens
        # in the execute_task method.
        return state

    def _route_after_hitl(self, state):
        """Route to the next node based on HITL state."""
        hitl_state = state.get("hitl_state", {})

        if not hitl_state.get("required", False):
            # HITL completed, continue with execution
            return "continue"

        # If we somehow got here but HITL is still required, this would be handled
        # by the execute_task method that waits for the hitl_required flag to change
        return "continue"
