## Preparation

### Install Node.js

Install [Node.js 18 or above](https://nodejs.org/en/download/) globally.

### Prepare an API Key

Prepare an API key from a visual-language (VL) model. You will use it later.

You can check the supported models in [Choose a model](../choose-a-model)

### Install adb

`adb` is a command-line tool that allows you to communicate with an Android device. There are two ways to install `adb`:

- way 1: use [Android Studio](https://developer.android.com/studio) to install
- way 2: use [Android command-line tools](https://developer.android.com/studio#command-line-tools-only) to install

Verify adb is installed successfully:

```bash
adb --version
```

When you see the following output, adb is installed successfully:

```log
Android Debug Bridge version 1.0.41
Version 34.0.4-10411341
Installed as /usr/local/bin//adb
Running on Darwin 24.3.0 (arm64)
```

### Set environment variable ANDROID_HOME

Reference [Android environment variables](https://developer.android.com/tools/variables), set the environment variable `ANDROID_HOME`.

Verify the `ANDROID_HOME` variable is set successfully:

```bash
echo $ANDROID_HOME
```

When the command has any output, the `ANDROID_HOME` variable is set successfully:

```log
/Users/<USER>/Library/Android/sdk
```

### Connect Android device with adb

In the developer options of the system settings, enable the 'USB debugging' of the Android device, if the 'USB debugging (secure settings)' exists, also enable it, then connect the Android device with a USB cable

<p align="center">
  <img src="/android-usb-debug-en.png" alt="android usb debug" width="400"/>
</p>

Verify the connection:

```bash
adb devices -l
```

When you see the following output, the connection is successful:

```log
List of devices attached
s4ey59	device usb:34603008X product:cezanne model:M2006J device:cezan transport_id:3
```