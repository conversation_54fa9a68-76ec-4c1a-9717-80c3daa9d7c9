{"name": "loader-runner", "version": "4.3.0", "description": "Runs (webpack) loaders", "main": "lib/LoaderRunner.js", "scripts": {"lint": "eslint lib test", "pretest": "npm run lint", "test": "mocha --reporter spec", "precover": "npm run lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha"}, "repository": {"type": "git", "url": "git+https://github.com/webpack/loader-runner.git"}, "keywords": ["webpack", "loader"], "author": "<PERSON> @sokra", "license": "MIT", "bugs": {"url": "https://github.com/webpack/loader-runner/issues"}, "homepage": "https://github.com/webpack/loader-runner#readme", "engines": {"node": ">=6.11.5"}, "files": ["lib/", "bin/", "hot/", "web_modules/", "schemas/"], "devDependencies": {"eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}}