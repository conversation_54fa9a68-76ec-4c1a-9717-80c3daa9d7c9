.device-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .device-title-container {
    display: flex;
    align-items: center;
  }

  .device-title {
    margin: 0;
    font-size: 18px;
    color: #000;
    margin-right: 12px;
    display: flex;
    align-items: center;
    height: 32px;
  }
}

.device-dropdown-button {
  border: none;
  padding: 4px 12px 4px 8px;
  display: flex;
  align-items: center;
  background: #f0f0f0;
  border-radius: 20px;
  box-shadow: none;
  height: 32px;

  .device-icon-container {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #3b82f6;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .device-icon {
      font-size: 14px;
      color: white;
    }

    .status-indicator {
      position: absolute;
      right: -5px;
      bottom: -5px;
      display: flex;
      align-items: center;
    }
  }

  .device-name {
    font-weight: bold;
    font-size: 16px;
    color: #333;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px;
    margin-right: 4px;

    &.no-device {
      color: #999;
      font-weight: normal;
      font-style: italic;
    }
  }

  .dropdown-arrow {
    color: #666;
    font-size: 12px;
    transform: scaleY(0.6);
    font-weight: bold;
  }
}

.device-dropdown {
  width: 430px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 10px 20px 0px #00000005;
  border: 1px solid #EAEDF1;

  .dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 16px 12px;

    .dropdown-title {
      font-weight: bold;
      font-size: 16px;
      color: #333;
    }
  }

  .device-list {
    &-item {
      padding: 5px 17px 9px 6px;
      cursor: pointer;

      &.selected {
        background: #2B83FF14;
      }

      &.offline {
        cursor: not-allowed;
        opacity: 0.5;
      }

      .device-item-content {
        display: flex;
        align-items: center;

        .device-item-icon-container {
          width: 34px;
          height: 34px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          .device-item-icon {
            font-size: 22px;
            color: #666;
          }
        }

        .device-item-info {
          flex: 1;

          .device-item-name {
            font-weight: bold;
            font-size: 15px;
            color: #333;
          }

          .device-item-status {
            display: flex;
            align-items: center;
            margin-top: 4px;

            .status-badge {
              display: flex;
              align-items: center;
              margin-right: 12px;

              .status-text {
                color: #666;
                font-size: 12px;
              }
            }

            .device-id-container {
              color: #999;
              font-size: 12px;
            }
          }
        }

        .current-device-indicator {
          margin-left: auto;
          color: #1890ff;
          font-weight: bold;
          font-size: 13px;
        }
      }
    }

    &-empty {
      padding: 20px;
      text-align: center;
      color: #999;
    }
  }
}

.status-dot {
  margin-right: 4px;
  font-size: 12px;
}

.status-divider {
  margin: 0 4px;
}