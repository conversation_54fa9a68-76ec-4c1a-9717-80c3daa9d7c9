@echo off
echo Starting Optimized Gemini CLI with your API key...
set GEMINI_API_KEY=AIzaSyBlM3-NO7PISCYYg6GWyrfUhaQr6R3dhhk
set GEMINI_MODEL=gemini-2.0-flash-exp
set GEMINI_SANDBOX=false
set NODE_OPTIONS=--max-old-space-size=8192
cd /d "C:\Users\<USER>\Documents\augment-projects\Ai Agent System"
echo Using fast Gemini 2.0 Flash model for better performance...
npx https://github.com/google-gemini/gemini-cli
pause
