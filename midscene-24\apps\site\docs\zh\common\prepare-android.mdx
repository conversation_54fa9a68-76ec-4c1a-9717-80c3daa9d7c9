## 准备工作

### 安装 Node.js

安装 [Node.js 18 或以上版本](https://nodejs.org/en/download/)。

### 准备 API Key

准备一个视觉语言（VL）模型的 API Key。

你可以在 [选择 AI 模型](../choose-a-model) 文档中查看 Midscene.js 支持的模型和配置。

### 安装 adb

`adb` 是一个命令行工具，允许你与 Android 设备通信。有两种安装 `adb` 的方法：

- 方法 1：使用 [Android Studio](https://developer.android.com/studio?hl=zh-cn) 安装
- 方法 2：使用 [Android 命令行工具](https://developer.android.com/studio#command-line-tools-only) 安装

验证 `adb` 是否安装成功：

```bash
adb --version
```

当你看到以下输出时，表示 `adb` 安装成功：

```log
Android Debug Bridge version 1.0.41
Version 34.0.4-10411341
Installed as /usr/local/bin//adb
Running on Darwin 24.3.0 (arm64) 
```

### 设置环境 ANDROID_HOME 变量

参考[Android 环境变量](https://developer.android.com/tools/variables?hl=zh-cn)，设置环境变量 `ANDROID_HOME`。

验证 `ANDROID_HOME` 变量是否设置成功：

```bash
echo $ANDROID_HOME
```

当上述命令有输出时，表示 `ANDROID_HOME` 变量设置成功：

```log
/Users/<USER>/Library/Android/sdk
```

### 连接 Android 设备

在 Android 设备的开发者选项中，启用 'USB 调试'，如果存在 'USB 调试（安全设置）'，也启用它，然后使用 USB 线连接 Android 设备。


<p align="center">
  <img src="/android-usb-debug-en.png" alt="android usb debug" width="400"/>
</p>

验证连接：

```bash
adb devices -l
```

当看到以下输出时，表示连接成功：

```log
List of devices attached
s4ey59	device usb:34603008X product:cezanne model:M2006J device:cezan transport_id:3
```
