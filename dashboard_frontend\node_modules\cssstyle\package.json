{"name": "cssstyle", "description": "CSSStyleDeclaration Object Model implementation", "keywords": ["CSS", "CSSStyleDeclaration", "StyleSheet"], "version": "2.3.0", "homepage": "https://github.com/jsdom/cssstyle", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jon.sakas.co/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://fatfisz.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/chad3814"}], "repository": "jsdom/cssstyle", "bugs": "https://github.com/jsdom/cssstyle/issues", "directories": {"lib": "./lib"}, "files": ["lib/"], "main": "./lib/CSSStyleDeclaration.js", "dependencies": {"cssom": "~0.3.6"}, "devDependencies": {"babel-generator": "~6.26.1", "babel-traverse": "~6.26.0", "babel-types": "~6.26.0", "babylon": "~6.18.0", "eslint": "~6.0.0", "eslint-config-prettier": "~6.0.0", "eslint-plugin-prettier": "~3.1.0", "jest": "^24.8.0", "npm-run-all": "^4.1.5", "prettier": "~1.18.0", "request": "^2.88.0", "resolve": "~1.11.1"}, "scripts": {"download": "node ./scripts/download_latest_properties.js && eslint lib/allProperties.js --fix", "generate": "run-p generate:*", "generate:implemented_properties": "node ./scripts/generate_implemented_properties.js", "generate:properties": "node ./scripts/generate_properties.js", "lint": "npm run generate && eslint . --max-warnings 0", "lint:fix": "eslint . --fix --max-warnings 0", "prepublishOnly": "npm run lint && npm run test", "test": "npm run generate && jest", "test-ci": "npm run lint && npm run test && codecov", "update-authors": "git log --format=\"%aN <%aE>\" | sort -f | uniq > AUTHORS"}, "license": "MIT", "engines": {"node": ">=8"}}