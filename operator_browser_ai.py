#!/usr/bin/env python3
"""
OpenAI Operator-Style Browser AI
Integrates with local AI agent system, LLMs, and provides exact Operator functionality
"""

import gradio as gr
import asyncio
import json
import sys
import os
import subprocess
import requests
import time
import threading
import socket
import websocket
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import importlib.util

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class LocalAgentSystemConnector:
    """Connects to the user's existing local AI agent system"""

    def __init__(self):
        self.agent_manager = None
        self.llm_router = None
        self.state_manager = None
        self.webrover_client = None
        self.ui_tars_client = None
        self.midscene_agent = None
        self.initialize_connections()

    def initialize_connections(self):
        """Initialize connections to local AI agent system"""
        try:
            # Connect to LLM Router
            self.connect_llm_router()

            # Connect to Agent Manager
            self.connect_agent_manager()

            # Connect to WebRover
            self.connect_webrover()

            # Connect to UI-TARS
            self.connect_ui_tars()

            # Connect to State Manager
            self.connect_state_manager()

            print("✅ Connected to local AI agent system")

        except Exception as e:
            print(f"⚠️ Partial connection to local system: {e}")

    def connect_llm_router(self):
        """Connect to the local LLM router"""
        try:
            # Import the user's LLM router
            llm_router_path = project_root / "llm" / "llm_router.py"
            if llm_router_path.exists():
                spec = importlib.util.spec_from_file_location("llm_router", llm_router_path)
                llm_router_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(llm_router_module)
                self.llm_router = llm_router_module.LLMRouter()
                print("✅ Connected to LLM Router")
        except Exception as e:
            print(f"⚠️ LLM Router connection failed: {e}")

    def connect_agent_manager(self):
        """Connect to the agent manager"""
        try:
            agent_manager_path = project_root / "core" / "agent_manager.py"
            if agent_manager_path.exists():
                spec = importlib.util.spec_from_file_location("agent_manager", agent_manager_path)
                agent_manager_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(agent_manager_module)
                self.agent_manager = agent_manager_module.AgentManager()
                print("✅ Connected to Agent Manager")
        except Exception as e:
            print(f"⚠️ Agent Manager connection failed: {e}")

    def connect_webrover(self):
        """Connect to WebRover backend"""
        try:
            # Test WebRover API connection
            response = requests.get("http://localhost:8000/health", timeout=3)
            if response.status_code == 200:
                self.webrover_client = "http://localhost:8000"
                print("✅ Connected to WebRover")
        except:
            print("⚠️ WebRover not available")

    def connect_ui_tars(self):
        """Connect to UI-TARS"""
        try:
            # Test UI-TARS connection
            response = requests.get("http://localhost:8080/health", timeout=3)
            if response.status_code == 200:
                self.ui_tars_client = "http://localhost:8080"
                print("✅ Connected to UI-TARS")
        except:
            print("⚠️ UI-TARS not available")

    def connect_state_manager(self):
        """Connect to state manager"""
        try:
            state_manager_path = project_root / "core" / "state_manager.py"
            if state_manager_path.exists():
                spec = importlib.util.spec_from_file_location("state_manager", state_manager_path)
                state_manager_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(state_manager_module)
                self.state_manager = state_manager_module.StateManager()
                print("✅ Connected to State Manager")
        except Exception as e:
            print(f"⚠️ State Manager connection failed: {e}")

    async def chat_with_local_llm(self, message: str, context: str = "") -> str:
        """Chat with local LLM through the router"""
        if not self.llm_router:
            return "❌ LLM Router not available"

        try:
            response = await self.llm_router.generate_chat(
                messages=[
                    {"role": "system", "content": f"You are an AI browser automation assistant. Context: {context}"},
                    {"role": "user", "content": message}
                ]
            )
            return response.get("text", "No response from LLM")
        except Exception as e:
            return f"❌ LLM Error: {str(e)}"

    def execute_browser_automation(self, task: str) -> Dict[str, Any]:
        """Execute browser automation through WebRover or UI-TARS"""
        if self.webrover_client:
            return self.execute_webrover_task(task)
        elif self.ui_tars_client:
            return self.execute_ui_tars_task(task)
        else:
            return {"error": "No browser automation service available"}

    def execute_webrover_task(self, task: str) -> Dict[str, Any]:
        """Execute task through WebRover"""
        try:
            payload = {
                "query": task,
                "agent_type": "task"
            }
            response = requests.post(f"{self.webrover_client}/query", json=payload, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"WebRover error: {response.status_code}"}
        except Exception as e:
            return {"error": f"WebRover execution failed: {str(e)}"}

    def execute_ui_tars_task(self, task: str) -> Dict[str, Any]:
        """Execute task through UI-TARS"""
        try:
            payload = {
                "action": task,
                "type": "browser_automation"
            }
            response = requests.post(f"{self.ui_tars_client}/execute", json=payload, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"UI-TARS error: {response.status_code}"}
        except Exception as e:
            return {"error": f"UI-TARS execution failed: {str(e)}"}

class OperatorBrowserController:
    """OpenAI Operator-like browser controller with exact same features"""

    def __init__(self, agent_connector: LocalAgentSystemConnector):
        self.agent_connector = agent_connector
        self.current_task = None
        self.task_steps = []
        self.step_index = 0
        self.is_user_controlling = False
        self.automation_paused = False
        self.browser_state = {}
        self.conversation_history = []

    async def start_operator_task(self, user_input: str) -> Dict[str, Any]:
        """Start a task exactly like OpenAI Operator"""
        self.current_task = user_input
        self.step_index = 0
        self.is_user_controlling = False
        self.automation_paused = False

        # Use local LLM to plan the task
        planning_prompt = f"""
        You are an AI browser automation agent like OpenAI's Operator.
        Break down this user request into specific, actionable browser automation steps:

        User Request: {user_input}

        Provide a step-by-step plan that can be executed through browser automation.
        """

        llm_response = await self.agent_connector.chat_with_local_llm(planning_prompt)

        # Parse the response into steps
        self.task_steps = self.parse_llm_steps(llm_response)

        return {
            "task": user_input,
            "steps": self.task_steps,
            "llm_planning": llm_response,
            "status": "ready_to_execute",
            "can_takeover": True,
            "total_steps": len(self.task_steps)
        }

    def parse_llm_steps(self, llm_response: str) -> List[str]:
        """Parse LLM response into actionable steps"""
        lines = llm_response.split('\n')
        steps = []

        for line in lines:
            line = line.strip()
            # Look for numbered steps or bullet points
            if (line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or
                line.startswith('-') or line.startswith('•')):
                # Clean up the step
                step = line.lstrip('123456789.-• ').strip()
                if step and len(step) > 5:  # Ignore very short steps
                    steps.append(step)

        # If no structured steps found, use the whole response as one step
        if not steps:
            steps = [llm_response.strip()]

        return steps

    async def execute_next_step(self) -> Dict[str, Any]:
        """Execute the next step like Operator"""
        if self.automation_paused:
            return {"status": "paused", "message": "Automation is paused. Resume to continue."}

        if self.is_user_controlling:
            return {"status": "user_control", "message": "User has control. Resume automation when ready."}

        if self.step_index >= len(self.task_steps):
            return {"status": "completed", "message": "✅ Task completed successfully!"}

        current_step = self.task_steps[self.step_index]

        # Execute the step through the agent system
        result = self.agent_connector.execute_browser_automation(current_step)

        self.step_index += 1

        return {
            "step": current_step,
            "step_number": self.step_index,
            "total_steps": len(self.task_steps),
            "result": result,
            "status": "in_progress" if self.step_index < len(self.task_steps) else "completed",
            "can_takeover": True,
            "progress_percentage": (self.step_index / len(self.task_steps)) * 100
        }

    def takeover_control(self) -> str:
        """User takes control like in Operator"""
        self.is_user_controlling = True
        self.automation_paused = True
        return """🎮 **USER CONTROL ACTIVATED**

You now have full control of the browser!

**What I'll do:**
- ✅ Monitor your actions
- ✅ Provide assistance when asked
- ✅ Remember context for when you hand back control
- ✅ Continue the task from where you left off

**To resume automation:** Click "Resume AI Control" or say "continue automation"
"""

    def resume_ai_control(self) -> str:
        """Resume AI control like in Operator"""
        self.is_user_controlling = False
        self.automation_paused = False
        return """🤖 **AI CONTROL RESUMED**

I'm back in control and will continue the automation!

**Current Status:**
- ✅ Resuming from where we left off
- ✅ All context preserved
- ✅ Ready to execute remaining steps

**Next:** I'll continue with the next step automatically.
"""

    def pause_automation(self) -> str:
        """Pause automation"""
        self.automation_paused = True
        return "⏸️ **AUTOMATION PAUSED** - Click Resume or Next Step to continue"

    def get_operator_status(self) -> Dict[str, Any]:
        """Get current status like Operator"""
        return {
            "current_task": self.current_task,
            "total_steps": len(self.task_steps),
            "completed_steps": self.step_index,
            "remaining_steps": len(self.task_steps) - self.step_index,
            "progress_percentage": (self.step_index / len(self.task_steps)) * 100 if self.task_steps else 0,
            "is_user_controlling": self.is_user_controlling,
            "automation_paused": self.automation_paused,
            "can_takeover": not self.is_user_controlling,
            "can_resume": self.is_user_controlling or self.automation_paused,
            "agent_connections": {
                "llm_router": self.agent_connector.llm_router is not None,
                "webrover": self.agent_connector.webrover_client is not None,
                "ui_tars": self.agent_connector.ui_tars_client is not None,
                "agent_manager": self.agent_connector.agent_manager is not None
            }
        }

class OperatorConversationManager:
    """Manages conversation exactly like OpenAI Operator"""

    def __init__(self, browser_controller: OperatorBrowserController):
        self.browser_controller = browser_controller
        self.conversation_memory = []
        self.session_start = datetime.now()

    async def process_operator_message(self, message: str, history: List) -> Tuple[List, str]:
        """Process message with Operator-like intelligence"""
        if not message.strip():
            return history, ""

        # Handle control commands
        control_response = self.handle_control_commands(message)
        if control_response:
            history.append([message, control_response])
            return history, ""

        # Check if this is a new automation request
        if self.is_automation_request(message):
            response = await self.handle_automation_request(message)
            history.append([message, response])
            return history, ""

        # Handle conversational chat with local LLM
        response = await self.handle_conversational_chat(message)
        history.append([message, response])
        return history, ""

    def handle_control_commands(self, message: str) -> Optional[str]:
        """Handle Operator control commands"""
        msg_lower = message.lower()

        if any(cmd in msg_lower for cmd in ["take control", "takeover", "let me control", "i'll take over"]):
            return self.browser_controller.takeover_control()

        if any(cmd in msg_lower for cmd in ["resume", "continue automation", "ai control", "take back control"]):
            return self.browser_controller.resume_ai_control()

        if any(cmd in msg_lower for cmd in ["pause", "stop", "wait"]):
            return self.browser_controller.pause_automation()

        if any(cmd in msg_lower for cmd in ["next step", "continue", "next", "proceed"]):
            # This will be handled asynchronously
            return None

        if any(cmd in msg_lower for cmd in ["status", "progress", "where are we"]):
            status = self.browser_controller.get_operator_status()
            return self.format_status_response(status)

        return None

    def is_automation_request(self, message: str) -> bool:
        """Detect if message is requesting automation"""
        automation_indicators = [
            "help me", "can you", "please", "automate", "open", "navigate",
            "search for", "find", "click", "fill", "submit", "go to",
            "book", "buy", "order", "schedule", "send", "email", "message"
        ]
        return any(indicator in message.lower() for indicator in automation_indicators)

    async def handle_automation_request(self, message: str) -> str:
        """Handle new automation request like Operator"""
        task_result = await self.browser_controller.start_operator_task(message)

        response = f"""🤖 **Task Started - OpenAI Operator Style**

**Your Request:** {task_result['task']}

**🧠 AI Planning:**
{task_result['llm_planning']}

**📋 Execution Plan ({task_result['total_steps']} steps):**
"""

        for i, step in enumerate(task_result['steps'], 1):
            response += f"{i}. {step}\n"

        response += f"""
**🎮 Control Options (Just Like Operator):**
- **"Next step"** - Execute one step at a time
- **"Take control"** - You control, I assist
- **"Continue automation"** - Run all steps automatically
- **"Pause"** - Stop and wait for instructions

**Ready to begin?** Say "next step" or I can start automatically!
"""

        return response

    async def handle_conversational_chat(self, message: str) -> str:
        """Handle conversational chat with local LLM"""
        context = self.get_conversation_context()

        llm_response = await self.browser_controller.agent_connector.chat_with_local_llm(
            message, context
        )

        # Add to conversation memory
        self.conversation_memory.append({
            "timestamp": datetime.now().isoformat(),
            "user": message,
            "ai": llm_response,
            "type": "chat"
        })

        return f"""🧠 **AI Response:**

{llm_response}

**💡 I can also help you with browser automation tasks!**
Just describe what you want to accomplish and I'll break it down and execute it step by step.
"""

    def get_conversation_context(self) -> str:
        """Get conversation context for LLM"""
        if not self.conversation_memory:
            return "New conversation with browser automation AI assistant."

        recent = self.conversation_memory[-3:]  # Last 3 exchanges
        context = "Recent conversation:\n"
        for entry in recent:
            context += f"User: {entry['user']}\nAI: {entry['ai'][:100]}...\n\n"

        return context

    def format_status_response(self, status: Dict[str, Any]) -> str:
        """Format status response like Operator"""
        if not status["current_task"]:
            return """📊 **Status: Ready**

No active automation task.
Ready to help with browser automation or answer questions!

**Connected Services:**
""" + self.format_service_status(status["agent_connections"])

        return f"""📊 **Automation Status**

**Current Task:** {status["current_task"]}
**Progress:** {status["completed_steps"]}/{status["total_steps"]} steps ({status["progress_percentage"]:.1f}%)
**Remaining:** {status["remaining_steps"]} steps

**Control Status:**
- User Control: {"✅ Active" if status["is_user_controlling"] else "❌ AI Control"}
- Automation: {"⏸️ Paused" if status["automation_paused"] else "▶️ Running"}

**Available Actions:**
- {"Resume AI Control" if status["can_resume"] else "Take Control" if status["can_takeover"] else "Continue"}

**Connected Services:**
""" + self.format_service_status(status["agent_connections"])

    def format_service_status(self, connections: Dict[str, bool]) -> str:
        """Format service connection status"""
        status = ""
        for service, connected in connections.items():
            icon = "✅" if connected else "❌"
            status += f"{icon} {service.replace('_', ' ').title()}\n"
        return status

def create_operator_interface():
    """Create OpenAI Operator-like interface"""

    # Initialize the system
    agent_connector = LocalAgentSystemConnector()
    browser_controller = OperatorBrowserController(agent_connector)
    conversation_manager = OperatorConversationManager(browser_controller)

    with gr.Blocks(title="OpenAI Operator Browser AI", theme=gr.themes.Soft()) as demo:

        # Header
        gr.Markdown("# 🤖 OpenAI Operator-Style Browser AI")
        gr.Markdown("**Exact same features as OpenAI's Operator • Connected to your local AI agent system**")

        # Status bar
        with gr.Row():
            status_display = gr.Markdown("🔄 **Initializing connections to local AI agent system...**")

        # Control panel - exactly like Operator
        with gr.Row():
            takeover_btn = gr.Button("🎮 Take Control", size="sm", variant="secondary")
            resume_btn = gr.Button("🤖 Resume AI", size="sm", variant="primary")
            next_step_btn = gr.Button("▶️ Next Step", size="sm")
            pause_btn = gr.Button("⏸️ Pause", size="sm")
            status_btn = gr.Button("📊 Status", size="sm")

        # Main conversation interface
        chatbot = gr.Chatbot(
            value=[["System", f"""🚀 **OpenAI Operator Browser AI Ready!**

**🎯 Exact same capabilities as OpenAI's Operator:**

✅ **Natural Language Automation**
- "Help me book a flight to New York"
- "Find and order office supplies online"
- "Research competitors and compile a report"
- "Schedule a meeting through my calendar"

✅ **Seamless Control Handoff**
- **Take Control**: You control browser, I assist
- **Resume AI**: I take back control and continue
- **Step-by-Step**: Execute one action at a time
- **Full Automation**: Complete tasks automatically

✅ **Connected to Your Local System:**
- 🧠 Local LLM Router: {"✅ Connected" if agent_connector.llm_router else "❌ Not Found"}
- 🤖 Agent Manager: {"✅ Connected" if agent_connector.agent_manager else "❌ Not Found"}
- 🌐 WebRover: {"✅ Connected" if agent_connector.webrover_client else "❌ Not Found"}
- 🎯 UI-TARS: {"✅ Connected" if agent_connector.ui_tars_client else "❌ Not Found"}

**Just describe what you want to accomplish - I'll handle the rest!**"""]],
            height=500,
            show_label=False
        )

        # Input area
        with gr.Row():
            msg = gr.Textbox(
                placeholder="Describe what you want to accomplish (just like with OpenAI's Operator)...",
                container=False,
                scale=7
            )
            send_btn = gr.Button("Send", variant="primary", scale=1)

        # Quick action templates
        with gr.Row():
            gr.Markdown("**Quick Actions:**")
        with gr.Row():
            email_btn = gr.Button("📧 Email Tasks", size="sm")
            research_btn = gr.Button("🔍 Research & Analysis", size="sm")
            shopping_btn = gr.Button("🛒 Online Shopping", size="sm")
            booking_btn = gr.Button("📅 Booking & Scheduling", size="sm")

        # Event handlers
        async def handle_message(message, history):
            return await conversation_manager.process_operator_message(message, history)

        async def handle_next_step():
            result = await browser_controller.execute_next_step()
            if result["status"] == "completed":
                return f"✅ **Task Completed!**\n\n{result['message']}"
            elif result["status"] == "in_progress":
                return f"▶️ **Step {result['step_number']}/{result['total_steps']}**\n\n**Executed:** {result['step']}\n\n**Progress:** {result['progress_percentage']:.1f}%"
            else:
                return f"ℹ️ **Status:** {result['message']}"

        def handle_takeover():
            return browser_controller.takeover_control()

        def handle_resume():
            return browser_controller.resume_ai_control()

        def handle_pause():
            return browser_controller.pause_automation()

        def handle_status():
            status = browser_controller.get_operator_status()
            return conversation_manager.format_status_response(status)

        # Wire up events
        send_btn.click(handle_message, [msg, chatbot], [chatbot, msg])
        msg.submit(handle_message, [msg, chatbot], [chatbot, msg])

        takeover_btn.click(handle_takeover, outputs=status_display)
        resume_btn.click(handle_resume, outputs=status_display)
        next_step_btn.click(handle_next_step, outputs=status_display)
        pause_btn.click(handle_pause, outputs=status_display)
        status_btn.click(handle_status, outputs=status_display)

        # Quick action handlers
        def start_email_task():
            return [["User", "Help me automate my email workflow - check inbox, respond to important emails, and organize by priority"], ["AI", "🤖 Starting email automation task..."]]

        def start_research_task():
            return [["User", "Research my competitors and compile a comprehensive analysis report"], ["AI", "🔍 Starting research and analysis task..."]]

        def start_shopping_task():
            return [["User", "Help me find and order office supplies online"], ["AI", "🛒 Starting online shopping task..."]]

        def start_booking_task():
            return [["User", "Help me schedule a meeting and book a conference room"], ["AI", "📅 Starting booking and scheduling task..."]]

        email_btn.click(start_email_task, outputs=chatbot)
        research_btn.click(start_research_task, outputs=chatbot)
        shopping_btn.click(start_shopping_task, outputs=chatbot)
        booking_btn.click(start_booking_task, outputs=chatbot)

    return demo

def main():
    """Main function"""
    print("🤖 Starting OpenAI Operator-Style Browser AI...")
    print("🔗 Connecting to local AI agent system...")
    print("📍 Will run on http://localhost:7890")

    try:
        demo = create_operator_interface()
        print("✅ OpenAI Operator interface created successfully")

        demo.launch(
            server_name="0.0.0.0",
            server_port=7890,
            share=False,
            inbrowser=True,
            show_error=True,
            quiet=False
        )

    except Exception as e:
        print(f"❌ Error: {e}")
        print("🔧 Trying port 7891...")
        try:
            demo.launch(
                server_name="0.0.0.0",
                server_port=7891,
                share=False,
                inbrowser=True,
                show_error=True
            )
        except Exception as e2:
            print(f"❌ Failed: {e2}")

if __name__ == "__main__":
    main()
