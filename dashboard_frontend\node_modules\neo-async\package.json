{"name": "neo-async", "version": "2.6.2", "description": "Neo-Async is a drop-in replacement for Async, it almost fully covers its functionality and runs faster ", "main": "async.js", "license": "MIT", "keywords": ["async", "util"], "repository": {"type": "git", "url": "**************:suguru03/neo-async.git"}, "homepage": "https://github.com/suguru03/neo-async", "dependencies": {}, "devDependencies": {"aigle": "^1.14.0", "async": "^2.6.0", "benchmark": "^2.1.1", "bluebird": "^3.5.1", "codecov.io": "^0.1.6", "fs-extra": "^4.0.2", "func-comparator": "^0.7.2", "gulp": "^4.0.2", "gulp-bump": "^2.8.0", "gulp-exit": "0.0.2", "gulp-git": "^2.4.2", "gulp-jscs": "^4.0.0", "gulp-mocha": "^4.2.0", "gulp-tag-version": "^1.3.0", "gulp-util": "^3.0.7", "husky": "^1.2.0", "istanbul": "^0.4.3", "jsdoc": "^3.5.5", "jshint": "^2.9.5", "lint-staged": "^8.1.0", "lodash": "^4.16.6", "minimist": "^1.2.0", "mocha": "^3.5.3", "mocha-parallel-executor": "^0.3.0", "mocha.parallel": "^0.15.3", "prettier": "^1.15.2", "require-dir": "^0.3.0"}, "lint-staged": {"*.{js,ts}": ["prettier --write", "git add"]}, "prettier": {"printWidth": 100, "singleQuote": true}, "browser": "async.min.js"}