"""
Abacus DeepAgent implementation for integration with the AI Agent System.
"""
import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from .client import AbacusDeepAgentClient

logger = logging.getLogger(__name__)

class AbacusDeepAgent:
    """
    Abacus DeepAgent implementation that can be used with the AI Agent System.

    This class provides a bridge between the Abacus DeepAgent platform and the
    AI Agent System, allowing DeepAgents to be used as agents within the system.
    """

    def __init__(self,
                 agent_id: Optional[str] = None,
                 name: Optional[str] = None,
                 description: Optional[str] = None,
                 model_type: str = "gpt-4",
                 capabilities: Optional[List[str]] = None,
                 parameters: Optional[Dict] = None,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        Initialize the DeepAgent.

        Args:
            agent_id: ID of an existing agent to connect to
            name: Name of the agent if creating a new one
            description: Description of the agent if creating a new one
            model_type: Type of model to use
            capabilities: List of agent capabilities if creating a new one
            parameters: Additional parameters for the agent if creating a new one
            api_key: API key for authentication
            base_url: Base URL for the API
        """
        self.client = AbacusDeepAgentClient(api_key=api_key, base_url=base_url)
        self.agent_id = agent_id
        self.name = name or "Unnamed DeepAgent"
        self.description = description or "DeepAgent integrated with AI Agent System"
        self.model_type = model_type
        self.status = "initialized"
        self.last_active = None
        self.config = {}
        self._initialization_task = None

        # Create task for async initialization
        self._initialization_task = asyncio.create_task(self._initialize_agent(
            agent_id, name, description, model_type, capabilities, parameters
        ))

    async def _initialize_agent(self,
                               agent_id: Optional[str],
                               name: Optional[str],
                               description: Optional[str],
                               model_type: str,
                               capabilities: Optional[List[str]],
                               parameters: Optional[Dict]):
        """Initialize the agent asynchronously."""
        try:
            if agent_id:
                # Connect to existing agent
                agent_info = await self.client.get_agent(agent_id)
                self.name = agent_info.get("name", name)
                self.description = agent_info.get("description", description)
                self.config = agent_info
                self.status = "connected"
                logger.info(f"Connected to existing DeepAgent {agent_id}")
            elif name and capabilities:
                # Create new agent
                agent_info = await self.client.create_agent(
                    name=name,
                    description=description or "DeepAgent integrated with AI Agent System",
                    model_type=model_type,
                    capabilities=capabilities,
                    parameters=parameters
                )
                self.agent_id = agent_info.get("id")
                self.config = agent_info
                self.status = "created"
                logger.info(f"Created new DeepAgent {self.agent_id}")
            else:
                logger.warning("Neither agent_id nor name+capabilities provided. Agent is in a placeholder state.")
        except Exception as e:
            logger.error(f"Error initializing Abacus DeepAgent: {e}")
            self.status = "error"

    async def wait_for_initialization(self):
        """Wait for agent initialization to complete."""
        if self._initialization_task:
            await self._initialization_task

    async def process_message(self, message: Dict) -> Dict:
        """
        Process a message using this agent.

        Args:
            message: Message to process

        Returns:
            Agent response
        """
        # Wait for initialization if still ongoing
        await self.wait_for_initialization()

        # Check if initialization failed
        if self.status == "error":
            return {
                "content": "Agent initialization failed. Please check the logs.",
                "sender_id": f"abacus_agent_{self.agent_id or 'unknown'}",
                "recipient_id": message.get("sender_id", "unknown"),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "error": "Agent initialization failed",
                    "agent_type": "abacus_deep_agent",
                    "agent_id": self.agent_id
                }
            }

        # Update status and last active time
        self.status = "processing"
        self.last_active = datetime.now()

        try:
            # Extract relevant info from message
            content = message.get("content", "")
            context = message.get("context", {})
            sender = message.get("sender_id", "unknown")

            # Construct input for the agent
            input_data = {
                "message": content,
                "sender": sender
            }

            # Run the agent
            result = await self.client.run_agent(
                agent_id=self.agent_id,
                input_data=input_data,
                context=context
            )

            # Update status
            self.status = "idle"

            # Format response
            response = {
                "content": result.get("output", {}),
                "sender_id": f"abacus_agent_{self.agent_id}",
                "recipient_id": sender,
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "agent_type": "abacus_deep_agent",
                    "agent_id": self.agent_id,
                    "raw_response": result
                }
            }

            return response
        except Exception as e:
            logger.exception(f"Error processing message with Abacus DeepAgent: {e}")
            self.status = "error"

            # Return error response
            return {
                "content": f"Error processing your request: {str(e)}",
                "sender_id": f"abacus_agent_{self.agent_id or 'unknown'}",
                "recipient_id": message.get("sender_id", "unknown"),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "error": str(e),
                    "agent_type": "abacus_deep_agent",
                    "agent_id": self.agent_id
                }
            }

    async def add_knowledge(self, knowledge_data: Dict) -> Dict:
        """
        Add knowledge to the agent.

        Args:
            knowledge_data: Knowledge data

        Returns:
            Result of adding the knowledge
        """
        # Wait for initialization if still ongoing
        await self.wait_for_initialization()

        try:
            result = await self.client.add_knowledge(self.agent_id, knowledge_data)
            return {"success": True, "data": result}
        except Exception as e:
            logger.exception(f"Error adding knowledge to Abacus DeepAgent: {e}")
            return {"success": False, "error": str(e)}

    async def add_tool(self, tool_data: Dict) -> Dict:
        """
        Add a tool to the agent.

        Args:
            tool_data: Tool data

        Returns:
            Result of adding the tool
        """
        # Wait for initialization if still ongoing
        await self.wait_for_initialization()

        try:
            result = await self.client.add_tool(self.agent_id, tool_data)
            return {"success": True, "data": result}
        except Exception as e:
            logger.exception(f"Error adding tool to Abacus DeepAgent: {e}")
            return {"success": False, "error": str(e)}

    async def train(self, examples: List[Dict]) -> Dict:
        """
        Train the agent with examples.

        Args:
            examples: Training examples

        Returns:
            Training result
        """
        # Wait for initialization if still ongoing
        await self.wait_for_initialization()

        try:
            result = await self.client.train_agent(self.agent_id, examples)
            return {"success": True, "data": result}
        except Exception as e:
            logger.exception(f"Error training Abacus DeepAgent: {e}")
            return {"success": False, "error": str(e)}
