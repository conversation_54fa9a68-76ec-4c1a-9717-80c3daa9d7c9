# babel-plugin-polyfill-corejs2

## Install

Using npm:

```sh
npm install --save-dev babel-plugin-polyfill-corejs2
```

or using yarn:

```sh
yarn add babel-plugin-polyfill-corejs2 --dev
```

## Usage

Add this plugin to your Babel configuration:

```json
{
  "plugins": [["polyfill-corejs2", { "method": "usage-global" }]]
}
```

This package supports the `usage-pure`, `usage-global`, and `entry-global` methods.
When `entry-global` is used, it replaces imports to `core-js`.
