"""
Web interface for the Multi-Agent AI System.
"""
import asyncio
from typing import Dict, List, Optional, Any
import uvicorn
from fastapi import FastAP<PERSON>, Depends, HTTPException, status, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from pydantic import BaseModel
import jwt
from datetime import datetime, timedelta
import os
import json
import config
from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_manager import AgentManager
from dotenv import load_dotenv
import httpx

# Load environment variables from .env file
load_dotenv()

# Import integrations
from integrations.google_ai_studio import GoogleAIStudioClient
from integrations.ondemand_io import OnDemandIOClient, DeploymentManager

# Import Google Cloud integration
try:
    from integrations.google_cloud import GoogleCloudIntegration
    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_AVAILABLE = False

# Import Deep Agent integration
try:
    from integrations.deep_agent import DeepAgentClient, DeepAgent
    DEEP_AGENT_AVAILABLE = True
except ImportError:
    DEEP_AGENT_AVAILABLE = False

# Cloudflare tunnel info
CLOUDFLARE_TUNNEL_NAME = os.getenv("CLOUDFLARE_TUNNEL_NAME", "ai-agent-dashboard")

# Set up logger
logger = setup_logger("web_interface")

# Create FastAPI app
app = FastAPI(
    title=config.SYSTEM_NAME,
    description="Web interface for the Multi-Agent AI System",
    version=config.VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates setup
templates = Jinja2Templates(directory="templates")

# Global references to managers
state_manager_ref = None
agent_manager_ref = None

# Authentication models
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

# Authentication functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(seconds=config.SECURITY_CONFIG["jwt_expiration"])
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode,
        config.SECURITY_CONFIG["jwt_secret"],
        algorithm=config.SECURITY_CONFIG["jwt_algorithm"]
    )
    return encoded_jwt

async def get_current_user(token: str):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token,
            config.SECURITY_CONFIG["jwt_secret"],
            algorithms=[config.SECURITY_CONFIG["jwt_algorithm"]]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise credentials_exception
    return token_data

# API routes
@app.get("/")
async def root(request: Request):
    """Root endpoint that redirects to the dashboard."""
    return {"message": f"Welcome to {config.SYSTEM_NAME} API"}

@app.get("/api/status")
async def get_status():
    """Get system status."""
    if state_manager_ref is None:
        return {"status": "initializing"}

    system_state = await state_manager_ref.get_state("system")
    return {
        "status": "running",
        "version": config.VERSION,
        "start_time": system_state.get("start_time"),
        "uptime_seconds": (
            datetime.now() -
            datetime.fromisoformat(system_state.get("start_time"))
        ).total_seconds(),
    }

@app.get("/api/agents")
async def get_agents():
    """Get all agents and their status."""
    if agent_manager_ref is None:
        return {"agents": []}

    agents = agent_manager_ref.get_all_agents()
    agent_info = {}

    for agent_id, agent in agents.items():
        agent_info[agent_id] = {
            "name": agent.name,
            "description": agent.description,
            "status": agent.status,
            "last_active": agent.last_active.isoformat() if agent.last_active else None,
        }

    return {"agents": agent_info}

@app.get("/api/agents/{agent_id}")
async def get_agent(agent_id: str):
    """Get information about a specific agent."""
    if agent_manager_ref is None:
        raise HTTPException(status_code=503, detail="Agent manager not available")

    agent = agent_manager_ref.get_agent(agent_id)
    if agent is None:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

    return {
        "id": agent_id,
        "name": agent.name,
        "description": agent.description,
        "status": agent.status,
        "last_active": agent.last_active.isoformat() if agent.last_active else None,
        "config": agent.config,
    }

@app.post("/api/agents/{agent_id}/message")
async def send_message_to_agent(agent_id: str, message: Dict):
    """Send a message to a specific agent."""
    if agent_manager_ref is None:
        raise HTTPException(status_code=503, detail="Agent manager not available")

    agent = agent_manager_ref.get_agent(agent_id)
    if agent is None:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

    # Add sender and recipient to message
    message["sender_id"] = "web_interface"
    message["recipient_id"] = agent_id
    message["timestamp"] = datetime.now().isoformat()

    # Send message
    await agent_manager_ref.send_message(message)

    return {"status": "message sent"}

@app.post("/api/agent/hitl-continue")
async def hitl_continue():
    # Resume the paused agent workflow
    if agent_manager_ref is not None:
        await agent_manager_ref.resume_hitl()
    return {"status": "resumed"}

# Models for integration requests
class PromptRequest(BaseModel):
    prompt: str
    model_name: str = "gemini-pro"
    temperature: float = 0.7
    max_output_tokens: int = 800

class DeploymentRequest(BaseModel):
    app_name: str
    repo_url: str
    branch: str = "main"
    start_command: str = "python app.py"
    environment_variables: Dict[str, str] = None
    port: int = 8000

class RestAPIAgentRequest(BaseModel):
    name: str
    endpoint_url: str
    description: Optional[str] = None

# Google Cloud models
class GCSUploadRequest(BaseModel):
    bucket_name: str
    source_file_path: str
    destination_blob_name: str = None

class GCSDownloadRequest(BaseModel):
    bucket_name: str
    source_blob_name: str
    destination_file_path: str

class GCSListFilesRequest(BaseModel):
    bucket_name: str
    prefix: Optional[str] = None

# Deep Agent models
class DeepAgentCreateRequest(BaseModel):
    name: str
    capabilities: List[str]
    description: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

class DeepAgentRunRequest(BaseModel):
    input_data: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    stream: bool = False

class DeepAgentAddToolRequest(BaseModel):
    tool_data: Dict[str, Any]

class DeepAgentTrainRequest(BaseModel):
    examples: List[Dict[str, Any]]

# API routes for external integrations
@app.post("/api/ai-studio/generate")
async def generate_from_prompt(request: PromptRequest):
    """Generate text from a prompt using Google AI Studio."""
    try:
        client = GoogleAIStudioClient()
        result = client.generate_text(
            prompt=request.prompt,
            model_name=request.model_name,
            temperature=request.temperature,
            max_output_tokens=request.max_output_tokens
        )
        return {"result": result}
    except Exception as e:
        logger.exception(f"Error generating text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ai-studio/export-code")
async def export_code_snippet(request: PromptRequest, language: str = "python"):
    """Export a code snippet for using a prompt in application code."""
    try:
        client = GoogleAIStudioClient()
        snippet = client.export_code_snippet(
            prompt=request.prompt,
            model_name=request.model_name,
            language=language,
            temperature=request.temperature
        )
        return {"code_snippet": snippet}
    except Exception as e:
        logger.exception(f"Error exporting code snippet: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ondemand/applications")
async def list_applications():
    """List all applications on OnDemand.io."""
    try:
        client = OnDemandIOClient()
        applications = client.list_applications()
        return {"applications": applications}
    except Exception as e:
        logger.exception(f"Error listing applications: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ondemand/deploy")
async def deploy_application(request: DeploymentRequest):
    """Deploy an application to OnDemand.io."""
    try:
        deployment_manager = DeploymentManager()
        result = deployment_manager.deploy_from_git(
            app_name=request.app_name,
            repo_url=request.repo_url,
            branch=request.branch,
            start_command=request.start_command,
            environment_variables=request.environment_variables,
            port=request.port
        )
        return {"deployment": result}
    except Exception as e:
        logger.exception(f"Error deploying application: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ondemand/create-rest-agent")
async def create_rest_agent(request: RestAPIAgentRequest):
    """Create a REST API agent on OnDemand.io."""
    try:
        deployment_manager = DeploymentManager()
        result = deployment_manager.create_rest_api_agent(
            name=request.name,
            endpoint_url=request.endpoint_url,
            description=request.description
        )
        return {"agent": result}
    except Exception as e:
        logger.exception(f"Error creating REST API agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoints
@app.websocket("/ws/browser-feed")
async def browser_feed(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            # Get the latest screenshot from the agent manager
            if agent_manager_ref is not None:
                screenshot_data = await agent_manager_ref.get_latest_screenshot_base64()
                await websocket.send_json({"type": "screenshot", "data": screenshot_data})
            await asyncio.sleep(1)  # Adjust for desired frame rate
    except WebSocketDisconnect:
        logger.info("Browser feed WebSocket disconnected")
    except Exception as e:
        logger.exception(f"Error in browser feed WebSocket: {e}")

@app.websocket("/ws/agent-events")
async def agent_events(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            # Send agent events to connected clients
            # Example: send {"type": "hitl_pause", "message": "Agent paused for user input"}
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        logger.info("Agent events WebSocket disconnected")
    except Exception as e:
        logger.exception(f"Error in agent events WebSocket: {e}")

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.exception(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": str(exc)},
    )

# Google Cloud integration endpoints
@app.get("/api/gcp/buckets")
async def list_gcp_buckets():
    """List all Google Cloud Storage buckets."""
    if not GOOGLE_CLOUD_AVAILABLE:
        raise HTTPException(status_code=501, detail="Google Cloud integration is not available")

    try:
        gcp = GoogleCloudIntegration()
        buckets = gcp.list_buckets()
        return {"buckets": buckets}
    except Exception as e:
        logger.exception(f"Error listing GCP buckets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gcp/upload")
async def upload_to_gcs(request: GCSUploadRequest):
    """Upload a file to Google Cloud Storage."""
    if not GOOGLE_CLOUD_AVAILABLE:
        raise HTTPException(status_code=501, detail="Google Cloud integration is not available")

    try:
        gcp = GoogleCloudIntegration()

        # If destination blob name is not provided, use the source file name
        if not request.destination_blob_name:
            import os
            request.destination_blob_name = os.path.basename(request.source_file_path)

        success = gcp.upload_file(
            request.bucket_name,
            request.source_file_path,
            request.destination_blob_name
        )

        if success:
            return {"status": "success", "message": f"File uploaded to gs://{request.bucket_name}/{request.destination_blob_name}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to upload file")
    except Exception as e:
        logger.exception(f"Error uploading to GCS: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gcp/download")
async def download_from_gcs(request: GCSDownloadRequest):
    """Download a file from Google Cloud Storage."""
    if not GOOGLE_CLOUD_AVAILABLE:
        raise HTTPException(status_code=501, detail="Google Cloud integration is not available")

    try:
        gcp = GoogleCloudIntegration()
        success = gcp.download_file(
            request.bucket_name,
            request.source_blob_name,
            request.destination_file_path
        )

        if success:
            return {"status": "success", "message": f"File downloaded to {request.destination_file_path}"}
        else:
            raise HTTPException(status_code=500, detail="Failed to download file")
    except Exception as e:
        logger.exception(f"Error downloading from GCS: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gcp/files")
async def list_gcs_files(request: GCSListFilesRequest):
    """List files in a Google Cloud Storage bucket."""
    if not GOOGLE_CLOUD_AVAILABLE:
        raise HTTPException(status_code=501, detail="Google Cloud integration is not available")

    try:
        gcp = GoogleCloudIntegration()
        files = gcp.list_files(request.bucket_name, request.prefix)
        return {"files": files}
    except Exception as e:
        logger.exception(f"Error listing GCS files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Deep Agent endpoints
@app.get("/api/deep-agents")
async def list_deep_agents():
    """List all Deep Agents."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        agents = client.list_agents()
        return {"agents": agents}
    except Exception as e:
        logger.exception(f"Error listing Deep Agents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/deep-agents")
async def create_deep_agent(request: DeepAgentCreateRequest):
    """Create a new Deep Agent."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        agent = client.create_agent(
            name=request.name,
            capabilities=request.capabilities,
            description=request.description,
            parameters=request.parameters
        )

        # Register with the agent manager if it's available
        if agent_manager_ref is not None:
            deep_agent = DeepAgent(agent_id=agent.get("id"))
            await agent_manager_ref.register_agent(f"deep_{agent.get('id')}", deep_agent)

        return {"agent": agent}
    except Exception as e:
        logger.exception(f"Error creating Deep Agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/deep-agents/{agent_id}")
async def get_deep_agent(agent_id: str):
    """Get information about a Deep Agent."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        agent = client.get_agent(agent_id)
        return {"agent": agent}
    except Exception as e:
        logger.exception(f"Error getting Deep Agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/deep-agents/{agent_id}")
async def delete_deep_agent(agent_id: str):
    """Delete a Deep Agent."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        result = client.delete_agent(agent_id)

        # Remove from agent manager if it's available
        if agent_manager_ref is not None:
            await agent_manager_ref.unregister_agent(f"deep_{agent_id}")

        return {"result": result}
    except Exception as e:
        logger.exception(f"Error deleting Deep Agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/deep-agents/{agent_id}/run")
async def run_deep_agent(agent_id: str, request: DeepAgentRunRequest):
    """Run a Deep Agent with given input."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        result = client.run_agent(
            agent_id=agent_id,
            input_data=request.input_data,
            context=request.context,
            tools=request.tools,
            stream=request.stream
        )

        # Handle streamed response
        if request.stream:
            from fastapi.responses import StreamingResponse

            async def stream_generator():
                for line in result.iter_lines():
                    if line:
                        yield f"data: {line.decode('utf-8')}\n\n"

            return StreamingResponse(
                stream_generator(),
                media_type="text/event-stream"
            )

        return {"result": result}
    except Exception as e:
        logger.exception(f"Error running Deep Agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/deep-agents/{agent_id}/tools")
async def add_tool_to_deep_agent(agent_id: str, request: DeepAgentAddToolRequest):
    """Add a tool to a Deep Agent."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        result = client.add_tool(agent_id, request.tool_data)
        return {"result": result}
    except Exception as e:
        logger.exception(f"Error adding tool to Deep Agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/deep-agents/{agent_id}/train")
async def train_deep_agent(agent_id: str, request: DeepAgentTrainRequest):
    """Train a Deep Agent with examples."""
    if not DEEP_AGENT_AVAILABLE:
        raise HTTPException(status_code=501, detail="Deep Agent integration is not available")

    try:
        client = DeepAgentClient()
        result = client.train_agent(agent_id, request.examples)
        return {"result": result}
    except Exception as e:
        logger.exception(f"Error training Deep Agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Cloudflare tunnel endpoints
@app.get("/api/tunnel/info")
async def get_tunnel_info():
    """Get information about the current Cloudflare tunnel."""
    import subprocess

    try:
        # Try to get tunnel info using cloudflared command
        result = subprocess.run(
            ["cloudflared", "tunnel", "info", CLOUDFLARE_TUNNEL_NAME],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            return {
                "status": "error",
                "message": "Failed to get tunnel information",
                "error": result.stderr
            }

        # Extract the public URL if available
        import re
        url_match = re.search(r"https://[^\s]+", result.stdout)
        public_url = url_match.group(0) if url_match else None

        return {
            "status": "success",
            "tunnel_name": CLOUDFLARE_TUNNEL_NAME,
            "public_url": public_url,
            "raw_info": result.stdout
        }
    except Exception as e:
        logger.exception(f"Error getting tunnel info: {e}")
        return {
            "status": "error",
            "message": str(e),
            "tunnel_name": CLOUDFLARE_TUNNEL_NAME
        }

# MidScene integration
@app.post("/api/midscene/connect")
async def connect_to_midscene():
    """Bridge the connection to MidScene."""
    try:
        # Specific working model name and keys
        midscene_base_url = "https://gemini.googleapis.com/v1"
        midscene_api_key = "********************************************************************************************************************************************************************"
        gemini_api_key = "AIzaSyBlM3-NO7PISCYYg6GWyrfUhaQr6R3dhhk"
        model_name = "gemini-pro"
        use_gemini = True

        payload = {
            "model": model_name,
            "use_gemini": use_gemini  # Explicitly set to True
        }

        headers = {
            "Authorization": f"Bearer {gemini_api_key if use_gemini else midscene_api_key}",
            "Content-Type": "application/json"
        }

        # Fallback to local Ollama model if needed
        if not use_gemini:
            logger.warning("Gemini not available, falling back to local Ollama model.")
            payload["model"] = "ollama-local"

        # Simulate a connection request (replace with actual MidScene API endpoint if available)
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{midscene_base_url}/connect", json=payload, headers=headers)
            response.raise_for_status()

        # Debugging: Verify Chrome extension connectivity
        logger.debug("Verifying Chrome extension connectivity...")
        extension_check_url = "http://localhost:9222/json"  # Default Chrome DevTools URL
        try:
            extension_response = await client.get(extension_check_url)
            extension_response.raise_for_status()
            logger.info("Chrome extension is active and responding.")
        except Exception as ext_err:
            logger.error(f"Failed to verify Chrome extension: {ext_err}")
            raise HTTPException(status_code=500, detail="Chrome extension verification failed.")

        return {"status": "success", "message": "Connected to MidScene and Chrome extension verified successfully."}
    except Exception as e:
        logger.exception(f"Error connecting to MidScene: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# MidScene Bridge Mode
@app.post("/api/midscene/bridge")
async def enable_bridge_mode():
    """Enable Bridge Mode for MidScene SDK to control the browser."""
    try:
        # Example: Start listening for connections
        logger.info("Bridge Mode: Listening for connection...")

        # Replace with actual SDK integration logic
        midscene_base_url = os.getenv("OPENAI_BASE_URL")
        midscene_api_key = os.getenv("OPENAI_API_KEY")
        model_name = os.getenv("MIDSCENE_MODEL_NAME")
        use_gemini = os.getenv("MIDSCENE_USE_GEMINI")

        if not all([midscene_base_url, midscene_api_key, model_name, use_gemini]):
            raise ValueError("Missing required MidScene configuration.")

        payload = {
            "model": model_name,
            "use_gemini": bool(int(use_gemini))
        }

        headers = {
            "Authorization": f"Bearer {midscene_api_key}",
            "Content-Type": "application/json"
        }

        # Interact with the Chrome extension via MidScene Bridge Mode
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{midscene_base_url}/bridge", json=payload, headers=headers)
            response.raise_for_status()

        bridge_status = {
            "status": "connected",
            "log": "Bridge mode successfully enabled"
        }

        return {"status": "success", "bridge_status": bridge_status}
    except Exception as e:
        logger.exception(f"Error enabling Bridge Mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Serve static files in production
if not config.DEBUG:
    app.mount("/static", StaticFiles(directory="static"), name="static")

async def start_web_interface(
    agent_manager: AgentManager,
    state_manager: StateManager,
    host: str = "0.0.0.0",
    port: int = 8000,
    debug: bool = False
):
    """
    Start the web interface.

    Args:
        agent_manager (AgentManager): Agent manager instance
        state_manager (StateManager): State manager instance
        host (str): Host to bind to
        port (int): Port to bind to
        debug (bool): Whether to run in debug mode
    """
    global agent_manager_ref, state_manager_ref

    # Set global references
    agent_manager_ref = agent_manager
    state_manager_ref = state_manager

    # Configure Uvicorn server
    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level="debug" if debug else "info",
        reload=debug,
    )

    # Start server
    server = uvicorn.Server(config)
    await server.serve()

    # Reset global references on shutdown
    agent_manager_ref = None
    state_manager_ref = None
