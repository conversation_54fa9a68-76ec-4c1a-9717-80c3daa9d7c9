#!/usr/bin/env python3
"""
Terminal-Connected Dashboard System
Complete integration between GUI, terminal, cloud systems, and all components
"""
import os
import sys
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import subprocess
import time
import json
from pathlib import Path
import logging
import queue
import io
import contextlib
from datetime import datetime
import webbrowser
import psutil
import socket

# Terminal Output Redirection Class
class TerminalRedirector:
    """Redirects stdout/stderr to both terminal and GUI"""
    def __init__(self, text_widget, original_stream):
        self.text_widget = text_widget
        self.original_stream = original_stream
        self.queue = queue.Queue()

    def write(self, text):
        # Write to original stream (terminal)
        self.original_stream.write(text)
        self.original_stream.flush()

        # Queue for GUI update
        self.queue.put(text)

    def flush(self):
        self.original_stream.flush()

class TerminalConnectedDashboard:
    """
    Advanced Dashboard with full terminal integration and cloud connectivity
    """

    def __init__(self):
        # Setup environment variables for better compatibility
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUNBUFFERED'] = '1'
        if sys.platform == 'win32':
            os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'

        # Initialize logging with both file and console output
        self.setup_logging()

        # Create GUI
        self.root = tk.Tk()
        self.root.title("AI Agent System - Terminal Connected Dashboard")
        self.root.geometry("1600x1000")
        self.root.minsize(1200, 800)

        # Component tracking
        self.components = {
            "Web Interface": {
                "script": "run_web_interface.py",
                "port": 8000,
                "status": "Stopped",
                "process": None,
                "url": "http://localhost:8000"
            },
            "UI-TARS": {
                "script": "ui_tars/main.py",
                "port": 8080,
                "status": "Stopped",
                "process": None,
                "url": "http://localhost:8080"
            },
            "Jarvis Interface": {
                "script": "start_jarvis_with_alphaevolve.py",
                "port": None,
                "status": "Stopped",
                "process": None,
                "url": None
            },
            "System Monitor": {
                "script": "monitor.py",
                "port": None,
                "status": "Stopped",
                "process": None,
                "url": None
            },
            "Borg Cluster": {
                "script": "run_borg_cluster.py",
                "port": None,
                "status": "Stopped",
                "process": None,
                "url": None
            },
            "AlphaEvolve": {
                "script": "alpha_evolve_monitor.py",
                "port": None,
                "status": "Stopped",
                "process": None,
                "url": None
            },
            "Unified Dashboard": {
                "script": "run_unified_dashboard.py",
                "port": 5000,
                "status": "Stopped",
                "process": None,
                "url": "http://localhost:5000"
            }
        }

        # Setup GUI
        self.setup_gui()

        # Setup terminal redirection
        self.setup_terminal_redirection()

        # Start status monitoring
        self.start_monitoring()

        self.log_message("🚀 Terminal Connected Dashboard initialized")
        self.log_message("✅ All systems ready for integration")

    def setup_logging(self):
        """Setup comprehensive logging system"""
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Setup logging configuration
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_dir / "dashboard.log", encoding='utf-8'),
                logging.FileHandler(log_dir / f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log", encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger("dashboard")

    def setup_gui(self):
        """Setup the complete GUI interface"""
        # Create notebook for tabbed interface
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Main Dashboard Tab
        self.main_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.main_frame, text="🎮 Main Dashboard")

        # Terminal Output Tab
        self.terminal_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.terminal_frame, text="💻 Terminal Output")

        # System Monitor Tab
        self.monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.monitor_frame, text="📊 System Monitor")

        # Cloud Integration Tab
        self.cloud_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.cloud_frame, text="☁️ Cloud Integration")

        # Setup each tab
        self.setup_main_dashboard()
        self.setup_terminal_tab()
        self.setup_monitor_tab()
        self.setup_cloud_tab()

    def setup_main_dashboard(self):
        """Setup main dashboard controls"""
        # Title
        title_label = ttk.Label(self.main_frame, text="🚀 AI Agent System Dashboard",
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(10, 20))

        # Control buttons frame
        control_frame = ttk.LabelFrame(self.main_frame, text="System Control", padding="15")
        control_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="🚀 Start All Components",
                  command=self.start_all_components).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="⛔ Stop All Components",
                  command=self.stop_all_components).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 Refresh Status",
                  command=self.refresh_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🌐 Open Web Browser",
                  command=self.open_all_web_interfaces).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📋 System Report",
                  command=self.generate_system_report).pack(side=tk.LEFT, padx=5)

        # Components frame with scrollable list
        components_frame = ttk.LabelFrame(self.main_frame, text="Components Management", padding="15")
        components_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Create treeview for components
        self.tree = ttk.Treeview(components_frame, columns=("Status", "Port", "PID", "Actions"), show="tree headings")
        self.tree.heading("#0", text="Component")
        self.tree.heading("Status", text="Status")
        self.tree.heading("Port", text="Port")
        self.tree.heading("PID", text="PID")
        self.tree.heading("Actions", text="Actions")

        # Configure column widths
        self.tree.column("#0", width=200)
        self.tree.column("Status", width=100)
        self.tree.column("Port", width=80)
        self.tree.column("PID", width=80)
        self.tree.column("Actions", width=150)

        # Add scrollbar to treeview
        tree_scroll = ttk.Scrollbar(components_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=tree_scroll.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind events
        self.tree.bind("<Double-1>", self.on_component_double_click)
        self.tree.bind("<Button-3>", self.on_component_right_click)

        # Populate component tree
        self.populate_component_tree()

    def setup_terminal_tab(self):
        """Setup terminal output tab"""
        # Terminal output frame
        terminal_frame = ttk.LabelFrame(self.terminal_frame, text="Live Terminal Output", padding="10")
        terminal_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Terminal text widget
        self.terminal_text = scrolledtext.ScrolledText(
            terminal_frame,
            wrap=tk.WORD,
            height=30,
            bg="black",
            fg="green",
            font=("Consolas", 10),
            insertbackground="green"
        )
        self.terminal_text.pack(fill=tk.BOTH, expand=True)

        # Terminal input frame
        input_frame = ttk.Frame(self.terminal_frame)
        input_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(input_frame, text="Command:").pack(side=tk.LEFT)
        self.command_entry = ttk.Entry(input_frame)
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.command_entry.bind("<Return>", self.execute_command)

        ttk.Button(input_frame, text="Execute", command=self.execute_command).pack(side=tk.RIGHT)
        ttk.Button(input_frame, text="Clear", command=self.clear_terminal).pack(side=tk.RIGHT, padx=5)

    def setup_monitor_tab(self):
        """Setup system monitoring tab"""
        # System info frame
        info_frame = ttk.LabelFrame(self.monitor_frame, text="System Information", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        # Create system info labels
        self.cpu_label = ttk.Label(info_frame, text="CPU: --")
        self.cpu_label.pack(anchor=tk.W)

        self.memory_label = ttk.Label(info_frame, text="Memory: --")
        self.memory_label.pack(anchor=tk.W)

        self.disk_label = ttk.Label(info_frame, text="Disk: --")
        self.disk_label.pack(anchor=tk.W)

        # Network connections frame
        network_frame = ttk.LabelFrame(self.monitor_frame, text="Network Connections", padding="10")
        network_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Network connections tree
        self.network_tree = ttk.Treeview(network_frame, columns=("Local", "Remote", "Status"), show="tree headings")
        self.network_tree.heading("#0", text="PID")
        self.network_tree.heading("Local", text="Local Address")
        self.network_tree.heading("Remote", text="Remote Address")
        self.network_tree.heading("Status", text="Status")

        self.network_tree.pack(fill=tk.BOTH, expand=True)

    def setup_cloud_tab(self):
        """Setup cloud integration tab"""
        # GitHub integration
        github_frame = ttk.LabelFrame(self.cloud_frame, text="GitHub Integration", padding="10")
        github_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(github_frame, text="🐙 Open GitHub Repository",
                  command=self.open_github).pack(side=tk.LEFT, padx=5)
        ttk.Button(github_frame, text="📥 Pull Latest Changes",
                  command=self.git_pull).pack(side=tk.LEFT, padx=5)
        ttk.Button(github_frame, text="📤 Push Changes",
                  command=self.git_push).pack(side=tk.LEFT, padx=5)

        # HuggingFace integration
        hf_frame = ttk.LabelFrame(self.cloud_frame, text="HuggingFace Integration", padding="10")
        hf_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(hf_frame, text="🤗 Open HuggingFace",
                  command=self.open_huggingface).pack(side=tk.LEFT, padx=5)
        ttk.Button(hf_frame, text="📦 Install Transformers",
                  command=self.install_transformers).pack(side=tk.LEFT, padx=5)

        # Reddit integration
        reddit_frame = ttk.LabelFrame(self.cloud_frame, text="Reddit Integration", padding="10")
        reddit_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(reddit_frame, text="🔴 Open Reddit",
                  command=self.open_reddit).pack(side=tk.LEFT, padx=5)

        # Cloud status
        status_frame = ttk.LabelFrame(self.cloud_frame, text="Cloud Status", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.cloud_status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, height=10)
        self.cloud_status_text.pack(fill=tk.BOTH, expand=True)

    def setup_terminal_redirection(self):
        """Setup terminal output redirection to GUI"""
        # Create redirectors
        self.stdout_redirector = TerminalRedirector(self.terminal_text, sys.stdout)
        self.stderr_redirector = TerminalRedirector(self.terminal_text, sys.stderr)

        # Redirect streams
        sys.stdout = self.stdout_redirector
        sys.stderr = self.stderr_redirector

        # Start queue processing
        self.process_queue()

    def process_queue(self):
        """Process output queue and update GUI"""
        try:
            # Process stdout queue
            while True:
                try:
                    text = self.stdout_redirector.queue.get_nowait()
                    self.terminal_text.insert(tk.END, text)
                    self.terminal_text.see(tk.END)
                except queue.Empty:
                    break

            # Process stderr queue
            while True:
                try:
                    text = self.stderr_redirector.queue.get_nowait()
                    self.terminal_text.insert(tk.END, text)
                    self.terminal_text.see(tk.END)
                except queue.Empty:
                    break

        except Exception as e:
            pass  # Ignore errors during queue processing

        # Schedule next update
        self.root.after(100, self.process_queue)

    def populate_component_tree(self):
        """Populate the component tree with current data"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Add components
        for name, info in self.components.items():
            pid = info['process'].pid if info['process'] else "N/A"
            port = info['port'] if info['port'] else "N/A"

            self.tree.insert("", "end", text=name, values=(
                info['status'],
                port,
                pid,
                "Start/Stop/Open"
            ))

    def refresh_status(self):
        """Refresh component status"""
        self.log_message("🔄 Refreshing component status...")

        for name, info in self.components.items():
            if info['process']:
                # Check if process is still running
                try:
                    if info['process'].poll() is None:
                        info['status'] = "Running"
                    else:
                        info['status'] = "Stopped"
                        info['process'] = None
                except:
                    info['status'] = "Error"
                    info['process'] = None
            else:
                info['status'] = "Stopped"

        self.populate_component_tree()
        self.update_system_info()
        self.log_message("✅ Status refresh completed")

    def start_all_components(self):
        """Start all components in sequence"""
        self.log_message("🚀 Starting all components...")

        startup_order = [
            "Borg Cluster",
            "AlphaEvolve",
            "System Monitor",
            "UI-TARS",
            "Web Interface",
            "Jarvis Interface",
            "Unified Dashboard"
        ]

        for component in startup_order:
            if component in self.components:
                self.start_component(component)
                time.sleep(2)  # Give time between starts

        self.log_message("🎉 All components startup sequence completed!")

    def stop_all_components(self):
        """Stop all running components"""
        self.log_message("⛔ Stopping all components...")

        for name in self.components:
            self.stop_component(name)

        self.log_message("🛑 All components stopped")

    def start_component(self, name):
        """Start a specific component"""
        if name not in self.components:
            self.log_message(f"❌ Unknown component: {name}")
            return

        info = self.components[name]

        if info['process'] and info['process'].poll() is None:
            self.log_message(f"⚠️ {name} is already running")
            return

        self.log_message(f"🔄 Starting {name}...")

        try:
            script_path = info['script']
            if not os.path.exists(script_path):
                self.log_message(f"❌ Script not found: {script_path}")
                return

            # Start the process
            process = subprocess.Popen(
                [sys.executable, script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            info['process'] = process
            info['status'] = "Running"

            self.log_message(f"✅ {name} started successfully (PID: {process.pid})")

            # Start thread to monitor process output
            threading.Thread(
                target=self.monitor_process_output,
                args=(name, process),
                daemon=True
            ).start()

        except Exception as e:
            self.log_message(f"❌ Error starting {name}: {str(e)}")
            info['status'] = "Error"

        self.populate_component_tree()

    def stop_component(self, name):
        """Stop a specific component"""
        if name not in self.components:
            return

        info = self.components[name]

        if not info['process'] or info['process'].poll() is not None:
            self.log_message(f"⚠️ {name} is not running")
            info['status'] = "Stopped"
            info['process'] = None
            self.populate_component_tree()
            return

        self.log_message(f"🔄 Stopping {name}...")

        try:
            process = info['process']
            process.terminate()

            # Wait for graceful shutdown
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                self.log_message(f"🔪 {name} force killed")

            info['status'] = "Stopped"
            info['process'] = None
            self.log_message(f"⏹️ {name} stopped successfully")

        except Exception as e:
            self.log_message(f"❌ Error stopping {name}: {str(e)}")

        self.populate_component_tree()

    def monitor_process_output(self, name, process):
        """Monitor process output and display in terminal"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(f"[{name}] {line.strip()}")
        except Exception as e:
            print(f"[{name}] Output monitoring error: {str(e)}")

    def on_component_double_click(self, event):
        """Handle double-click on component"""
        item = self.tree.selection()[0]
        component = self.tree.item(item, "text")

        info = self.components[component]

        if info['status'] == "Running":
            self.stop_component(component)
        else:
            self.start_component(component)

    def on_component_right_click(self, event):
        """Handle right-click on component"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            component = self.tree.item(item, "text")

            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="Start", command=lambda: self.start_component(component))
            context_menu.add_command(label="Stop", command=lambda: self.stop_component(component))
            context_menu.add_command(label="Restart", command=lambda: self.restart_component(component))

            if self.components[component]['url']:
                context_menu.add_separator()
                context_menu.add_command(label="Open in Browser",
                                       command=lambda: webbrowser.open(self.components[component]['url']))

            context_menu.tk_popup(event.x_root, event.y_root)

    def restart_component(self, name):
        """Restart a component"""
        self.stop_component(name)
        time.sleep(1)
        self.start_component(name)

    def execute_command(self, event=None):
        """Execute a terminal command"""
        command = self.command_entry.get().strip()
        if not command:
            return

        self.command_entry.delete(0, tk.END)
        self.log_message(f"💻 Executing: {command}")

        def run_command():
            try:
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )

                if result.stdout:
                    print(f"Output:\n{result.stdout}")
                if result.stderr:
                    print(f"Error:\n{result.stderr}")

                print(f"Exit code: {result.returncode}")

            except Exception as e:
                print(f"Command execution error: {str(e)}")

        threading.Thread(target=run_command, daemon=True).start()

    def clear_terminal(self):
        """Clear terminal output"""
        self.terminal_text.delete(1.0, tk.END)

    def update_system_info(self):
        """Update system information"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_label.config(text=f"CPU: {cpu_percent:.1f}%")

            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_label.config(text=f"Memory: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")

            # Disk usage
            disk = psutil.disk_usage('/')
            self.disk_label.config(text=f"Disk: {disk.percent:.1f}% ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)")

            # Update network connections
            self.update_network_connections()

        except Exception as e:
            self.log_message(f"Error updating system info: {str(e)}")

    def update_network_connections(self):
        """Update network connections display"""
        # Clear existing items
        for item in self.network_tree.get_children():
            self.network_tree.delete(item)

        try:
            # Get network connections
            connections = psutil.net_connections(kind='inet')

            for conn in connections[:20]:  # Limit to first 20
                if conn.laddr:
                    local_addr = f"{conn.laddr.ip}:{conn.laddr.port}"
                    remote_addr = f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A"

                    self.network_tree.insert("", "end", text=str(conn.pid), values=(
                        local_addr,
                        remote_addr,
                        conn.status
                    ))

        except Exception as e:
            pass  # Ignore errors in network monitoring

    def start_monitoring(self):
        """Start system monitoring loop"""
        def monitor_loop():
            while True:
                try:
                    self.root.after(0, self.update_system_info)
                    time.sleep(5)
                except Exception as e:
                    break

        threading.Thread(target=monitor_loop, daemon=True).start()

    def open_all_web_interfaces(self):
        """Open all web interfaces in browser"""
        for name, info in self.components.items():
            if info['url'] and info['status'] == "Running":
                webbrowser.open(info['url'])
                self.log_message(f"🌐 Opened {name} in browser")

    def generate_system_report(self):
        """Generate comprehensive system report"""
        self.log_message("📋 Generating system report...")

        report = f"""
=== AI Agent System Report ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Component Status:
"""

        for name, info in self.components.items():
            report += f"  {name}: {info['status']}\n"

        # Save report
        report_file = f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        self.log_message(f"📋 System report saved to: {report_file}")

    def open_github(self):
        """Open GitHub in browser"""
        webbrowser.open("https://github.com")
        self.log_message("🐙 Opened GitHub")

    def git_pull(self):
        """Pull latest changes from git"""
        self.log_message("📥 Pulling latest changes...")

        def pull():
            try:
                result = subprocess.run(
                    ["git", "pull"],
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )
                print(f"Git pull result:\n{result.stdout}")
                if result.stderr:
                    print(f"Git pull error:\n{result.stderr}")
            except Exception as e:
                print(f"Git pull error: {str(e)}")

        threading.Thread(target=pull, daemon=True).start()

    def git_push(self):
        """Push changes to git"""
        self.log_message("📤 Pushing changes...")

        def push():
            try:
                # Add all changes
                subprocess.run(["git", "add", "."], cwd=os.getcwd())

                # Commit with timestamp
                commit_msg = f"Dashboard update {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                subprocess.run(["git", "commit", "-m", commit_msg], cwd=os.getcwd())

                # Push
                result = subprocess.run(
                    ["git", "push"],
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )
                print(f"Git push result:\n{result.stdout}")
                if result.stderr:
                    print(f"Git push error:\n{result.stderr}")
            except Exception as e:
                print(f"Git push error: {str(e)}")

        threading.Thread(target=push, daemon=True).start()

    def open_huggingface(self):
        """Open HuggingFace in browser"""
        webbrowser.open("https://huggingface.co")
        self.log_message("🤗 Opened HuggingFace")

    def install_transformers(self):
        """Install transformers library"""
        self.log_message("📦 Installing transformers...")

        def install():
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", "transformers", "torch"],
                    capture_output=True,
                    text=True
                )
                print(f"Installation result:\n{result.stdout}")
                if result.stderr:
                    print(f"Installation error:\n{result.stderr}")
            except Exception as e:
                print(f"Installation error: {str(e)}")

        threading.Thread(target=install, daemon=True).start()

    def open_reddit(self):
        """Open Reddit in browser"""
        webbrowser.open("https://reddit.com/r/Python")
        self.log_message("🔴 Opened Reddit")

    def log_message(self, message):
        """Log a message to both logger and terminal"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        # Log to file
        self.logger.info(message)

        # Print to terminal (will be captured by redirector)
        print(formatted_message)

    def on_closing(self):
        """Handle application closing"""
        if messagebox.askokcancel("Quit", "Stop all components and quit?"):
            self.log_message("🛑 Shutting down dashboard...")
            self.stop_all_components()

            # Restore original streams
            sys.stdout = self.stdout_redirector.original_stream
            sys.stderr = self.stderr_redirector.original_stream

            self.root.destroy()

    def run(self):
        """Run the dashboard"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("🎯 Terminal Connected Dashboard ready!")
        self.log_message("💡 All GUI output is now connected to terminal")
        self.log_message("🔗 Cloud integrations available in Cloud tab")

        # Initial status refresh
        self.refresh_status()

        self.root.mainloop()

if __name__ == "__main__":
    try:
        print("🚀 Starting Terminal Connected Dashboard...")

        # Install required packages if missing
        required_packages = ["psutil"]
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                print(f"Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])

        # Change to script directory
        script_dir = Path(__file__).parent
        os.chdir(script_dir)

        # Create and run dashboard
        dashboard = TerminalConnectedDashboard()
        dashboard.run()

    except KeyboardInterrupt:
        print("\n🛑 Dashboard interrupted by user")
    except Exception as e:
        print(f"❌ Dashboard error: {str(e)}")
        import traceback
        traceback.print_exc()
