#!/usr/bin/env python3
# AI Agentic Unified Dashboard Interface
# Centralized control system for autonomous AI agents
"""
Test if GUI components are working
"""
import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_gui():
    print("🔧 Testing GUI components...")

    try:
        # Test basic tkinter
        root = tk.Tk()
        root.title("GUI Test - Modern AI Hub")
        root.geometry("400x200")
        root.configure(bg="#1e1e1e")

        # Add a test label
        label = tk.Label(root, text="✅ GUI Test Successful!",
                        fg="white", bg="#1e1e1e", font=("Arial", 16))
        label.pack(pady=50)

        # Add a button to close
        button = tk.Button(root, text="Close Test",
                          command=root.destroy,
                          bg="#0078d4", fg="white")
        button.pack(pady=10)

        print("✅ GUI window created successfully")
        print("🎯 If you can see this window, GUI is working!")

        # Show the window
        root.mainloop()

    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui()
