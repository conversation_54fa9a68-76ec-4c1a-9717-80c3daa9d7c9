name: AI test
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to checkout'
        required: false
        default: 'main'
        type: string

permissions:
  contents: read

jobs:
  main:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        node-version: [18.19.0]

    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      OPENAI_BASE_URL: ${{ secrets.OPENAI_BASE_URL }}
      MIDSCENE_MODEL_NAME: gpt-4o-2024-11-20
      CI: 1
      # MIDSCENE_DEBUG_AI_PROFILE: 1

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ref: ${{ github.event.inputs.branch || 'main' }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0
  
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
    
    - name: <PERSON>ache Playwright dependencies
      uses: actions/cache@v3
      id: cache-playwright
      with:
        path: ~/.cache/ms-playwright
        key: ${{ runner.os }}-playwright-${{ hashFiles('pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-playwright-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Install Playwright dependencies
      run: |
        cd packages/web-integration
        if [ "${{ steps.cache-playwright.outputs.cache-hit }}" != "true" ]; then
          npx playwright install --with-deps
        else
          npx playwright install-deps && npx playwright install
        fi

    - name: Install puppeteer dependencies
      run: |
        cd packages/web-integration
        npx puppeteer browsers install chrome

    - name: Build project
      run: pnpm run build
    
    - name: Run e2e tests
      run: pnpm run e2e
      id: e2e-tests
      continue-on-error: true

    - name: Upload e2e report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: e2e-report
        path: ${{ github.workspace }}/packages/web-integration/midscene_run/report
        if-no-files-found: ignore
      
    - name: Run e2e tests cache
      run: pnpm run e2e:cache
      id: e2e-tests-cache
      continue-on-error: true

    - name: Upload e2e cache report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: e2e-cache-report
        path: ${{ github.workspace }}/packages/web-integration/midscene_run/report
        if-no-files-found: ignore

    - name: Run e2e tests report
      run: pnpm run e2e:report
      id: e2e-tests-report
      continue-on-error: true

    - name: Upload e2e report output
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: e2e-report-output
        path: ${{ github.workspace }}/packages/web-integration/midscene_run/report
        if-no-files-found: ignore

    - name: Check if script failed
      if: steps.e2e-tests.outcome == 'failure' || steps.e2e-tests-cache.outcome == 'failure' || steps.e2e-tests-report.outcome == 'failure'
      run: exit 1
