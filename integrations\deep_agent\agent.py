"""
Deep Agent implementation for AI Agent System.
"""
import logging
from typing import Dict, List, Optional, Any, Union
import asyncio
from datetime import datetime

from .client import DeepAgentClient

logger = logging.getLogger(__name__)

class DeepAgent:
    """
    Deep Agent implementation that can be used with the AI Agent System.
    """

    def __init__(self,
                 agent_id: Optional[str] = None,
                 name: Optional[str] = None,
                 capabilities: Optional[List[str]] = None,
                 description: Optional[str] = None,
                 parameters: Optional[Dict] = None,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        Initialize Deep Agent.

        Args:
            agent_id: ID of existing agent to connect to
            name: Name of the agent if creating a new one
            capabilities: List of agent capabilities if creating a new one
            description: Description of the agent if creating a new one
            parameters: Additional parameters for the agent if creating a new one
            api_key: API key for Deep Agent
            base_url: Base URL for Deep Agent API
        """
        self.client = DeepAgentClient(api_key=api_key, base_url=base_url)
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.status = "initialized"
        self.last_active = None
        self.config = {}

        if agent_id:
            # Connect to existing agent
            try:
                agent_info = self.client.get_agent(agent_id)
                self.name = agent_info.get("name", name)
                self.description = agent_info.get("description", description)
                self.config = agent_info
            except Exception as e:
                logger.error(f"Failed to connect to Deep Agent {agent_id}: {e}")
                self.status = "error"
        elif name and capabilities:
            # Create new agent
            try:
                agent_info = self.client.create_agent(
                    name=name,
                    capabilities=capabilities,
                    description=description,
                    parameters=parameters
                )
                self.agent_id = agent_info.get("id")
                self.config = agent_info
            except Exception as e:
                logger.error(f"Failed to create Deep Agent: {e}")
                self.status = "error"
        else:
            logger.warning("Neither agent_id nor name+capabilities provided. Agent is in a placeholder state.")

    async def process_message(self, message: Dict) -> Dict:
        """
        Process a message using this agent.

        Args:
            message: Message to process

        Returns:
            Agent response
        """
        # Update status and last active time
        self.status = "processing"
        self.last_active = datetime.now()

        try:
            # Extract relevant info from message
            content = message.get("content", "")
            context = message.get("context", {})
            sender = message.get("sender_id", "unknown")

            input_data = {
                "message": content,
                "sender": sender
            }

            # Run the agent (use asyncio.to_thread to avoid blocking)
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self.client.run_agent(
                    agent_id=self.agent_id,
                    input_data=input_data,
                    context=context
                )
            )

            # Update status
            self.status = "idle"

            # Format response
            response = {
                "content": result.get("output", {}),
                "sender_id": f"deep_agent_{self.agent_id}",
                "recipient_id": sender,
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "agent_type": "deep_agent",
                    "agent_id": self.agent_id,
                    "raw_response": result
                }
            }

            return response
        except Exception as e:
            logger.exception(f"Error processing message with Deep Agent: {e}")
            self.status = "error"

            # Return error response
            return {
                "content": f"Error processing your request: {str(e)}",
                "sender_id": f"deep_agent_{self.agent_id}",
                "recipient_id": message.get("sender_id", "unknown"),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "error": str(e),
                    "agent_type": "deep_agent",
                    "agent_id": self.agent_id
                }
            }

    async def add_tool(self, tool_data: Dict) -> Dict:
        """
        Add a tool to the agent.

        Args:
            tool_data: Tool configuration

        Returns:
            Result of adding the tool
        """
        try:
            result = self.client.add_tool(self.agent_id, tool_data)
            return {"success": True, "data": result}
        except Exception as e:
            logger.exception(f"Error adding tool to Deep Agent: {e}")
            return {"success": False, "error": str(e)}

    async def train(self, examples: List[Dict]) -> Dict:
        """
        Train the agent with examples.

        Args:
            examples: Training examples

        Returns:
            Training result
        """
        try:
            result = self.client.train_agent(self.agent_id, examples)
            return {"success": True, "data": result}
        except Exception as e:
            logger.exception(f"Error training Deep Agent: {e}")
            return {"success": False, "error": str(e)}
