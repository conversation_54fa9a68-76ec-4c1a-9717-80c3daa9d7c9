#!/usr/bin/env python3
import subprocess
import sys
import os

# Run the dashboard launcher
try:
    print("🚀 Starting AI Agent Dashboard...")
    result = subprocess.run([sys.executable, "dashboard_launcher_with_output.py"], 
                          cwd=os.getcwd(), 
                          capture_output=False)
    print(f"Dashboard process completed with return code: {result.returncode}")
except Exception as e:
    print(f"Error running dashboard: {e}")