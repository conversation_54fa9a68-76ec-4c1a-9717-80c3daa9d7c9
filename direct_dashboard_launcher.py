#!/usr/bin/env python3
"""
Direct Dashboard Launcher with Immediate Feedback
Creates visible confirmation that dashboard is launching
"""
import os
import sys
import subprocess
import time
import tkinter as tk
from tkinter import messagebox
import threading

def show_launch_confirmation():
    """Show immediate confirmation dialog"""
    root = tk.Tk()
    root.withdraw()  # Hide main window

    result = messagebox.askquestion(
        "AI Agent System Launch",
        "🚀 Ready to launch AI Agent System Dashboard?\n\n"
        "This will open:\n"
        "• Terminal Connected Dashboard (Main)\n"
        "• Quick Start Dashboard (Backup)\n\n"
        "Click YES to proceed!",
        icon='question'
    )

    if result == 'yes':
        root.destroy()
        return True
    else:
        root.destroy()
        return False

def launch_dashboards():
    """Launch both dashboards"""
    python_exe = "C:/Users/<USER>/Documents/augment-projects/Ai Agent System/agent_env/Scripts/python.exe"

    # Launch main dashboard
    try:
        print("Launching terminal_connected_dashboard.py...")
        subprocess.Popen([python_exe, "terminal_connected_dashboard.py"])
        print("✅ Main dashboard launched!")
    except Exception as e:
        print(f"❌ Error launching main dashboard: {e}")

    time.sleep(2)

    # Launch quick dashboard
    try:
        print("Launching quick_start_dashboard.py...")
        subprocess.Popen([python_exe, "quick_start_dashboard.py"])
        print("✅ Quick dashboard launched!")
    except Exception as e:
        print(f"❌ Error launching quick dashboard: {e}")

def show_success_confirmation():
    """Show success confirmation"""
    root = tk.Tk()
    root.withdraw()

    messagebox.showinfo(
        "Dashboard Launched!",
        "🎉 AI Agent System Dashboard Launched Successfully!\n\n"
        "Look for these windows:\n"
        "• AI Agent System - Terminal Connected Dashboard\n"
        "• AI Agent System - Quick Start Dashboard\n\n"
        "Features available:\n"
        "• Component management (Start/Stop services)\n"
        "• Terminal Output tab (Live logs)\n"
        "• System Monitor tab (Resource usage)\n"
        "• Cloud Integration tab (GitHub/HuggingFace/Reddit)\n\n"
        "If you don't see windows, check your taskbar!"
    )

    root.destroy()

def main():
    print("🚀 AI Agent System - Direct Dashboard Launcher")
    print("=" * 50)

    # Show launch confirmation
    if show_launch_confirmation():
        print("User confirmed launch - proceeding...")

        # Launch dashboards
        launch_dashboards()

        # Wait a moment
        time.sleep(3)

        # Show success confirmation
        show_success_confirmation()

        print("🎉 Dashboard launch sequence completed!")
        print("Check your screen for dashboard windows!")

    else:
        print("Launch cancelled by user.")

if __name__ == "__main__":
    main()
