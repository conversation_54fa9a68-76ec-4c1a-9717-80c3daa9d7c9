name: Issue Close Require

on:
  schedule:
    - cron: '0 0 * * *'

permissions:
  # Permits `actions-cool/issues-helper` to close an issue
  issues: write
  contents: read

jobs:
  issue-close-require:
    runs-on: ubuntu-latest
    if: github.repository == 'web-infra-dev/midscene'
    steps:
      - name: need reproduction
        uses: actions-cool/issues-helper@a610082f8ac0cf03e357eb8dd0d5e2ba075e017e # v3.6.0
        with:
          actions: 'close-issues'
          labels: 'need reproduction'
          inactive-day: 5
          body: |
            As the issue was labelled with `need reproduction`, but no response in 5 days. This issue will be closed. Feel free to comment and reopen it if you have any further questions. For background, see [Why reproductions are required](https://antfu.me/posts/why-reproductions-are-required).

            由于该 issue 被标记为 "需要重现"，但在 5 天内没有回应，因此该 issue 将被关闭。如果你有任何进一步的问题，请随时发表评论并重新打开该 issue。背景请参考 [为什么需要最小重现](https://antfu.me/posts/why-reproductions-are-required-zh)。