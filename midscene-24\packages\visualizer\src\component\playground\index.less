@import '../common.less';

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 14px;
}

.playground-container,
.image-describer {
  width: 100%;
  height: 100%;

  &.vertical-mode {
    height: inherit;

    .form-part {
      h3 {
        font-size: 14px;
        line-height: 1.6;
      }
    }
  }

  .playground-header {
    padding: 10px 10px 30px;
  }

  .playground-left-panel {
    width: 100%;
    background-color: #FFF;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto !important;

    .ant-form {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }

  .form-part {
    h3 {
      margin-top: 0;
      margin-bottom: 12px;
      font-size: 18px;
    }

    .form-sub-title {
      margin-bottom: 12px;
      font-size: 14px;
    }

    .switch-btn-wrapper {
      .ant-btn {
        padding: 0;
      }
    }
  }



  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding: @layout-extension-space-vertical @layout-extension-space-horizontal;

    .loading-progress-text {
      text-align: center;
      width: 100%;
      color: @weak-text;
      margin-top: 16px;
    }

    .loading-progress-text-tab-info {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .loading-progress-text-progress {
      height: 60px;
    }
  }

  .result-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    border-radius: 4px;
    flex: 1 1 auto;
    min-height: 0;
    overflow: auto;

    &.result-wrapper-compact {
      padding: 0;
    }

    &.vertical-mode-result {
      height: inherit;
      min-height: 300px;
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    pre {
      display: block;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    .result-empty-tip {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      width: 100%;
      height: 100%;
      border: 1px solid rgba(0, 0, 0, 0.12);
      border-radius: 8px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      box-sizing: border-box;
    }
  }
}


.input-wrapper {
  box-sizing: border-box;

  .mode-radio-group-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .mode-radio-group {
    display: flex;
    align-items: center;
    height: 100%;

    .ant-radio-button-wrapper {
      height: 24px;
      padding: 0 8px;
      line-height: 24px;
      border-radius: 11px;
      margin-right: 8px;
      box-shadow: none;
      border: none;
      background-color: #F7F7F7;
      font-size: 12px;

      &:before {
        display: none;
      }

      &:focus-within {
        outline: none;
      }

      &.ant-radio-button-wrapper-checked {
        background-color: #2B83FF;
        border-color: #2B83FF;
        color: white;

        &:hover {
          color: #fff;
        }
      }

      &:hover {
        color: #2B83FF;
      }
    }
  }

  .main-side-console-input {
    position: relative;
    margin-top: 10px;

    .main-side-console-input-textarea {
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.12);
      padding: 12px 16px 52px 16px;
      transition: background-color 0.2s ease;
      overflow-y: auto;
      white-space: pre-wrap;
      line-height: 21px;

      scrollbar-width: thin;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
    }

    &.disabled {
      .form-controller-wrapper {
        background-color: transparent;
      }
    }
  }

  .ant-input {
    padding-bottom: 40px;
  }

  .form-controller-wrapper {
    position: absolute;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: calc(100% - 32px);
    box-sizing: border-box;
    align-items: flex-end;
    gap: 8px;
    background-color: #FFF;
    left: 16px;
    bottom: 0.5px;
    padding: 8px 0;
    line-height: 32px;
    transition: background-color 0.2s ease;
  }

  .settings-wrapper {
    &.settings-wrapper-hover {
      color: @main-text;
    }

    display: flex;
    flex-direction: row;
    gap: 2px;
    color: @weak-text;
    flex-wrap: wrap;
  }
}