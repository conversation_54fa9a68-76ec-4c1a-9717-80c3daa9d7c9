# 更新日志

> 完整更新日志请参考：[Midscene Releases](https://github.com/web-infra-dev/midscene/releases)

## v0.20 - 支持传入 XPath 定位元素

### 🌐 Web集成增强
#### 1️⃣ 新增 aiAsk 方法
- 可直接向 AI 模型提问，获取当前页面的字符串形式答案
- 适用场景：页面内容问答、信息提取等需要 AI 推理的任务
- 示例：

```typescript
await agent.aiAsk('问题描述')
```

####   2️⃣ 支持传入 XPath 定位元素
- 定位优先级：指定的 XPath > 缓存 > AI 大模型定位
- 适用场景：已知元素 XPath，需要跳过 AI 大模型定位
- 示例：

```typescript
await agent.aiTap('提交按钮', { xpath: '//button[@id="submit"]' })
```

### 📱 Android 改进
#### 1️⃣ Playground 任务可取消
- 支持中断正在执行的自动化任务，提升调试效率

#### 2️⃣ aiLocate API 增强
- 返回设备像素比（Device Pixel Ratio），通常用于计算元素真实坐标

### 📈 报告生成优化
改进报告生成机制，从批量存储改为单次追加，有效降低内存占用，避免用例数量大时造成的内存溢出

## v0.19 - 支持获取完整的执行过程数据

### 新增 API 获取 Midscene 执行过程数据

为 agent 添加 `_unstableLogContent` API，即可获取 Midscene 执行过程数据，比如每个步骤的耗时、AI Tokens 消耗情况、页面截图等！

对了，Midscene 的报告就是根据这份数据生成了，也就是说，使用这份数据，你甚至可以定制一个属于你自己的报告！

详情请参考：[API 文档](./API.mdx#agent_unstablelogcontent)

### CLI 新增参数支持调整 Midscene 环境变量优先级

默认情况下，`dotenv` 不会覆盖 `.env` 文件中同名的全局环境变量。如果希望覆盖，你可以使用 `--dotenv-override` 选项。

详情请参考：[使用 YAML 格式的自动化脚本](./automate-with-scripts-in-yaml.mdx#%E4%BD%BF%E7%94%A8-env-%E4%B8%AD%E7%9A%84%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F%E8%A6%86%E7%9B%96%E5%90%8C%E5%90%8D%E7%9A%84%E5%85%A8%E5%B1%80%E7%8E%AF%E5%A2%83%E5%8F%98%E9%87%8F)

### 大幅减少报告文件大小

裁剪生成的报告中冗余的数据，大幅减少复杂页面的报告文件大小，用户的典型复杂页面报告大小从 47.6M 减小到 15.6M！

## v0.18 - 回放报告功能增强

🚀 Midscene 又有更新啦！为你带来高质量的 UI 自动化体验。

### 在报告中增加自定义节点

* 为 agent 添加 `logScreenshot` API，将当前页面的截图作为报告节点。支持设置节点标题和描述，使报告内容更加丰富。适用于关键步骤截图记录、错误状态捕获、UI 验证等。

![](/blog/logScreenshot-api.png)

* 示例：

```typescript
test('login github', async ({ ai, aiAssert, aiInput, logScreenshot }) => {
  if (CACHE_TIME_OUT) {
    test.setTimeout(200 * 1000);
  }
  await ai('Click the "Sign in" button');
  await aiInput('quanru', 'username');
  await aiInput('123456', 'password');

  // 自定义记录
  await logScreenshot('Login page', {
    content: 'Username is quanru, password is 123456',
  });

  await ai('Click the "Sign in" button');
  await aiAssert('Login success');
});
```



### 支持将报告下载为视频

* 支持从报告播放器直接导出视频，点击播放器界面的下载按钮即可保存。

![](/blog/export-video.png)

* 适用场景：分享测试结果、存档重现步骤、演示问题复现



### Android 暴露更多配置

* 支持使用远程 adb 主机，配置键盘策略

  * `autoDismissKeyboard?: boolean` - 可选参数，是否在输入文本后自动关闭键盘

  * `androidAdbPath?: string` - 可选参数，用于指定 adb 可执行文件的路径

  * `remoteAdbHost?: string` - 可选参数，用于指定远程 adb 主机

  * `remoteAdbPort?: number` - 可选参数，用于指定远程 adb 端口

* 示例：

```typescript
await agent.aiInput('搜索框', '测试内容', { autoDismissKeyboard: true })
```

```typescript
const agent = await agentFromAdbDevice('s4ey59', {
    autoDismissKeyboard: false, // 可选参数，是否在输入文本后自动关闭键盘。默认值为 true。
    androidAdbPath: '/usr/bin/adb', // 可选参数，用于指定 adb 可执行文件的路径
    remoteAdbHost: '************', // 可选参数，用于指定远程 adb 主机
    remoteAdbPort: '5037' // 可选参数，用于指定远程 adb 端口
})
```

立即升级版本，体验这些强大新功能！

* [自定义报告节点 API 文档](/zh/API.mdx#agentlogscreenshot)
* [Android 更多配置项 API 文档](/zh/integrate-with-android.mdx#androiddevice-%E7%9A%84%E6%9E%84%E9%80%A0%E5%87%BD%E6%95%B0)


## v0.17 - 让 AI 看见页面 DOM

### 数据查询 API 全面增强

为满足更多自动化和数据提取场景，以下 API 新增了 options 参数，支持更灵活的 DOM 信息和截图传递：

- `agent.aiQuery(dataDemand, options)`
- `agent.aiBoolean(prompt, options)`
- `agent.aiNumber(prompt, options)`
- `agent.aiString(prompt, options)`

#### 新增 `options` 参数

- `domIncluded`：是否向模型发送精简后的 DOM 信息，默认值为 false。一般用于提取 UI 中不可见的属性，比如图片的链接。
- `screenshotIncluded`：是否向模型发送截图。默认值为 true。

#### 代码示例

```typescript
// 提取通讯录中所有联系人的完整信息（包含隐藏的头像链接）
const contactsData = await agent.aiQuery(
  "{name: string, id: number, company: string, department: string, avatarUrl: string}[], extract all contact information including hidden avatarUrl attributes",
  { domIncluded: true }
);

// 检查通讯录中第一个联系人的 id 属性是否为 1
const isId1 = await agent.aiBoolean(
  "Is the first contact's id is 1?",
  { domIncluded: true }
);

// 获取第一个联系人的 ID（隐藏属性）
const firstContactId = await agent.aiNumber("First contact's id?", { domIncluded: true });

// 获取第一个联系人的头像 URL（页面上不可见的属性）
const avatarUrl = await agent.aiString(
  "What is the Avatar URL of the first contact?",
  { domIncluded: true }
);
```

### 新增右键点击能力

你有没有遇到过需要自动化右键操作的场景？现在，Midscene 支持了全新的 `agent.aiRightClick()` 方法！

#### 功能

使用右键点击页面元素，适用于那些自定义了右键事件的场景。注意：Midscene 无法与浏览器原生菜单交互。

#### 参数说明

- `locate`: 用自然语言描述你要操作的元素
- `options`: 可选，支持 `deepThink`（AI精细定位）、`cacheable`（结果缓存）

#### 示例

```typescript
// 在通讯录应用中右键点击联系人，触发自定义上下文菜单
await agent.aiRightClick("Alice Johnson", { deepThink: true });

// 然后可以点击菜单中的选项
await agent.aiTap("Copy Info"); // 复制联系人信息到剪贴板
```


### 示例及其报告

#### 示例页面

<iframe src="https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/contacts3.html" width="100%" height="800"></iframe>

#### 示例报告

<iframe src="https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/puppeteer-2025-06-04_20-41-45-be8ibktz.html" width="100%" height="800"></iframe>


### 一个完整示例

在下面的报告文件中，我们展示了一个完整的示例，展示了如何使用新的 `aiRightClick` API 和新的查询参数来提取包含隐藏属性的联系人数据。

报告文件：[puppeteer-2025-06-04_20-41-45-be8ibktz.html](https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/puppeteer-2025-06-04_20-41-45-be8ibktz.html)

对应代码可以参考我们的示例仓库：[puppeteer-demo/extract-data.ts](https://github.com/web-infra-dev/midscene-example/blob/main/puppeteer-demo/extract-data.ts)

### 重构缓存能力

使用 xpath 缓存，而不是基于坐标，提高缓存命中概率。

缓存文件格式使用 yaml 替换 json，提高可读性。

## v0.16 - 支持 MCP

### Midscene MCP

🤖 使用 Cursor / Trae 帮助编写测试用例。
🕹️ 快速实现浏览器操作，媲美 Manus 平台。
🔧 快速集成 Midscene 能力，融入你的平台和工具。

了解详情: [MCP](./mcp.mdx)

### 支持结构化 API 

APIs: `aiBoolean`, `aiNumber`, `aiString`, `aiLocate`

了解详情: [使用结构化 API 优化自动化代码](./blog-programming-practice-using-structured-api.md)

## v0.15 - Android 自动化上线！

### Android 自动化上线！

🤖 AI 调试：自然语言调试
📱 支持原生、Lynx 和 WebView 应用
🔁 可回放运行
🛠️ YAML 或 JS SDK
⚡ 自动规划 & 即时操作 API

### 更多功能

* 支持自定义 midscene_run 目录
* 增强报告文件名生成，支持唯一标识符和分段模式
* 增强超时配置和日志记录，支持网络空闲和导航超时
* 适配 gemini-2.5-pro

了解详情: [支持 Android 自动化](./blog-support-android-automation.mdx)

## v0.14 - 即时操作 API

### 即时操作 API

* 新增即时操作 API，增强 AI 操作的准确性

了解详情: [即时操作 API](./blog-introducing-instant-actions-and-deep-think.md)

## v0.13 - 深度思考模式

### 原子 AI 交互方法

* 支持 aiTap, aiInput, aiHover, aiScroll, aiKeyboardPress 等原子操作

### 深度思考模式

* 增强点击准确性，提供更深层次的上下文理解

![](/blog/0.13.jpeg)

## v0.12 - 集成 Qwen 2.5 VL

### 集成 Qwen 2.5 VL 的本地能力

* 保持输出准确性
* 支持更多元素交互
* 成本降低 80% 以上

## v0.11.0 - UI-TARS 模型缓存

### **✨ UI-TARS 模型支持缓存**

* 通过文档开启缓存 👉 ： [开启缓存](./caching.mdx)

* 开启效果

<video src="https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/antd-form-cache.mp4" controls/>


![](/blog/0.11.0.png)

### **✨ 优化 DOM 树提取策略**

* 优化了 dom 树的信息能力，加速了 GPT 4o 等模型的推理过程

![](/blog/0.11.0-2.png)


## v0.10.0 - UI-TARS 模型上线

UI-TARS 是由 **Seed** 团队开源的 Native GUI agent 模型。UI-TARS 起名源之[星际穿越](https://zh.wikipedia.org/zh-cn/%E6%98%9F%E9%99%85%E7%A9%BF%E8%B6%8A)电影中的 [TARS 机器人](https://interstellarfilm.fandom.com/wiki/TARS)，它具备高度的智能和自主思考能力。 UI-TARS **将图片和人类指令作为输入信息**，可以正确的感知下一步的行动，从而逐渐接近人类指令的目标，在 GUI 自动化任务的各项基准测试中均领先于各类开源模型、闭源商业模型。

![](/blog/0.10.0.png)

UI-TARS:Pioneering Automated GUI Interaction with Native Agents - Figure 1

![](/blog/0.10.0-2.png)

UI-TARS:Pioneering Automated GUI Interaction with Native - Figure 4

### **✨**模型优势

UI-TARS 模型在 GUI 任务中有以下优势：


* **目标驱动**

* **推理速度快**

* **Native GUI agent 模型**

* **模型开源**

* **公司内部私有化部署无数据安全问题**


## v0.9.0 - 桥接模式上线！

通过 Midscene 浏览器插件，你可以用脚本联动桌面浏览器进行自动化操作了！

我们把它命名为“桥接模式”（Bridge Mode）。


相比于之前各种 CI 环境调试，优势在于：

1. 可以复用桌面浏览器，尤其是 Cookie、登录态、前置界面状态等 ，即刻开启自动化，而不用操心环境搭建

2. 支持人工与脚本配合操作界面，提升自动化工具的灵活性

3. 简单的业务回归，Bridge Mode 本地跑一下就行

![](/blog/0.9.0.png)

文档：[通过 Chrome 插件快速体验](./bridge-mode-by-chrome-extension.mdx)

## v0.8.0 - Chrome 插件

### **✨ 新增  Chrome 插件，任意页面随时运行 Midscene**

通过 Chrome 插件，你可以零代码、任意页面随时运行 Midscene，体验它的 Action \ Query \ Assert 等能力。

体验方式：[ 使用 Chrome 插件体验 Midscene](./quick-experience.mdx)

<video src="https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/Midscene_extension.mov" controls/>


## v0.7.0 - Playground 能力

### **✨ 新增 Playground 能力，随时发起调试**

再也不用频繁重跑脚本调试 Prompt 了！

在全新的测试报告页上，你可以随时对 AI 执行结果进行调试，包括页面操作、页面信息提取、页面断言。

<video src="https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/midscene-playground.mov" controls/>


## v0.6.0 - 支持字节豆包模型

### **✨ 模型：**支持字节豆包模型

全新支持调用豆包模型调用，参考下方环境变量即可体验。

```bash
MIDSCENE_OPENAI_INIT_CONFIG_JSON='{"baseURL":"https://xxx.net/api/v3","apiKey":"xxx"}'
MIDSCENE_MODEL_NAME='ep-20240925111815-mpfz8'
MIDSCENE_MODEL_TEXT_ONLY='true'
```

总结目前豆包模型的可用性：

* 目前豆包只有纯文本模型，也就是“看”不到图片。在纯粹通过界面文本进行推理的场景中表现尚可。

* 如果用例需要结合分析界面 UI ，它完全不可用



举例：

✅ 多肉葡萄的价格 (可以通过界面文字的顺序猜出来)

✅ 切换语言文本按钮(可以是:中文，英文文本) (可以通过界面文字内容猜出来)

❌ 左下角播放按钮 (需要图像理解，失败)



### ✨ 模型：支持 GPT-4o 结构化输出、成本继续下降

通过使用 gpt-4o-2024-08-06 模型，Midscene 已支持结构化输出（structured-output）特性，确保了稳定性增强、成本下降了 40%+。

Midscene 现已支持命中 GPT-4o prompt caching 特性，待公司 GPT 平台跟进部署后，AI 调用成本将继续下降。



### ✨ 测试报告：支持动画回放

现在你可以在测试报告中查看每个步骤的动画回放，快速调试自己的运行脚本

<video src="https://lf3-static.bytednsdoc.com/obj/eden-cn/nupipfups/Midscene/midscene-play-all.mp4" controls/>



### ✨ 提速：合并执行流程， 响应提速 30%

新版本中，我们将 Plan 和 Locate 操作在 prompt 执行上进行一定程度合并，使得 AI 响应速度提升  30%

> Before

![](/blog/0.6.0.png)

> after

![](/blog/0.6.0-2.png)



### ✨ 测评报告：不同模型在 Midscene 场景下的表现

* GPT 4o 系列模型，接近 100% 正确率

* doubao-pro-4k 纯文本模型，接近可用状态

![](/blog/0.6.0-3.png)

![](/blog/0.6.0-4.png)



### **🐞**问题修复

优化了页面信息提取，避免遮挡元素被收集，以此优化成功率、速度、AI 调用成本 🚀

> before

![](/blog/0.6.0-5.png)

> after

![](/blog/0.6.0-6.png)


## v0.5.0 - 支持 GPT-4o 结构化输出

### 新功能 **✨**

* 支持了 gpt-4o-2024-08-06 模型提供 100% JSON 格式限制，降低了 Midscene 任务规划时的幻觉行为

![](/blog/0.5.0.png)

* 支持了 Playwright AI 行为实时可视化，提升排查问题的效率

![](/blog/0.5.0-2.png)

* 缓存通用化，缓存能力不再仅仅局限于 playwright，pagepass、puppeteer 都可以使用缓存

```diff
- playwright test --config=playwright.config.ts
# 开启缓存
+ MIDSCENE_CACHE=true playwright test --config=playwright.config.ts
```

* 支持了  azure openAI 的调用方式

* 支持了 AI 对于 Input 现有基础之上的增删改行为

### 问题修复 **🐞**

* 优化了对于非文本、input、图片元素的识别，提升 AI 任务正确性

* 在 AI 交互过程中裁剪了不必要的属性字段，降低了 token 消耗

* 优化了 KeyboardPress、Input 事件在任务规划时容易出现幻觉的情况

* 针对 pagepass 通过 Midscene 执行过程中出现的闪烁行为，提供了优化方案

```javascript
// 目前 pagepagepsss 依赖的 puppeteer 版本太低，截图可能会导致界面闪动、光标丢失，通过下面方式可以解决
const originScreenshot = puppeteerPage.screenshot;
puppeteerPage.screenshot = async (options) => {
  return await originScreenshot.call(puppeteerPage, {
    ...options,
    captureBeyondViewport: false
  });
};
```

## v0.4.0 - 支持使用 Cli

### 新功能 **✨**

* Midscene 支持 Cli 的使用方式，降低 Midscene 使用门槛

```bash
# headed 模式（即可见浏览器）访问 baidu.com 并搜索“天气”
npx @midscene/cli --headed --url https://www.baidu.com --action "输入 '天气', 敲回车" --sleep 3000

# 访问 Github 状态页面并将状态保存到 ./status.json
npx @midscene/cli --url https://www.githubstatus.com/ \
  --query-output status.json \
  --query '{serviceName: string, status: string}[], github 页面的服务状态，返回服务名称'
```

* 支持 AI 执行等待能力，让 AI 等到到某个时候继续后续任务执行

* Playwright AI 任务报告展示整体耗时，并按测试组进行聚合 AI 任务

### 问题修复 **🐞**

* 修复 AI 在连续性任务时容易出现幻觉导致任务规划失败



## v0.3.0 - 支持 AI HTML 报告

### 新功能 **✨**

* AI 报告 html 化，将测试报告按测试组聚合，方便测试报告分发

### 问题修复 **🐞**

* 修复 AI 报告滚动预览问题



## v0.2.0 - 通过自然语言控制 puppeteer

### 新功能 **✨**

* 支持通过自然语言控制 puppeteer 实现页面操作自动化🗣️💻

* 在 playwright 框架中提供 AI 缓存能力，提高稳定性和执行效率

* AI 报告可视化按照测试组进行合并，优化聚合展示

* 支持 AI 断言能力，让 AI 判断页面是否满足某种条件





## v0.1.0 - 通过自然语言控制 playwright

### 新功能 **✨**

* 通过自然语言控制 playwright 实现页面操作自动化 🗣️💻

* 通过自然语言提取页面信息 🔍🗂️

* AI 报告，AI 行为、思考可视化 🛠️👀

* 直接使用 GPT-4o 模型，无需任何训练 🤖🔧

