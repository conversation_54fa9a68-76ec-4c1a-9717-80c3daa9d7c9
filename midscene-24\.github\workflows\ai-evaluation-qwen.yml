name: AI evaluation - Qwen
on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to checkout'
        required: false
        default: 'main'
        type: string

permissions:
  contents: read

jobs:
  main:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        node-version: [18.19.0]

    env:
      OPENAI_API_KEY: ${{ secrets.QWEN_KEY }}
      OPENAI_BASE_URL: ${{ secrets.QWEN_BASE_URL }}
      MIDSCENE_MODEL_NAME: "qwen-vl-max-latest"
      MIDSCENE_USE_QWEN_VL: 1
      CI: 1

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
        ref: ${{ github.event.inputs.branch || 'main' }}

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0
  
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Build project
      run: pnpm run build
    
    - name: Run evaluation
      run: |
        cd packages/evaluation
        pnpm run evaluate:planning
        pnpm run evaluate:assertion

    - name: Upload test log
      uses: actions/upload-artifact@v4
      with:
        name: evaluation-test-log
        path: ${{ github.workspace }}/packages/evaluation/src/__ai_responses__
        if-no-files-found: ignore
