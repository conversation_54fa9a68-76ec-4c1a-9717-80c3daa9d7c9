# Midscene.js - AI 驱动，带来愉悦的 UI 自动化体验

开源的 AI 操作助手，适用于 Web、移动端、自动化和测试

## 功能特性

### 用自然语言编写自动化脚本
- 描述你的目标和步骤，Midscene 会为你规划和操作用户界面。
- 使用 Javascript SDK 或 YAML 格式编写自动化脚本。

### 网页或移动应用
- **网页自动化**：可以[与 Puppeteer 集成](https://midscenejs.com/integrate-with-puppeteer.html)，[与 Playwright 集成](https://midscenejs.com/integrate-with-playwright.html)或使用[桥接模式](https://midscenejs.com/bridge-mode-by-chrome-extension.html)来控制桌面浏览器。
- **Android 自动化**：使用 [Javascript SDK](https://midscenejs.com/integrate-with-android.html) 配合 adb 来控制本地 Android 设备。

### 工具
- **用于调试的可视化报告**: 通过我们的测试报告和 Playground，你可以轻松理解、回放和调试整个过程。
- [**使用缓存，提高执行效率**](https://midscenejs.com/zh/caching.html): 使用缓存能力重放脚本，提高执行效率。
- [**MCP**](https://midscenejs.com/zh/mcp.html): 允许其他 MCP Client 直接使用 Midscene 的能力。

### 三种 API 类型

- [**交互 API**](https://midscenejs.com/zh/api.html#interaction-methods): 与用户界面交互。
- [**数据提取 API**](https://midscenejs.com/zh/api.html#data-extraction): 从用户界面和 DOM 中提取数据。
- [**实用 API**](https://midscenejs.com/zh/api.html#more-apis): 实用函数，如 `aiAssert()` （断言）, `aiLocate()` （定位）, `aiWaitFor()` （等待）。

## 演示案例

我们准备了一些演示案例供你学习 Midscene.js 的使用。

1. 使用 JS 代码驱动任务编排，收集周杰伦演唱会信息，并写入 Google Docs（使用 UI-TARS 模型）

<video src="https://github.com/user-attachments/assets/75474138-f51f-4c54-b3cf-46d61d059999" height="300" controls />

2. 控制 Android 上的地图应用（使用 Qwen-2.5-VL 模型）

<video src="https://github.com/user-attachments/assets/1f5bab0e-4c28-44e1-b378-a38809b05a00" height="300" controls />

3. 使用 midscene mcp 浏览页面 ( https://www.saucedemo.com/ )，执行登录、添加产品、下订单，最后基于 mcp 执行步骤和 playwright 示例生成测试用例

<video src="https://github.com/user-attachments/assets/a95ca353-e50c-4091-85ba-e542f576b6be" height="300" controls />

## 零代码快速体验

- **[Chrome 插件](https://midscenejs.com/quick-experience.html)**：通过 [Chrome 插件](https://midscenejs.com/quick-experience.html) 立即开始浏览器内体验，无需编写任何代码。
- **[Android Playground](https://midscenejs.com/quick-experience-with-android.html)**：使用 Android playground 来控制你的本地 Android 设备。

## 模型选择

Midscene.js 支持多模态 LLM 模型，如 `gpt-4o`，以及视觉语言模型，如 `Qwen2.5-VL`、`Doubao-1.5-thinking-vision-pro`、`gemini-2.5-pro` 和 `UI-TARS`。

推荐使用视觉语言模型进行 UI 自动化。

了解更多关于[选择 AI 模型](https://midscenejs.com/choose-a-model)

## 两种自动化风格

### 自动规划

Midscene 将自动规划步骤并执行。这可能会比较慢，并且严重依赖 AI 模型的质量。

```javascript
await aiAction('逐一点击所有记录。如果某个记录包含文本"completed"，则跳过它');
```

### 工作流风格

将复杂逻辑拆分为多个步骤，以提高自动化代码的稳定性。

```javascript
const recordList = await agent.aiQuery('string[], the record list')
for (const record of recordList) {
  const hasCompleted = await agent.aiBoolean(`check if the record contains the text "completed"`)
  if (!hasCompleted) {
    await agent.aiTap(record)
  }
}
```

> 有关工作流风格的更多详细信息，请参阅 [Blog - 使用 JavaScript 优化 AI 自动化代码
](https://midscenejs.com/zh/blog-programming-practice-using-structured-api.html)


## 与其他工具比较

* **调试体验**: 你很快就会发现，调试和维护自动化脚本才是真正的痛点。无论模型多么强大，你仍然需要调试过程以确保其保持长期稳定。Midscene.js 提供了可视化报告、内置的 Playground 和 Chrome 插件，以调试整个运行过程。这是大多数开发者真正需要的特性，我们也在持续努力改进调试体验。

* **开源、免费、部署灵活**: Midscene.js 是一个开源项目。它与云服务和模型提供商解耦，你可以选择公共或私有部署。总会有一个适合你的方案。

* **与 Javascript 集成**: 你可以永远相信 Javascript 😎

## 资源

* 主页和文档：[https://midscenejs.com](https://midscenejs.com/)
* 示例项目：[https://github.com/web-infra-dev/midscene-example](https://github.com/web-infra-dev/midscene-example)
* API 参考：[https://midscenejs.com/api.html](https://midscenejs.com/api.html)
* GitHub：[https://github.com/web-infra-dev/midscene](https://github.com/web-infra-dev/midscene)

## 社区

* [Discord](https://discord.gg/2JyBHxszE4)
* [在 X 上关注我们](https://x.com/midscene_ai)
* [飞书交流群](https://applink.larkoffice.com/client/chat/chatter/add_by_link?link_token=291q2b25-e913-411a-8c51-191e59aab14d)

## 致谢

我们要感谢以下项目：

- [Rsbuild](https://github.com/web-infra-dev/rsbuild) 提供构建工具。
- [UI-TARS](https://github.com/bytedance/ui-tars) 提供开源智能体模型 UI-TARS。
- [Qwen2.5-VL](https://github.com/QwenLM/Qwen2.5-VL) 提供开源 VL 模型 Qwen2.5-VL。
- [scrcpy](https://github.com/Genymobile/scrcpy) 和 [yume-chan](https://github.com/yume-chan) 让我们能够用浏览器控制 Android 设备。
- [appium-adb](https://github.com/appium/appium-adb) 提供 adb 的 javascript 桥接。
- [YADB](https://github.com/ysbing/YADB) 提供 yadb 工具，提升文本输入性能。
- [Puppeteer](https://github.com/puppeteer/puppeteer) 提供浏览器自动化和控制。
- [Playwright](https://github.com/microsoft/playwright) 提供浏览器自动化、控制和测试。

## License

Midscene.js 使用 [MIT 许可协议](https://github.com/web-infra-dev/midscene/blob/main/LICENSE)。
