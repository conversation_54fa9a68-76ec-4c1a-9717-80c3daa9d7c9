def _route_after_verification(self, state):
    """Route to the next node based on verification result."""
    if "verification" not in state or not state["verification"]:
        return "retry"
    
    verification = state["verification"]
    
    if verification["status"] == "success":
        # Check if all steps are completed
        current_step = state["current_step"]
        plan = state["plan"]
        
        if current_step >= len(plan):
            return "done"
        else:
            return "continue"
    else:
        # Retry the action
        return "retry"