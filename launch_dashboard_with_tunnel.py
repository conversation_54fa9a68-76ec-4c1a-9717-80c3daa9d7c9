#!/usr/bin/env python
"""
Launcher for AI Agent System with Cloudflare tunneling.
"""
import os
import sys
import subprocess
import time
import signal
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('launch_dashboard.log')
    ]
)

logger = logging.getLogger("dashboard_launcher")

# Paths
SCRIPT_DIR = Path(__file__).parent.absolute()
TUNNEL_CONFIG_FILE = SCRIPT_DIR / "cloudflare_tunnel_config.json"

# Configuration
DASHBOARD_PORT = 8000
TUNNEL_NAME = os.getenv("CLOUDFLARE_TUNNEL_NAME", "ai-agent-dashboard")

# Create tunnel config file if it doesn't exist
def create_tunnel_config():
    """Create tunnel config file if it doesn't exist."""
    if not TUNNEL_CONFIG_FILE.exists():
        import json
        config = {
            "tunnel": TUNNEL_NAME,
            "credentials-file": str(SCRIPT_DIR / ".cloudflared" / f"{TUNNEL_NAME}.json"),
            "ingress": [
                {
                    "hostname": f"{TUNNEL_NAME}.example.com",
                    "service": f"http://localhost:{DASHBOARD_PORT}"
                },
                {
                    "service": f"http://localhost:{DASHBOARD_PORT}"
                }
            ]
        }

        with open(TUNNEL_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)

        logger.info(f"Created tunnel config file at {TUNNEL_CONFIG_FILE}")

# Check if tunnel exists
def check_tunnel_exists():
    """Check if tunnel exists."""
    try:
        result = subprocess.run(
            ["cloudflared", "tunnel", "list"],
            capture_output=True,
            text=True
        )

        return TUNNEL_NAME in result.stdout
    except Exception as e:
        logger.error(f"Error checking tunnel existence: {e}")
        return False

# Create tunnel if it doesn't exist
def create_tunnel():
    """Create tunnel if it doesn't exist."""
    if not check_tunnel_exists():
        try:
            logger.info(f"Creating tunnel '{TUNNEL_NAME}'...")
            result = subprocess.run(
                ["cloudflared", "tunnel", "create", TUNNEL_NAME],
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                logger.error(f"Failed to create tunnel: {result.stderr}")
                return False

            logger.info(f"Tunnel created: {result.stdout}")
            return True
        except Exception as e:
            logger.error(f"Error creating tunnel: {e}")
            return False
    else:
        logger.info(f"Tunnel '{TUNNEL_NAME}' already exists")
        return True

# Run dashboard
def run_dashboard():
    """Run AI Agent System dashboard."""
    try:
        logger.info("Starting AI Agent System dashboard...")
        dashboard_process = subprocess.Popen([
            sys.executable,
            "main.py",
            "--web",
            f"--port={DASHBOARD_PORT}"
        ])

        logger.info(f"Dashboard started with PID {dashboard_process.pid}")
        return dashboard_process
    except Exception as e:
        logger.error(f"Error starting dashboard: {e}")
        return None

# Run tunnel
def run_tunnel():
    """Run Cloudflare tunnel."""
    try:
        logger.info(f"Starting Cloudflare tunnel '{TUNNEL_NAME}'...")
        tunnel_process = subprocess.Popen([
            "cloudflared",
            "tunnel",
            "--config",
            str(TUNNEL_CONFIG_FILE),
            "run",
            TUNNEL_NAME
        ])

        logger.info(f"Tunnel started with PID {tunnel_process.pid}")
        return tunnel_process
    except Exception as e:
        logger.error(f"Error starting tunnel: {e}")
        return None

def main():
    """Main function."""
    # Make sure we're in the virtual environment
    if "VIRTUAL_ENV" not in os.environ:
        logger.warning("Not running in a virtual environment. Some features might not work as expected.")

    # Create tunnel config
    create_tunnel_config()

    # Create tunnel if needed
    if not create_tunnel():
        logger.error("Failed to create or verify tunnel. Exiting.")
        return 1

    # Start processes
    dashboard_process = run_dashboard()
    if not dashboard_process:
        logger.error("Failed to start dashboard. Exiting.")
        return 1

    time.sleep(5)  # Give dashboard time to start

    tunnel_process = run_tunnel()
    if not tunnel_process:
        logger.error("Failed to start tunnel. Terminating dashboard and exiting.")
        dashboard_process.terminate()
        return 1

    # Setup signal handling for clean exit
    def signal_handler(sig, frame):
        logger.info("Received signal to terminate. Shutting down...")
        tunnel_process.terminate()
        dashboard_process.terminate()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Print tunnel info
    try:
        logger.info("Getting tunnel URL...")
        time.sleep(5)  # Give tunnel time to start

        result = subprocess.run(
            ["cloudflared", "tunnel", "info", TUNNEL_NAME],
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            logger.info(f"Tunnel info: {result.stdout}")
            import re
            url_match = re.search(r"https://[^\s]+", result.stdout)
            if url_match:
                logger.info(f"Dashboard is accessible at: {url_match.group(0)}")
    except Exception as e:
        logger.error(f"Error getting tunnel info: {e}")

    # Wait for processes to finish
    logger.info("Services are running. Press Ctrl+C to exit.")
    try:
        dashboard_process.wait()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt. Shutting down...")
        tunnel_process.terminate()
        dashboard_process.terminate()

    return 0

if __name__ == "__main__":
    sys.exit(main())
