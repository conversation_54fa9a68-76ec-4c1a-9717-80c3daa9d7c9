"""
Quick test script to verify browser automation is working
"""
import subprocess
import sys

def test_browser_automation():
    print("Testing Browser Automation Setup...")

    # Test script
    test_script = """
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import time

print("TESTING: Initializing Chrome browser...")

chrome_options = Options()
chrome_options.add_argument("--headless")  # Run headless for testing
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

try:
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)

    print("TESTING: Browser initialized successfully")

    # Test navigation
    driver.get("https://www.google.com")
    title = driver.title
    print(f"TESTING: Navigated to Google, title: {title}")

    driver.quit()
    print("TESTING: Browser automation test PASSED")

except Exception as e:
    print(f"TESTING: Browser automation test FAILED - {str(e)}")
"""

    try:
        # Write test script
        with open("temp_test.py", "w") as f:
            f.write(test_script)

        # Execute test
        result = subprocess.run([sys.executable, "temp_test.py"],
                              capture_output=True, text=True, timeout=30)

        print("Test Output:")
        print(result.stdout)
        if result.stderr:
            print("Test Errors:")
            print(result.stderr)

        # Clean up
        import os
        if os.path.exists("temp_test.py"):
            os.remove("temp_test.py")

        if result.returncode == 0:
            print("\n🎉 Browser automation is ready to use!")
            return True
        else:
            print("\n❌ Browser automation test failed")
            return False

    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_browser_automation()
