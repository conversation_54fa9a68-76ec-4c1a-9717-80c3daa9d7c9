{"name": "jiti", "version": "1.21.7", "description": "Runtime typescript and ESM support for Node.js", "repository": "unjs/jiti", "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": "bin/jiti.js", "files": ["lib", "dist", "register.js"], "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish --tag 1x", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/plugin-transform-typescript": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/template": "^7.25.9", "@babel/types": "^7.26.3", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^22.10.2", "@types/object-hash": "^3.0.6", "@types/resolve": "^1.20.6", "@types/semver": "^7.5.8", "@vitest/coverage-v8": "^2.1.8", "acorn": "^8.14.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.7", "config": "^3.3.12", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^9.5.2", "fast-glob": "^3.3.2", "mlly": "^1.7.3", "object-hash": "^3.0.0", "pathe": "^1.1.2", "pirates": "^4.0.6", "pkg-types": "^1.2.1", "prettier": "^3.4.2", "reflect-metadata": "^0.2.2", "semver": "^7.6.3", "std-env": "^3.8.0", "terser-webpack-plugin": "^5.3.11", "ts-loader": "^9.5.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vite": "^6.0.3", "vitest": "^2.1.8", "webpack": "^5.97.1", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@9.15.0"}