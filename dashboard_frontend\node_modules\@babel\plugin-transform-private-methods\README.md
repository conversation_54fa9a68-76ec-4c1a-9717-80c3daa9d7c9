# @babel/plugin-transform-private-methods

> This plugin transforms private class methods

See our website [@babel/plugin-transform-private-methods](https://babeljs.io/docs/babel-plugin-transform-private-methods) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-private-methods
```

or using yarn:

```sh
yarn add @babel/plugin-transform-private-methods --dev
```
