// Service worker for Midscene Extension

// Load API keys and MCP server URL from Chrome storage
chrome.storage.sync.get(
  ['GOOGLE_API_KEY', 'OPENAI_API_KEY', 'MCP_SERVER_URL'],
  (items) => {
    const GOOGLE_API_KEY = items.GOOGLE_API_KEY || 'Missing Google API Key';
    const OPENAI_API_KEY = items.OPENAI_API_KEY || 'Missing OpenAI API Key';
    const MCP_SERVER_URL = items.MCP_SERVER_URL || 'Missing MCP Server URL';

    if (
      !items.GOOGLE_API_KEY ||
      !items.OPENAI_API_KEY ||
      !items.MCP_SERVER_URL
    ) {
      console.error(
        'One or more environment variables are missing. Please set them in Chrome storage.',
      );
      return;
    }

    // Log the loaded values (for debugging purposes only, remove in production)
    console.log('Google API Key:', GOOGLE_API_KEY);
    console.log('OpenAI API Key:', OPENAI_API_KEY);
    console.log('MCP Server URL:', MCP_SERVER_URL);

    // Example: Fetch data from MCP server
    async function fetchData() {
      try {
        console.log('Fetching data from:', `${MCP_SERVER_URL}/api/data`);
        const response = await fetch(`${MCP_SERVER_URL}/api/data`, {
          headers: {
            Authorization: `Bearer ${OPENAI_API_KEY}`,
          },
        });
        console.log('Response status:', response.status);
        const data = await response.json();
        console.log('Data from MCP server:', data);
      } catch (error) {
        console.error('Error fetching data from MCP server:', error);
      }
    }

    // Call fetchData on service worker startup
    fetchData();
  },
);
