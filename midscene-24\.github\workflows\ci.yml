name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

permissions:
  contents: read


jobs:
  main:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.19.0]

    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      OPENAI_BASE_URL: ${{ secrets.OPENAI_BASE_URL }}
      MIDSCENE_MODEL_NAME: ${{ secrets.MIDSCENE_MODEL_NAME }}

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0
  
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: <PERSON><PERSON> Puppeteer dependencies
      uses: actions/cache@v3
      id: cache-puppeteer
      with:
        path: ~/.cache/puppeteer
        key: ${{ runner.os }}-puppeteer-${{ hashFiles('pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-puppeteer-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile --ignore-scripts
    
    - name: Install puppeteer dependencies
      run: |
        cd packages/web-integration
        npx puppeteer browsers install chrome

    - name: Build project
      run: pnpm run build

    - name: Run tests
      run: pnpm run test
