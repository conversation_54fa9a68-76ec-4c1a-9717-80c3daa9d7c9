// Copyright 2016 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.!

#ifndef UNICODE_SCRIPT_H_
#define UNICODE_SCRIPT_H_

#include "common.h"

namespace sentencepiece {
namespace unicode_script {
enum ScriptType : int32_t {
  U_Adlam,
  U_Ahom,
  U_Anatolian_Hieroglyphs,
  U_Arabic,
  U_Armenian,
  U_Avestan,
  U_Balinese,
  U_Bamum,
  U_Bassa_Vah,
  U_Batak,
  U_Bengali,
  U_<PERSON><PERSON><PERSON><PERSON>,
  U_Bopomofo,
  U_<PERSON>,
  <PERSON>_<PERSON>,
  U_<PERSON>,
  <PERSON><PERSON>,
  U_Canadian_Aboriginal,
  U_Carian,
  U_Caucasian_Albanian,
  U_<PERSON>,
  U_<PERSON>,
  U_Cherokee,
  U_Common,
  U_Coptic,
  U_Cuneiform,
  U_Cypriot,
  U_Cyrillic,
  U_Deseret,
  U_Devanagari,
  U_Duployan,
  U_Egyptian_Hieroglyphs,
  U_Elbasan,
  U_Ethiopic,
  U_Georgian,
  U_Glagolitic,
  U_Gothic,
  U_Grantha,
  U_Greek,
  U_Gujarati,
  U_Gurmukhi,
  U_Han,
  U_Hangul,
  U_Hanunoo,
  U_Hatran,
  U_Hebrew,
  U_Hiragana,
  U_Imperial_Aramaic,
  U_Inherited,
  U_Inscriptional_Pahlavi,
  U_Inscriptional_Parthian,
  U_Javanese,
  U_Kaithi,
  U_Kannada,
  U_Katakana,
  U_Kayah_Li,
  U_Kharoshthi,
  U_Khmer,
  U_Khojki,
  U_Khudawadi,
  U_Lao,
  U_Latin,
  U_Lepcha,
  U_Limbu,
  U_Linear_A,
  U_Linear_B,
  U_Lisu,
  U_Lycian,
  U_Lydian,
  U_Mahajani,
  U_Malayalam,
  U_Mandaic,
  U_Manichaean,
  U_Marchen,
  U_Meetei_Mayek,
  U_Mende_Kikakui,
  U_Meroitic_Cursive,
  U_Meroitic_Hieroglyphs,
  U_Miao,
  U_Modi,
  U_Mongolian,
  U_Mro,
  U_Multani,
  U_Myanmar,
  U_Nabataean,
  U_New_Tai_Lue,
  U_Newa,
  U_Nko,
  U_Ogham,
  U_Ol_Chiki,
  U_Old_Hungarian,
  U_Old_Italic,
  U_Old_North_Arabian,
  U_Old_Permic,
  U_Old_Persian,
  U_Old_South_Arabian,
  U_Old_Turkic,
  U_Oriya,
  U_Osage,
  U_Osmanya,
  U_Pahawh_Hmong,
  U_Palmyrene,
  U_Pau_Cin_Hau,
  U_Phags_Pa,
  U_Phoenician,
  U_Psalter_Pahlavi,
  U_Rejang,
  U_Runic,
  U_Samaritan,
  U_Saurashtra,
  U_Sharada,
  U_Shavian,
  U_Siddham,
  U_SignWriting,
  U_Sinhala,
  U_Sora_Sompeng,
  U_Sundanese,
  U_Syloti_Nagri,
  U_Syriac,
  U_Tagalog,
  U_Tagbanwa,
  U_Tai_Le,
  U_Tai_Tham,
  U_Tai_Viet,
  U_Takri,
  U_Tamil,
  U_Tangut,
  U_Telugu,
  U_Thaana,
  U_Thai,
  U_Tibetan,
  U_Tifinagh,
  U_Tirhuta,
  U_Ugaritic,
  U_Vai,
  U_Warang_Citi,
  U_Yi
};

ScriptType GetScript(char32 c);
}  // namespace unicode_script
}  // namespace sentencepiece
#endif  // UNICODE_SCRIPT
