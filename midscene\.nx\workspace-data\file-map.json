{"version": "6.0", "nxVersion": "21.1.2", "pathMappings": {}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": ".github/ISSUE_TEMPLATE/bug-report.en-US.yml", "hash": "8431854721319346434"}, {"file": ".github/ISSUE_TEMPLATE/feature-request.en-US.yml", "hash": "7004844254604520162"}, {"file": ".github/ISSUE_TEMPLATE/llm-connectivity-issue---模型连接错误.md", "hash": "17949787666428268901"}, {"file": ".github/pr-labeler.yml", "hash": "11689183948627681798"}, {"file": ".github/release.yml", "hash": "7366568934401710961"}, {"file": ".github/workflows/ai-evaluation.yml", "hash": "2997845482755412167"}, {"file": ".github/workflows/ai-unit-test.yml", "hash": "2141068611578164357"}, {"file": ".github/workflows/ai.yml", "hash": "7756412651700319810"}, {"file": ".github/workflows/ci.yml", "hash": "7534980422474765104"}, {"file": ".github/workflows/issue-close-require.yml", "hash": "17196048805816312445"}, {"file": ".github/workflows/issue-labeled.yml", "hash": "13877459736868042007"}, {"file": ".github/workflows/lint.yml", "hash": "14595550927822750553"}, {"file": ".github/workflows/pr-label.yml", "hash": "2171052545433481452"}, {"file": ".github/workflows/release.yml", "hash": "17407705924129192341"}, {"file": ".giti<PERSON>re", "hash": "15384059148843088325"}, {"file": ".husky/commit-msg", "hash": "17649910785587780476"}, {"file": ".npmrc", "hash": "14110365686756594372"}, {"file": ".prettieri<PERSON>re", "hash": "5241649441478762059"}, {"file": ".prettier<PERSON>", "hash": "14231632535905330688"}, {"file": ".vscode/settings.json", "hash": "2056098529487229721"}, {"file": "CONTRIBUTING.md", "hash": "15252665886296119433"}, {"file": "LICENSE", "hash": "1536050475177833712"}, {"file": "README.md", "hash": "2191112154780538332"}, {"file": "README.zh.md", "hash": "6304213490061773310"}, {"file": "biome.json", "hash": "12842595654699801788"}, {"file": "commitlint.config.js", "hash": "768093783767933984"}, {"file": "cspell.config.cjs", "hash": "12883391610197407788"}, {"file": "manifest.json", "hash": "8629891419867864324"}, {"file": "nx.json", "hash": "18432490310383745432"}, {"file": "package.json", "hash": "1599009295880946784"}, {"file": "pnpm-lock.yaml", "hash": "14580736649917322300"}, {"file": "pnpm-workspace.yaml", "hash": "2118831957560914809"}, {"file": "scripts/dictionary.txt", "hash": "5000187333068759512"}, {"file": "scripts/release.js", "hash": "8578157043502087799"}], "projectFileMap": {"@midscene/android": [{"file": "packages/android/.gitignore", "hash": "15578116857450152836"}, {"file": "packages/android/README.md", "hash": "12019950833402542438"}, {"file": "packages/android/bin/yadb", "hash": "12642385591691843096"}, {"file": "packages/android/modern.config.ts", "hash": "16311047121309170045"}, {"file": "packages/android/package.json", "hash": "9261863099351210187", "deps": ["npm:@modern-js/module-tools", "npm:@types/node@18.19.62", "npm:dotenv", "npm:typescript", "npm:vitest", "@midscene/core", "@midscene/shared", "@midscene/web", "npm:appium-adb"]}, {"file": "packages/android/src/agent/index.ts", "hash": "9005816439812036247"}, {"file": "packages/android/src/index.ts", "hash": "2311171413254476830"}, {"file": "packages/android/src/page/index.ts", "hash": "4936561223276366600"}, {"file": "packages/android/src/utils/index.ts", "hash": "14465803683471595014"}, {"file": "packages/android/tests/ai/ebay.test.ts", "hash": "7776751028840120644"}, {"file": "packages/android/tests/ai/setting.test.ts", "hash": "4402674316332667155"}, {"file": "packages/android/tests/ai/todo.test.ts", "hash": "2609164060670839778"}, {"file": "packages/android/tests/ai/travel.test.ts", "hash": "13727004196524248559"}, {"file": "packages/android/tsconfig.json", "hash": "13178818454793725303"}, {"file": "packages/android/vitest.config.ts", "hash": "12467849268244868633"}], "@midscene/mcp": [{"file": "packages/mcp/.gitignore", "hash": "13097120832024477453"}, {"file": "packages/mcp/README.md", "hash": "6335514386855215269"}, {"file": "packages/mcp/package.json", "hash": "16238305053168367333", "deps": ["npm:@modelcontextprotocol/inspector", "npm:@rslib/core", "npm:@types/node@18.19.62", "npm:typescript", "npm:vitest", "npm:dotenv", "@midscene/web", "@midscene/report", "@midscene/core", "@midscene/shared", "npm:@modelcontextprotocol/sdk", "npm:zod", "npm:puppeteer"]}, {"file": "packages/mcp/rslib.config.ts", "hash": "2627593262810324606"}, {"file": "packages/mcp/scripts/inspect.mjs", "hash": "13147195289562324464"}, {"file": "packages/mcp/src/index.ts", "hash": "13237890006018511858"}, {"file": "packages/mcp/src/midscene.ts", "hash": "10805315093271930865"}, {"file": "packages/mcp/src/prompts.ts", "hash": "13415972690454684549"}, {"file": "packages/mcp/src/puppeteer.ts", "hash": "778751481413766340"}, {"file": "packages/mcp/src/resources.ts", "hash": "17527162900104058017"}, {"file": "packages/mcp/src/tools.ts", "hash": "17141269812453178149"}, {"file": "packages/mcp/src/utils.ts", "hash": "5335626635845146783"}, {"file": "packages/mcp/tests/index.test.ts", "hash": "16098981063383110373"}, {"file": "packages/mcp/tests/test-login.mjs", "hash": "1844319203093686138"}, {"file": "packages/mcp/tests/tsconfig.json", "hash": "5626000896348919133"}, {"file": "packages/mcp/tsconfig.json", "hash": "1563491567485740069"}, {"file": "packages/mcp/vitest.config.ts", "hash": "17201146107034299193"}], "chrome-extension": [{"file": "apps/chrome-extension/README.md", "hash": "14964697770636531825"}, {"file": "apps/chrome-extension/manifest.json", "hash": "8618301376426686181"}, {"file": "apps/chrome-extension/package.json", "hash": "16761063795303696747", "deps": ["npm:@rsbuild/core@1.3.22", "npm:@rsbuild/plugin-less", "npm:@rsbuild/plugin-node-polyfill", "npm:@rsbuild/plugin-react", "npm:@rsbuild/plugin-type-check", "npm:@types/chrome", "npm:@types/react@18.3.23", "npm:@types/react-dom@18.3.7", "npm:archiver@6.0.2", "npm:less@4.2.2", "npm:typescript", "npm:@ant-design/icons", "@midscene/core", "@midscene/recorder", "@midscene/report", "@midscene/shared", "@midscene/visualizer", "@midscene/web", "npm:antd", "npm:canvas-confetti", "npm:dayjs", "npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:zu<PERSON>"]}, {"file": "apps/chrome-extension/rsbuild.config.ts", "hash": "2420675471152680390"}, {"file": "apps/chrome-extension/scripts/pack-extension.js", "hash": "18351680252472328119"}, {"file": "apps/chrome-extension/service_worker.js", "hash": "17327781224104656407"}, {"file": "apps/chrome-extension/src/App.less", "hash": "13673590303385798063"}, {"file": "apps/chrome-extension/src/App.tsx", "hash": "2902365706825405352"}, {"file": "apps/chrome-extension/src/component/playground.tsx", "hash": "7820458514494224244"}, {"file": "apps/chrome-extension/src/env.d.ts", "hash": "17902477445153278673"}, {"file": "apps/chrome-extension/src/extension/bridge.less", "hash": "1030715127788081200"}, {"file": "apps/chrome-extension/src/extension/bridge.tsx", "hash": "14486860832020664633"}, {"file": "apps/chrome-extension/src/extension/common.less", "hash": "3164483274791185728"}, {"file": "apps/chrome-extension/src/extension/misc.tsx", "hash": "2327142006476584062"}, {"file": "apps/chrome-extension/src/extension/popup.less", "hash": "16661025044271519594"}, {"file": "apps/chrome-extension/src/extension/popup.tsx", "hash": "3379415877330597810"}, {"file": "apps/chrome-extension/src/extension/recorder/ExportControls.tsx", "hash": "11296574479959308224"}, {"file": "apps/chrome-extension/src/extension/recorder/README.md", "hash": "7811141247686528021"}, {"file": "apps/chrome-extension/src/extension/recorder/components/ProgressModal.tsx", "hash": "3244274085348684950"}, {"file": "apps/chrome-extension/src/extension/recorder/components/RecordDetail.tsx", "hash": "18112453489772858906"}, {"file": "apps/chrome-extension/src/extension/recorder/components/RecordList.tsx", "hash": "15198093286414258914"}, {"file": "apps/chrome-extension/src/extension/recorder/components/SessionModals.tsx", "hash": "3033234093747104201"}, {"file": "apps/chrome-extension/src/extension/recorder/components/index.ts", "hash": "169170753196061826"}, {"file": "apps/chrome-extension/src/extension/recorder/generators/README.md", "hash": "17691608550720440813"}, {"file": "apps/chrome-extension/src/extension/recorder/generators/index.ts", "hash": "13657597438133979776"}, {"file": "apps/chrome-extension/src/extension/recorder/generators/playwrightGenerator.ts", "hash": "17323532448498302587"}, {"file": "apps/chrome-extension/src/extension/recorder/generators/shared/testGenerationUtils.ts", "hash": "4837163829207880695"}, {"file": "apps/chrome-extension/src/extension/recorder/generators/shared/types.ts", "hash": "6318196768508303113"}, {"file": "apps/chrome-extension/src/extension/recorder/generators/yamlGenerator.ts", "hash": "9027015733860592547"}, {"file": "apps/chrome-extension/src/extension/recorder/hooks/index.ts", "hash": "5033967725095965243"}, {"file": "apps/chrome-extension/src/extension/recorder/hooks/useLifecycleCleanup.ts", "hash": "16659990727507368676"}, {"file": "apps/chrome-extension/src/extension/recorder/hooks/useRecordingControl.ts", "hash": "12148648187923003091"}, {"file": "apps/chrome-extension/src/extension/recorder/hooks/useRecordingSession.ts", "hash": "13964866733693903300"}, {"file": "apps/chrome-extension/src/extension/recorder/hooks/useTabMonitoring.ts", "hash": "17672593568343653524"}, {"file": "apps/chrome-extension/src/extension/recorder/index.tsx", "hash": "8816462502240572423"}, {"file": "apps/chrome-extension/src/extension/recorder/logger.ts", "hash": "6590749196404299329"}, {"file": "apps/chrome-extension/src/extension/recorder/recorder.less", "hash": "12758467814244001689"}, {"file": "apps/chrome-extension/src/extension/recorder/shared/exportControlsUtils.ts", "hash": "12676164916881473529"}, {"file": "apps/chrome-extension/src/extension/recorder/types.ts", "hash": "10454508426093846747"}, {"file": "apps/chrome-extension/src/extension/recorder/utils.ts", "hash": "11892269328587101239"}, {"file": "apps/chrome-extension/src/index.tsx", "hash": "17444832320215641181"}, {"file": "apps/chrome-extension/src/scripts/blank_polyfill.ts", "hash": "15995089251912753983"}, {"file": "apps/chrome-extension/src/scripts/event-recorder-bridge.ts", "hash": "4349122721485992499"}, {"file": "apps/chrome-extension/src/scripts/stop-water-flow.ts", "hash": "11836023036514678621"}, {"file": "apps/chrome-extension/src/scripts/water-flow.ts", "hash": "10921912196790577431"}, {"file": "apps/chrome-extension/src/scripts/worker.ts", "hash": "6352436083427137293"}, {"file": "apps/chrome-extension/src/store.tsx", "hash": "7190361693910407428"}, {"file": "apps/chrome-extension/src/utils.ts", "hash": "3660787436919467350"}, {"file": "apps/chrome-extension/src/utils/eventOptimizer.ts", "hash": "11057046278501635238"}, {"file": "apps/chrome-extension/src/utils/indexedDB.ts", "hash": "4674550100722441492"}, {"file": "apps/chrome-extension/static/fonts/open-sans/Apache License.txt", "hash": "1513010955204511391"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt", "hash": "13405361138973758333"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-10-black/open-sans-10-black.png", "hash": "4022762690109436179"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt", "hash": "8918285496572017155"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-12-black/open-sans-12-black.png", "hash": "16227020828843957329"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt", "hash": "17509731953297622958"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-128-black/open-sans-128-black.png", "hash": "6526560555557404841"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt", "hash": "17760242806145087147"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-128-white/open-sans-128-white.png", "hash": "7588502251276031770"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt", "hash": "8981003640278145606"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-14-black/open-sans-14-black.png", "hash": "18224789592100448050"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt", "hash": "14430970711245030540"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-16-black/open-sans-16-black.png", "hash": "10141077623964941025"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt", "hash": "11774777900877220673"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-16-white/open-sans-16-white.png", "hash": "18403456962727054169"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt", "hash": "16897425171217907801"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-32-black/open-sans-32-black.png", "hash": "4186551999054889020"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt", "hash": "9704539175397559859"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-32-white/open-sans-32-white.png", "hash": "14478946172880258073"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt", "hash": "2474017896958986859"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-64-black/open-sans-64-black.png", "hash": "11037045387339274750"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt", "hash": "6751337557418323630"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-64-white/open-sans-64-white.png", "hash": "9737767760046538806"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt", "hash": "13153616041892293629"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-8-black/open-sans-8-black.png", "hash": "12270017317985787178"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt", "hash": "7624824435737734979"}, {"file": "apps/chrome-extension/static/fonts/open-sans/open-sans-8-white/open-sans-8-white.png", "hash": "5847058309906765927"}, {"file": "apps/chrome-extension/static/icon128.png", "hash": "7719887840315824054"}, {"file": "apps/chrome-extension/static/manifest.json", "hash": "15829188452906040506"}, {"file": "apps/chrome-extension/tsconfig.json", "hash": "15224053847990052607"}], "@midscene/web": [{"file": "packages/web-integration/.gitignore", "hash": "7911959920928671519"}, {"file": "packages/web-integration/README.md", "hash": "12019950833402542438"}, {"file": "packages/web-integration/bin/midscene-playground", "hash": "15662552547349787974"}, {"file": "packages/web-integration/modern.config.ts", "hash": "3104835274541408389"}, {"file": "packages/web-integration/package.json", "hash": "16632650235006992817", "deps": ["npm:@playwright/test", "npm:playwright", "npm:puppeteer", "npm:@modern-js/module-tools", "npm:@types/chrome", "npm:@types/cors", "npm:@types/express", "npm:@types/fs-extra", "npm:@types/js-yaml", "npm:@types/node@18.19.62", "npm:@types/semver@7.7.0", "npm:typescript", "npm:vitest", "@midscene/core", "@midscene/shared", "npm:@xmldom/xmldom", "npm:cors", "npm:dayjs", "npm:devtools-protocol@0.0.1380148", "npm:dotenv", "npm:express@4.21.2", "npm:fs-extra@11.2.0", "npm:http-server", "npm:inquirer@10.1.5", "npm:js-sha256", "npm:js-yaml@4.1.0", "npm:openai", "npm:semver", "npm:socket.io", "npm:socket.io-client"]}, {"file": "packages/web-integration/scripts/check-exports.js", "hash": "10040011747606026137"}, {"file": "packages/web-integration/src/bridge-mode/agent-cli-side.ts", "hash": "11036606403709133949"}, {"file": "packages/web-integration/src/bridge-mode/browser.ts", "hash": "5921417460644236014"}, {"file": "packages/web-integration/src/bridge-mode/common.ts", "hash": "8308245971450434635"}, {"file": "packages/web-integration/src/bridge-mode/index.ts", "hash": "1545046927847815051"}, {"file": "packages/web-integration/src/bridge-mode/io-client.ts", "hash": "17571970392617256822"}, {"file": "packages/web-integration/src/bridge-mode/io-server.ts", "hash": "12666008736205336802"}, {"file": "packages/web-integration/src/bridge-mode/page-browser-side.ts", "hash": "10207490624968991513"}, {"file": "packages/web-integration/src/chrome-extension/agent.ts", "hash": "7068171273418313030"}, {"file": "packages/web-integration/src/chrome-extension/cdpInput.ts", "hash": "8550039222264976091"}, {"file": "packages/web-integration/src/chrome-extension/dynamic-scripts.ts", "hash": "3573408301261318325"}, {"file": "packages/web-integration/src/chrome-extension/index.ts", "hash": "18429963023965676325"}, {"file": "packages/web-integration/src/chrome-extension/page.ts", "hash": "771505328919431653"}, {"file": "packages/web-integration/src/common/agent.ts", "hash": "11904701559691185770"}, {"file": "packages/web-integration/src/common/page.d.ts", "hash": "15976229208082756072"}, {"file": "packages/web-integration/src/common/plan-builder.ts", "hash": "10517831836602395857"}, {"file": "packages/web-integration/src/common/task-cache.ts", "hash": "6652341794113293807"}, {"file": "packages/web-integration/src/common/tasks.ts", "hash": "3309693129744872741"}, {"file": "packages/web-integration/src/common/ui-utils.ts", "hash": "5249260718331585734"}, {"file": "packages/web-integration/src/common/utils.ts", "hash": "1939612245235851270"}, {"file": "packages/web-integration/src/index.ts", "hash": "8860750036781538575"}, {"file": "packages/web-integration/src/page.ts", "hash": "10723986024911991941"}, {"file": "packages/web-integration/src/playground/agent.ts", "hash": "17375996041990058707"}, {"file": "packages/web-integration/src/playground/bin.ts", "hash": "17014557784046090284"}, {"file": "packages/web-integration/src/playground/index.ts", "hash": "1299319570125104731"}, {"file": "packages/web-integration/src/playground/server.ts", "hash": "3674623889256805063"}, {"file": "packages/web-integration/src/playground/static-page.ts", "hash": "12572390573941680441"}, {"file": "packages/web-integration/src/playwright/ai-fixture.ts", "hash": "17822574422055992039"}, {"file": "packages/web-integration/src/playwright/index.ts", "hash": "10118436461689685900"}, {"file": "packages/web-integration/src/playwright/page.ts", "hash": "12793475087373867337"}, {"file": "packages/web-integration/src/playwright/reporter/index.ts", "hash": "17528720291937708669"}, {"file": "packages/web-integration/src/playwright/reporter/select-cache-file.ts", "hash": "11600262051826034118"}, {"file": "packages/web-integration/src/puppeteer/agent-launcher.ts", "hash": "6895095421754607130"}, {"file": "packages/web-integration/src/puppeteer/base-page.ts", "hash": "10249262646700923892"}, {"file": "packages/web-integration/src/puppeteer/index.ts", "hash": "2674589549378367827"}, {"file": "packages/web-integration/src/puppeteer/page.ts", "hash": "15122841448219203315"}, {"file": "packages/web-integration/src/web-element.ts", "hash": "5928250280137846732"}, {"file": "packages/web-integration/src/yaml/builder.ts", "hash": "6139282690364358135"}, {"file": "packages/web-integration/src/yaml/index.ts", "hash": "5700871778718109757"}, {"file": "packages/web-integration/src/yaml/player.ts", "hash": "12925566778938298885"}, {"file": "packages/web-integration/src/yaml/utils.ts", "hash": "211072477850735943"}, {"file": "packages/web-integration/tests/ai/bridge/agent.test.ts", "hash": "6515777112413302766"}, {"file": "packages/web-integration/tests/ai/bridge/keyboard-event.test.ts", "hash": "5009707096154005894"}, {"file": "packages/web-integration/tests/ai/bridge/open-new-tab.test.ts", "hash": "14803892588084613322"}, {"file": "packages/web-integration/tests/ai/bridge/temp.test.ts", "hash": "11508931912835068597"}, {"file": "packages/web-integration/tests/ai/fixtures/ui-context.json", "hash": "1406977513933347166"}, {"file": "packages/web-integration/tests/ai/web/playwright-reporter-test/todo-report.spec.ts", "hash": "17874318698417269235"}, {"file": "packages/web-integration/tests/ai/web/playwright/ai-auto-todo.spec.ts", "hash": "9858453893769589011"}, {"file": "packages/web-integration/tests/ai/web/playwright/ai-online-order.spec.ts", "hash": "15927420246132165587"}, {"file": "packages/web-integration/tests/ai/web/playwright/ai-shop.spec.ts", "hash": "6863499691357022969"}, {"file": "packages/web-integration/tests/ai/web/playwright/fixture.ts", "hash": "16360406545208166643"}, {"file": "packages/web-integration/tests/ai/web/playwright/memory-release.spec.ts", "hash": "11820070425199592989"}, {"file": "packages/web-integration/tests/ai/web/playwright/open-new-tab.spec.ts", "hash": "3526118831317570564"}, {"file": "packages/web-integration/tests/ai/web/playwright/util.ts", "hash": "13848529240260335347"}, {"file": "packages/web-integration/tests/ai/web/puppeteer/agent.test.ts", "hash": "1203614465102957115"}, {"file": "packages/web-integration/tests/ai/web/puppeteer/e2e.test.ts", "hash": "291840487613587519"}, {"file": "packages/web-integration/tests/ai/web/puppeteer/open-new-tab.test.ts", "hash": "17094680116410026509"}, {"file": "packages/web-integration/tests/ai/web/puppeteer/query.test.ts", "hash": "11494206299136040047"}, {"file": "packages/web-integration/tests/ai/web/puppeteer/scroll.html", "hash": "14621784223744305091"}, {"file": "packages/web-integration/tests/ai/web/puppeteer/utils.ts", "hash": "14498085596489609873"}, {"file": "packages/web-integration/tests/ai/web/static/static-page.test.ts", "hash": "16334844644914393714"}, {"file": "packages/web-integration/tests/playwright.config.ts", "hash": "4149166614937956548"}, {"file": "packages/web-integration/tests/tsconfig.json", "hash": "7505414747124971468"}, {"file": "packages/web-integration/tests/unit-test/__snapshots__/agent.test.ts.snap", "hash": "14966718266178967985"}, {"file": "packages/web-integration/tests/unit-test/__snapshots__/plan-builder.test.ts.snap", "hash": "2956046621647786345"}, {"file": "packages/web-integration/tests/unit-test/__snapshots__/task-cache.test.ts.snap", "hash": "12267529345372176276"}, {"file": "packages/web-integration/tests/unit-test/__snapshots__/web-extractor.test.ts.snap", "hash": "12514496011861545337"}, {"file": "packages/web-integration/tests/unit-test/__snapshots__/yaml.test.ts.snap", "hash": "9615928672620445978"}, {"file": "packages/web-integration/tests/unit-test/agent.test.ts", "hash": "11352391070163770552"}, {"file": "packages/web-integration/tests/unit-test/bridge/io.test.ts", "hash": "8808818859454036985"}, {"file": "packages/web-integration/tests/unit-test/fixtures/cookie/httpbin.dev_cookies.json", "hash": "3499304007521583476"}, {"file": "packages/web-integration/tests/unit-test/fixtures/dump-with-invisible.json", "hash": "1197719348408479016"}, {"file": "packages/web-integration/tests/unit-test/fixtures/dump.json", "hash": "14471523214752881567"}, {"file": "packages/web-integration/tests/unit-test/fixtures/extractor/child.html", "hash": "17725075151063759577"}, {"file": "packages/web-integration/tests/unit-test/fixtures/extractor/scroll/input.png", "hash": "13600331808828426321"}, {"file": "packages/web-integration/tests/unit-test/fixtures/extractor/scroll/output.png", "hash": "1126414299366925511"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/assets/search-dark.svg", "hash": "12017576050206985142"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/assets/search.svg", "hash": "880212687021221889"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/child.html", "hash": "9624924015060666"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/index.html", "hash": "2201258299237055576"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/input.png", "hash": "13403541698316922350"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/merge-rects.html", "hash": "17786054704186462151"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/output.png", "hash": "16427213067701111101"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/scroll/input.png", "hash": "5649586787440137507"}, {"file": "packages/web-integration/tests/unit-test/fixtures/web-extractor/scroll/output.png", "hash": "17638798708489766796"}, {"file": "packages/web-integration/tests/unit-test/http-server.d.ts", "hash": "14187893848805693793"}, {"file": "packages/web-integration/tests/unit-test/page-task-executor-rightclick.test.ts", "hash": "6012609887215896495"}, {"file": "packages/web-integration/tests/unit-test/plan-builder.test.ts", "hash": "11692472953236446068"}, {"file": "packages/web-integration/tests/unit-test/playground-server.test.ts", "hash": "9391869688678792986"}, {"file": "packages/web-integration/tests/unit-test/task-cache.test.ts", "hash": "2064260978011185600"}, {"file": "packages/web-integration/tests/unit-test/util.test.ts", "hash": "15207777527791467866"}, {"file": "packages/web-integration/tests/unit-test/web-extractor.test.ts", "hash": "8342475264748837965"}, {"file": "packages/web-integration/tests/unit-test/yaml/__snapshots__/player.test.ts.snap", "hash": "14593437434290646202"}, {"file": "packages/web-integration/tests/unit-test/yaml/__snapshots__/utils.test.ts.snap", "hash": "5720186737276802352"}, {"file": "packages/web-integration/tests/unit-test/yaml/player.test.ts", "hash": "9893653739432935678"}, {"file": "packages/web-integration/tests/unit-test/yaml/server_root/index.html", "hash": "237936002733861904"}, {"file": "packages/web-integration/tests/unit-test/yaml/utils.test.ts", "hash": "3604098816265589180"}, {"file": "packages/web-integration/tsconfig.json", "hash": "902269404704574194"}, {"file": "packages/web-integration/vitest.config.ts", "hash": "3412629502166071557"}], "@midscene/android-playground": [{"file": "packages/android-playground/.gitignore", "hash": "11014019700538208705"}, {"file": "packages/android-playground/README.md", "hash": "2184070847716380651"}, {"file": "packages/android-playground/bin/android-playground", "hash": "6510760660328872492"}, {"file": "packages/android-playground/bin/server.bin", "hash": "11769298703933219925"}, {"file": "packages/android-playground/modern.config.ts", "hash": "16311047121309170045"}, {"file": "packages/android-playground/package.json", "hash": "10055390150157984201", "deps": ["npm:@modern-js/module-tools", "npm:@types/cors", "npm:@types/express", "npm:@types/node@18.19.62", "npm:typescript", "@midscene/android", "@midscene/shared", "@midscene/web", "npm:@yume-chan/adb", "npm:@yume-chan/adb-scrcpy", "npm:@yume-chan/adb-server-node-tcp", "npm:@yume-chan/scrcpy", "npm:@yume-chan/stream-extra", "npm:cors", "npm:express@4.21.2", "npm:open@10.1.0", "npm:socket.io"]}, {"file": "packages/android-playground/src/index.ts", "hash": "9590564832880248382"}, {"file": "packages/android-playground/src/scrcpy-server.ts", "hash": "6995707410781667995"}, {"file": "packages/android-playground/tsconfig.json", "hash": "13178818454793725303"}], "@midscene/cli": [{"file": "packages/cli/.gitignore", "hash": "11724367998625100003"}, {"file": "packages/cli/.npmignore", "hash": "12612294806064625054"}, {"file": "packages/cli/LICENSE", "hash": "1536050475177833712"}, {"file": "packages/cli/README.md", "hash": "7680732398219284019"}, {"file": "packages/cli/bin/midscene", "hash": "12058736291625394458"}, {"file": "packages/cli/modern.config.ts", "hash": "15677707626586826992"}, {"file": "packages/cli/package.json", "hash": "10742235543960889871", "deps": ["npm:@modern-js/module-tools", "npm:@types/js-yaml", "npm:@types/minimist", "npm:@types/node@18.19.62", "npm:@types/yargs", "npm:typescript", "npm:vitest", "npm:yargs@17.7.2", "npm:chalk", "npm:cli-spinners@3.2.0", "npm:dotenv", "npm:execa@9.3.0", "npm:glob@11.0.0", "npm:js-yaml@4.1.0", "npm:restore-cursor@5.1.0", "@midscene/android", "@midscene/core", "@midscene/web", "npm:puppeteer", "npm:http-server"]}, {"file": "packages/cli/src/args.ts", "hash": "13499651791550932448"}, {"file": "packages/cli/src/cli-utils.ts", "hash": "3563495400432666508"}, {"file": "packages/cli/src/http-server.d.ts", "hash": "14187893848805693793"}, {"file": "packages/cli/src/index.ts", "hash": "2163529797396864141"}, {"file": "packages/cli/src/printer.ts", "hash": "12452089373238418624"}, {"file": "packages/cli/src/tty-renderer.ts", "hash": "17090470732924677985"}, {"file": "packages/cli/src/yaml-runner.ts", "hash": "4352939770456362033"}, {"file": "packages/cli/tests/ai/__snapshots__/bin.test.ts.snap", "hash": "2735317737901665544"}, {"file": "packages/cli/tests/ai/bin.test.ts", "hash": "8846119705881292961"}, {"file": "packages/cli/tests/ai/bridge.test.ts", "hash": "6490661651771983338"}, {"file": "packages/cli/tests/midscene_scripts/local/local-error-message.yml", "hash": "12158152333392824890"}, {"file": "packages/cli/tests/midscene_scripts/local/local.yml", "hash": "17571877682232955925"}, {"file": "packages/cli/tests/midscene_scripts/online/online.yaml", "hash": "12531043774969094298"}, {"file": "packages/cli/tests/midscene_scripts_bridge/current_tab/check_content.yaml", "hash": "12114308174935402758"}, {"file": "packages/cli/tests/midscene_scripts_bridge/new_tab/bing.yaml", "hash": "15827743862042657706"}, {"file": "packages/cli/tests/midscene_scripts_bridge/new_tab/local.yml", "hash": "5812493310553120183"}, {"file": "packages/cli/tests/midscene_scripts_bridge/new_tab/open-new-tab.yaml", "hash": "15226870295830485838"}, {"file": "packages/cli/tests/server_root/index.html", "hash": "237936002733861904"}, {"file": "packages/cli/tests/unit-test/__snapshots__/cli-utils.test.ts.snap", "hash": "4102454085683382913"}, {"file": "packages/cli/tests/unit-test/cli-utils.test.ts", "hash": "8732410629684679399"}, {"file": "packages/cli/tsconfig.json", "hash": "14426046131469887535"}, {"file": "packages/cli/vitest.config.ts", "hash": "9379256860624790763"}], "@midscene/shared": [{"file": "packages/shared/README.md", "hash": "7680732398219284019"}, {"file": "packages/shared/modern.config.ts", "hash": "13209901212554949599"}, {"file": "packages/shared/modern.inspect.config.ts", "hash": "9818596715965859625"}, {"file": "packages/shared/package.json", "hash": "17741044855938231143", "deps": ["npm:@modern-js/module-tools", "npm:@types/debug", "npm:@types/node@18.19.62", "npm:@ui-tars/shared", "npm:dotenv", "npm:rimraf@3.0.2", "npm:typescript", "npm:vitest", "npm:debug@4.4.0", "npm:jimp", "npm:js-sha256"]}, {"file": "packages/shared/src/common.ts", "hash": "5215544265511818162"}, {"file": "packages/shared/src/constants/example-code.ts", "hash": "15194735898587771931"}, {"file": "packages/shared/src/constants/index.ts", "hash": "13969209442250761436"}, {"file": "packages/shared/src/env.ts", "hash": "16966742699075814222"}, {"file": "packages/shared/src/extractor/constants.ts", "hash": "12005739578432625625"}, {"file": "packages/shared/src/extractor/debug.ts", "hash": "16392623241580630531"}, {"file": "packages/shared/src/extractor/dom-util.ts", "hash": "7199777515059965786"}, {"file": "packages/shared/src/extractor/index.ts", "hash": "3058660428966036997"}, {"file": "packages/shared/src/extractor/locator.ts", "hash": "13524375258253135639"}, {"file": "packages/shared/src/extractor/tree.ts", "hash": "8815774883443100366"}, {"file": "packages/shared/src/extractor/util.ts", "hash": "14289762257436877435"}, {"file": "packages/shared/src/extractor/web-extractor.ts", "hash": "12608557448907922870"}, {"file": "packages/shared/src/img/box-select.ts", "hash": "10012810902088772748"}, {"file": "packages/shared/src/img/draw-box.ts", "hash": "11606637850769623970"}, {"file": "packages/shared/src/img/get-jimp.ts", "hash": "18358835916129355940"}, {"file": "packages/shared/src/img/index.ts", "hash": "8334464753477911224"}, {"file": "packages/shared/src/img/info.ts", "hash": "4628084510646967782"}, {"file": "packages/shared/src/img/jimp.d.ts", "hash": "15958916673157298193"}, {"file": "packages/shared/src/img/transform.ts", "hash": "6290792309295768308"}, {"file": "packages/shared/src/index.ts", "hash": "15995089251912753983"}, {"file": "packages/shared/src/logger.ts", "hash": "5838015058311791343"}, {"file": "packages/shared/src/modern-app-env.d.ts", "hash": "294344729181634630"}, {"file": "packages/shared/src/node/fs.ts", "hash": "3110418930980901591"}, {"file": "packages/shared/src/types/index.ts", "hash": "15013732436282536236"}, {"file": "packages/shared/src/us-keyboard-layout.ts", "hash": "1192006668566041200"}, {"file": "packages/shared/src/utils.ts", "hash": "14212633057129013470"}, {"file": "packages/shared/tests/fixtures/2x2.jpeg", "hash": "2903519172198342567"}, {"file": "packages/shared/tests/fixtures/baidu.png", "hash": "1644033978861333241"}, {"file": "packages/shared/tests/fixtures/colorful.png", "hash": "582365561266087481"}, {"file": "packages/shared/tests/fixtures/dump.json", "hash": "14623009288275064331"}, {"file": "packages/shared/tests/fixtures/heytea.jpeg", "hash": "6185420991624560674"}, {"file": "packages/shared/tests/fixtures/icon.png", "hash": "5046882236609742775"}, {"file": "packages/shared/tests/fixtures/long-text.png", "hash": "13809632096161477485"}, {"file": "packages/shared/tests/fixtures/reference-of-list.png", "hash": "5459993466814271561"}, {"file": "packages/shared/tests/fixtures/reference.png", "hash": "12110560398518061126"}, {"file": "packages/shared/tests/fixtures/table.png", "hash": "10569007302377185287"}, {"file": "packages/shared/tests/tsconfig.json", "hash": "11540933689707695353"}, {"file": "packages/shared/tests/unit-test/__snapshots__/tree.test.ts.snap", "hash": "14522192771758397363"}, {"file": "packages/shared/tests/unit-test/fs.test.ts", "hash": "4751962688159268634"}, {"file": "packages/shared/tests/unit-test/image/__snapshots__/index.test.ts.snap", "hash": "15155703077808933896"}, {"file": "packages/shared/tests/unit-test/image/index.test.ts", "hash": "13017556660758178467"}, {"file": "packages/shared/tests/unit-test/keyboard.test.ts", "hash": "8752842549604863227"}, {"file": "packages/shared/tests/unit-test/tree.test.ts", "hash": "4792276914554753761"}, {"file": "packages/shared/tests/utils.ts", "hash": "995489695094978613"}, {"file": "packages/shared/tsconfig.json", "hash": "3148820082074799427"}, {"file": "packages/shared/vitest.config.ts", "hash": "6013180291696830147"}], "@midscene/recorder": [{"file": "packages/recorder/.gitignore", "hash": "7031366407337426688"}, {"file": "packages/recorder/README.md", "hash": "4589140651521709260"}, {"file": "packages/recorder/package.json", "hash": "4586643705809335393", "deps": ["npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:@rsbuild/plugin-react", "npm:@rslib/core", "npm:@types/react@18.3.23", "npm:typescript", "npm:@ant-design/icons", "npm:antd", "npm:dayjs", "@midscene/shared"]}, {"file": "packages/recorder/rslib.config.ts", "hash": "17145573137944861504"}, {"file": "packages/recorder/src/Button.tsx", "hash": "5343402233726757861"}, {"file": "packages/recorder/src/RecordTimeline.css", "hash": "9745269742580780558"}, {"file": "packages/recorder/src/RecordTimeline.tsx", "hash": "13362178944120051058"}, {"file": "packages/recorder/src/button.css", "hash": "1671096309313684282"}, {"file": "packages/recorder/src/index.tsx", "hash": "17846159673191015394"}, {"file": "packages/recorder/src/recorder-iife-index.ts", "hash": "9845913997802120925"}, {"file": "packages/recorder/src/recorder.ts", "hash": "777800775046463632"}, {"file": "packages/recorder/tsconfig.json", "hash": "3592236710511592570"}], "@midscene/evaluation": [{"file": "packages/evaluation/.gitignore", "hash": "1288058080406481055"}, {"file": "packages/evaluation/README.md", "hash": "4066653781112047736"}, {"file": "packages/evaluation/data-generator/fixture.ts", "hash": "11262994566799254813"}, {"file": "packages/evaluation/data-generator/generator-headed.spec.ts", "hash": "13632958108743824051"}, {"file": "packages/evaluation/data-generator/generator-headless.spec.ts", "hash": "12482398860247405157"}, {"file": "packages/evaluation/data-generator/utils.ts", "hash": "367719746244879958"}, {"file": "packages/evaluation/package.json", "hash": "13214360265420313535", "deps": ["npm:@playwright/test", "npm:cli-progress", "npm:dotenv", "npm:playwright", "npm:sharp@0.34.1", "npm:typescript", "npm:vitest", "@midscene/core", "@midscene/shared", "@midscene/web"]}, {"file": "packages/evaluation/page-cases/assertion/online_order.json", "hash": "8253685882999667435"}, {"file": "packages/evaluation/page-cases/assertion/online_order_list.json", "hash": "12567357990195279439"}, {"file": "packages/evaluation/page-cases/inspect/antd-carousel.json", "hash": "15100707402771333607"}, {"file": "packages/evaluation/page-cases/inspect/antd-carousel.json-coordinates-annotated.png", "hash": "4575313566941873493"}, {"file": "packages/evaluation/page-cases/inspect/aweme-login.json", "hash": "15040204904734850102"}, {"file": "packages/evaluation/page-cases/inspect/aweme-login.json-coordinates-annotated.png", "hash": "1719653528897197528"}, {"file": "packages/evaluation/page-cases/inspect/aweme-play.json", "hash": "13073426651762320158"}, {"file": "packages/evaluation/page-cases/inspect/aweme-play.json-coordinates-annotated.png", "hash": "14405418880660090369"}, {"file": "packages/evaluation/page-cases/inspect/online_order.json", "hash": "6016686711278901177"}, {"file": "packages/evaluation/page-cases/inspect/online_order.json-coordinates-annotated.png", "hash": "1712375214155608204"}, {"file": "packages/evaluation/page-cases/inspect/online_order_list.json", "hash": "4466146863785173502"}, {"file": "packages/evaluation/page-cases/inspect/online_order_list.json-coordinates-annotated.png", "hash": "10866607194227749257"}, {"file": "packages/evaluation/page-cases/inspect/taobao.json", "hash": "17553348031053650287"}, {"file": "packages/evaluation/page-cases/inspect/taobao.json-coordinates-annotated.png", "hash": "11547260405945792219"}, {"file": "packages/evaluation/page-cases/inspect/todo.json", "hash": "5886347202712853448"}, {"file": "packages/evaluation/page-cases/inspect/todo.json-coordinates-annotated.png", "hash": "16711410075654640506"}, {"file": "packages/evaluation/page-cases/planning/antd-form-vl.json", "hash": "12112339043065190435"}, {"file": "packages/evaluation/page-cases/planning/antd-form-vl.json-planning-coordinates-annotated.png", "hash": "18229884588588460805"}, {"file": "packages/evaluation/page-cases/planning/antd-tooltip-vl.json", "hash": "9794646708050398566"}, {"file": "packages/evaluation/page-cases/planning/antd-tooltip-vl.json-planning-coordinates-annotated.png", "hash": "2677932087840122825"}, {"file": "packages/evaluation/page-cases/planning/aweme-login-vl.json", "hash": "3219797044289324053"}, {"file": "packages/evaluation/page-cases/planning/todo-vl.json", "hash": "2495958184257882391"}, {"file": "packages/evaluation/page-cases/planning/todo-vl.json-planning-coordinates-annotated.png", "hash": "16196691941685126201"}, {"file": "packages/evaluation/page-cases/planning/todo.json", "hash": "10391219052033571009"}, {"file": "packages/evaluation/page-cases/section-locator/antd-tooltip.json", "hash": "5171466479350579619"}, {"file": "packages/evaluation/page-cases/section-locator/antd-tooltip.json-coordinates-annotated.png", "hash": "14682116323461437133"}, {"file": "packages/evaluation/page-data/antd-carousel/element-snapshot.json", "hash": "5274092291310652897"}, {"file": "packages/evaluation/page-data/antd-carousel/element-tree.json", "hash": "88305671609540430"}, {"file": "packages/evaluation/page-data/antd-carousel/element-tree.txt", "hash": "10355294030327274933"}, {"file": "packages/evaluation/page-data/antd-carousel/input.png", "hash": "4563995406666656532"}, {"file": "packages/evaluation/page-data/antd-carousel/output.png", "hash": "18334203129862137618"}, {"file": "packages/evaluation/page-data/antd-carousel/output_without_text.png", "hash": "8948155470938352095"}, {"file": "packages/evaluation/page-data/antd-carousel/resize-output.png", "hash": "6377913127623573230"}, {"file": "packages/evaluation/page-data/antd-form/element-snapshot.json", "hash": "3279959114388690887"}, {"file": "packages/evaluation/page-data/antd-form/element-tree.json", "hash": "8408315503409318249"}, {"file": "packages/evaluation/page-data/antd-form/element-tree.txt", "hash": "5599012849970557550"}, {"file": "packages/evaluation/page-data/antd-form/input.png", "hash": "538857952391288469"}, {"file": "packages/evaluation/page-data/antd-form/output.png", "hash": "1644310685664873066"}, {"file": "packages/evaluation/page-data/antd-form/output_without_text.png", "hash": "10429232627625241625"}, {"file": "packages/evaluation/page-data/antd-form/resize-output.png", "hash": "12378481287093456566"}, {"file": "packages/evaluation/page-data/antd-pagination/element-snapshot.json", "hash": "7815105767920895591"}, {"file": "packages/evaluation/page-data/antd-pagination/element-tree.json", "hash": "16170371193967214383"}, {"file": "packages/evaluation/page-data/antd-pagination/element-tree.txt", "hash": "3317319857904829896"}, {"file": "packages/evaluation/page-data/antd-pagination/input.png", "hash": "3509653470733433504"}, {"file": "packages/evaluation/page-data/antd-pagination/output.png", "hash": "4227655506212485507"}, {"file": "packages/evaluation/page-data/antd-pagination/output_without_text.png", "hash": "15810655386001746237"}, {"file": "packages/evaluation/page-data/antd-pagination/resize-output.png", "hash": "18354439461623160299"}, {"file": "packages/evaluation/page-data/antd-tooltip/element-snapshot.json", "hash": "1339859782800307789"}, {"file": "packages/evaluation/page-data/antd-tooltip/element-tree.json", "hash": "7078379712634684675"}, {"file": "packages/evaluation/page-data/antd-tooltip/element-tree.txt", "hash": "8118913154043600643"}, {"file": "packages/evaluation/page-data/antd-tooltip/input.png", "hash": "15088358855507570738"}, {"file": "packages/evaluation/page-data/antd-tooltip/output.png", "hash": "7488863194251639294"}, {"file": "packages/evaluation/page-data/antd-tooltip/output_without_text.png", "hash": "16890498616288900071"}, {"file": "packages/evaluation/page-data/antd-tooltip/resize-output.png", "hash": "16462353896554837629"}, {"file": "packages/evaluation/page-data/aweme-login/element-snapshot.json", "hash": "6452908912544438515"}, {"file": "packages/evaluation/page-data/aweme-login/element-tree.json", "hash": "2365771307291436905"}, {"file": "packages/evaluation/page-data/aweme-login/element-tree.txt", "hash": "2598121573801226577"}, {"file": "packages/evaluation/page-data/aweme-login/input.png", "hash": "9271282692535716926"}, {"file": "packages/evaluation/page-data/aweme-login/output.png", "hash": "14225565636651844411"}, {"file": "packages/evaluation/page-data/aweme-login/output_without_text.png", "hash": "8321801319985116722"}, {"file": "packages/evaluation/page-data/aweme-login/resize-output.png", "hash": "2999323235157087608"}, {"file": "packages/evaluation/page-data/aweme-play/element-snapshot.json", "hash": "12649060895451668119"}, {"file": "packages/evaluation/page-data/aweme-play/element-tree.json", "hash": "329939570486370790"}, {"file": "packages/evaluation/page-data/aweme-play/element-tree.txt", "hash": "7633140663356935626"}, {"file": "packages/evaluation/page-data/aweme-play/input.png", "hash": "1325522094149998258"}, {"file": "packages/evaluation/page-data/aweme-play/output.png", "hash": "14779701086251636588"}, {"file": "packages/evaluation/page-data/aweme-play/output_without_text.png", "hash": "286309214642902333"}, {"file": "packages/evaluation/page-data/aweme-play/resize-output.png", "hash": "12276492716356854439"}, {"file": "packages/evaluation/page-data/githubstatus/element-snapshot.json", "hash": "2595917075578637879"}, {"file": "packages/evaluation/page-data/githubstatus/element-tree.json", "hash": "66664661177114180"}, {"file": "packages/evaluation/page-data/githubstatus/element-tree.txt", "hash": "10155967781285785575"}, {"file": "packages/evaluation/page-data/githubstatus/input.png", "hash": "424436501123320664"}, {"file": "packages/evaluation/page-data/githubstatus/output.png", "hash": "11947653146937426359"}, {"file": "packages/evaluation/page-data/githubstatus/output_without_text.png", "hash": "4332732270145915134"}, {"file": "packages/evaluation/page-data/githubstatus/resize-output.png", "hash": "1998305121334041422"}, {"file": "packages/evaluation/page-data/image-only/.gitignore", "hash": "1462820302700226431"}, {"file": "packages/evaluation/page-data/online_order/element-snapshot.json", "hash": "13036581708270279977"}, {"file": "packages/evaluation/page-data/online_order/element-tree.json", "hash": "8254802377444260205"}, {"file": "packages/evaluation/page-data/online_order/element-tree.txt", "hash": "11355373726493869562"}, {"file": "packages/evaluation/page-data/online_order/input.png", "hash": "4350687722373249100"}, {"file": "packages/evaluation/page-data/online_order/output.png", "hash": "10638489016230952240"}, {"file": "packages/evaluation/page-data/online_order/output_without_text.png", "hash": "17495305388919934457"}, {"file": "packages/evaluation/page-data/online_order/resize-output.png", "hash": "6029068591758922705"}, {"file": "packages/evaluation/page-data/online_order_list/element-snapshot.json", "hash": "2781260187706804422"}, {"file": "packages/evaluation/page-data/online_order_list/element-tree.json", "hash": "10803619822799386618"}, {"file": "packages/evaluation/page-data/online_order_list/element-tree.txt", "hash": "8702610859146183367"}, {"file": "packages/evaluation/page-data/online_order_list/input.png", "hash": "4061238054229653861"}, {"file": "packages/evaluation/page-data/online_order_list/output.png", "hash": "1064908420974469843"}, {"file": "packages/evaluation/page-data/online_order_list/output_without_text.png", "hash": "13931610676544666205"}, {"file": "packages/evaluation/page-data/online_order_list/resize-output.png", "hash": "18032880997919743867"}, {"file": "packages/evaluation/page-data/taobao/element-snapshot.json", "hash": "6119116655834454374"}, {"file": "packages/evaluation/page-data/taobao/element-tree.json", "hash": "6127837962712869622"}, {"file": "packages/evaluation/page-data/taobao/element-tree.txt", "hash": "15867398112276018778"}, {"file": "packages/evaluation/page-data/taobao/input.png", "hash": "13301528290731132431"}, {"file": "packages/evaluation/page-data/taobao/output.png", "hash": "11860467493866163482"}, {"file": "packages/evaluation/page-data/taobao/output_without_text.png", "hash": "15897163120429620181"}, {"file": "packages/evaluation/page-data/taobao/resize-output.png", "hash": "8515355778878643233"}, {"file": "packages/evaluation/page-data/todo-input-with-value/element-snapshot.json", "hash": "7276570758055912564"}, {"file": "packages/evaluation/page-data/todo-input-with-value/element-tree.json", "hash": "2156070226158743544"}, {"file": "packages/evaluation/page-data/todo-input-with-value/element-tree.txt", "hash": "14611752096027398896"}, {"file": "packages/evaluation/page-data/todo-input-with-value/input.png", "hash": "15414122494990972831"}, {"file": "packages/evaluation/page-data/todo-input-with-value/output.png", "hash": "4933356239157108410"}, {"file": "packages/evaluation/page-data/todo-input-with-value/output_without_text.png", "hash": "16360453408753740416"}, {"file": "packages/evaluation/page-data/todo-input-with-value/resize-output.png", "hash": "3280044314687870025"}, {"file": "packages/evaluation/page-data/todo/element-snapshot.json", "hash": "1003170365462737093"}, {"file": "packages/evaluation/page-data/todo/element-tree.json", "hash": "7792232409762930510"}, {"file": "packages/evaluation/page-data/todo/element-tree.txt", "hash": "1111691183613412955"}, {"file": "packages/evaluation/page-data/todo/input.png", "hash": "7130683106372778135"}, {"file": "packages/evaluation/page-data/todo/output.png", "hash": "3575510899430422526"}, {"file": "packages/evaluation/page-data/todo/output_without_text.png", "hash": "14128050858515175414"}, {"file": "packages/evaluation/page-data/todo/resize-output.png", "hash": "10988505373579410880"}, {"file": "packages/evaluation/page-data/visualstudio/element-snapshot.json", "hash": "17359524045145467037"}, {"file": "packages/evaluation/page-data/visualstudio/element-tree.json", "hash": "15923211595150637121"}, {"file": "packages/evaluation/page-data/visualstudio/element-tree.txt", "hash": "18296051510934774071"}, {"file": "packages/evaluation/page-data/visualstudio/input.png", "hash": "2464508387323846934"}, {"file": "packages/evaluation/page-data/visualstudio/output.png", "hash": "15942313232622513489"}, {"file": "packages/evaluation/page-data/visualstudio/output_without_text.png", "hash": "18197707653789604190"}, {"file": "packages/evaluation/page-data/visualstudio/resize-output.png", "hash": "14320468929698757582"}, {"file": "packages/evaluation/playwright.config.ts", "hash": "18021958511970988867"}, {"file": "packages/evaluation/src/test-analyzer.ts", "hash": "12910394647031047461"}, {"file": "packages/evaluation/tests/assertion.test.ts", "hash": "17262852643241425904"}, {"file": "packages/evaluation/tests/llm-locator.test.ts", "hash": "15002612902949300024"}, {"file": "packages/evaluation/tests/llm-planning.test.ts", "hash": "17572969636067622050"}, {"file": "packages/evaluation/tests/llm-section-locator.test.ts", "hash": "9475063449505762026"}, {"file": "packages/evaluation/tests/screenspot-v2-evaluation.test.ts", "hash": "12617199286536276471"}, {"file": "packages/evaluation/tests/util.ts", "hash": "2585549887986569368"}, {"file": "packages/evaluation/tsconfig.json", "hash": "1628507694048848140"}, {"file": "packages/evaluation/vitest.config.ts", "hash": "3979969246462013015"}], "@midscene/core": [{"file": "packages/core/.gitignore", "hash": "893504813350023537"}, {"file": "packages/core/.npmignore", "hash": "12612294806064625054"}, {"file": "packages/core/LICENSE", "hash": "1536050475177833712"}, {"file": "packages/core/README.md", "hash": "5025972156232664724"}, {"file": "packages/core/modern.config.ts", "hash": "11302950111319060674"}, {"file": "packages/core/package.json", "hash": "116802993171052990", "deps": ["npm:@modern-js/module-tools", "npm:@types/node@18.19.62", "npm:@types/node-fetch", "npm:typescript", "npm:vitest", "npm:@anthropic-ai/sdk", "npm:@azure/identity", "npm:@langchain/core", "@midscene/recorder", "@midscene/shared", "npm:@ui-tars/action-parser", "npm:dotenv", "npm:https-proxy-agent@7.0.2", "npm:jsonrepair", "npm:langsmith@0.3.7", "npm:openai", "npm:socks-proxy-agent@8.0.4"]}, {"file": "packages/core/src/ai-model/action-executor.ts", "hash": "10973412290365243633"}, {"file": "packages/core/src/ai-model/common.ts", "hash": "8701243619959645853"}, {"file": "packages/core/src/ai-model/index.ts", "hash": "11881254558716631546"}, {"file": "packages/core/src/ai-model/inspect.ts", "hash": "7110989704992388395"}, {"file": "packages/core/src/ai-model/llm-planning.ts", "hash": "11620263916871858796"}, {"file": "packages/core/src/ai-model/prompt/assertion.ts", "hash": "6435592957786820679"}, {"file": "packages/core/src/ai-model/prompt/common.ts", "hash": "12264800787161963435"}, {"file": "packages/core/src/ai-model/prompt/describe.ts", "hash": "17908567987501282038"}, {"file": "packages/core/src/ai-model/prompt/extraction.ts", "hash": "14750679390647742576"}, {"file": "packages/core/src/ai-model/prompt/llm-locator.ts", "hash": "16343697013633305568"}, {"file": "packages/core/src/ai-model/prompt/llm-planning.ts", "hash": "3077591385426495901"}, {"file": "packages/core/src/ai-model/prompt/llm-section-locator.ts", "hash": "6465798670614546804"}, {"file": "packages/core/src/ai-model/prompt/playwright-generator.ts", "hash": "3272045132616227247"}, {"file": "packages/core/src/ai-model/prompt/ui-tars-locator.ts", "hash": "4983662240780315885"}, {"file": "packages/core/src/ai-model/prompt/ui-tars-planning.ts", "hash": "3859722661641599965"}, {"file": "packages/core/src/ai-model/prompt/util.ts", "hash": "16962176847783434922"}, {"file": "packages/core/src/ai-model/prompt/yaml-generator.ts", "hash": "9767683696605003400"}, {"file": "packages/core/src/ai-model/service-caller/index.ts", "hash": "15280220734427226961"}, {"file": "packages/core/src/ai-model/service-caller/types.d.ts", "hash": "13556915266215307845"}, {"file": "packages/core/src/ai-model/ui-tars-planning.ts", "hash": "15474121366802871811"}, {"file": "packages/core/src/image/index.ts", "hash": "15486142888902117813"}, {"file": "packages/core/src/index.ts", "hash": "17580177620549246545"}, {"file": "packages/core/src/insight/index.ts", "hash": "2053532335562567650"}, {"file": "packages/core/src/insight/utils.ts", "hash": "9769800325489778097"}, {"file": "packages/core/src/tree.ts", "hash": "18068030931009347564"}, {"file": "packages/core/src/types.ts", "hash": "2446675192627138083"}, {"file": "packages/core/src/utils.ts", "hash": "12456765218182215531"}, {"file": "packages/core/src/yaml.ts", "hash": "962591575906184118"}, {"file": "packages/core/tests/ai/assert/assert.test.ts", "hash": "1574027153135648450"}, {"file": "packages/core/tests/ai/connectivity.test.ts", "hash": "6055318185608433829"}, {"file": "packages/core/tests/ai/extract/__snapshots__/extract.test.ts.snap", "hash": "133082102824185802"}, {"file": "packages/core/tests/ai/extract/extract.test.ts", "hash": "4273790458678101668"}, {"file": "packages/core/tests/ai/insight/insight.test.ts", "hash": "1855258996886965774"}, {"file": "packages/core/tests/ai/llm-inspect.test.ts", "hash": "17756038303575564299"}, {"file": "packages/core/tests/ai/llm-planning/__snapshots__/basic.test.ts.snap", "hash": "7384268766032203287"}, {"file": "packages/core/tests/ai/llm-planning/__snapshots__/input.test.ts.snap", "hash": "1010155978199320622"}, {"file": "packages/core/tests/ai/llm-planning/__snapshots__/planning-input.test.ts.snap", "hash": "17522657927167230589"}, {"file": "packages/core/tests/ai/llm-planning/__snapshots__/planning.test.ts.snap", "hash": "12658000023311529781"}, {"file": "packages/core/tests/ai/llm-planning/basic.test.ts", "hash": "9912062580069421158"}, {"file": "packages/core/tests/ai/llm-planning/input.test.ts", "hash": "18412682276531745145"}, {"file": "packages/core/tests/ai/llm-section-locator.test.ts", "hash": "8773911424425057849"}, {"file": "packages/core/tests/ai/parse-action.test.ts", "hash": "2600508716553486217"}, {"file": "packages/core/tests/ai/ui-tars-planning/output.png", "hash": "11433451685062210432"}, {"file": "packages/core/tests/ai/ui-tars-planning/plan-to-target.test.ts", "hash": "5282973730733928005"}, {"file": "packages/core/tests/evaluation.ts", "hash": "3424558897628038283"}, {"file": "packages/core/tests/fixtures/baidu.png", "hash": "1644033978861333241"}, {"file": "packages/core/tests/fixtures/dump-for-utils-test.json", "hash": "4389078897111215324"}, {"file": "packages/core/tests/fixtures/dump.json", "hash": "14623009288275064331"}, {"file": "packages/core/tests/tsconfig.json", "hash": "6802760196593597334"}, {"file": "packages/core/tests/unit-test/__snapshots__/llm-planning.test.ts.snap", "hash": "3367878553804633946"}, {"file": "packages/core/tests/unit-test/__snapshots__/tree.test.ts.snap", "hash": "11548672040837102839"}, {"file": "packages/core/tests/unit-test/env.test.ts", "hash": "1725651252279045226"}, {"file": "packages/core/tests/unit-test/executor/__snapshots__/index.test.ts.snap", "hash": "3262184434985958761"}, {"file": "packages/core/tests/unit-test/executor/index.test.ts", "hash": "16218488540839197215"}, {"file": "packages/core/tests/unit-test/llm-planning.test.ts", "hash": "1716823209285978840"}, {"file": "packages/core/tests/unit-test/mocks/intl-mock.ts", "hash": "3420317940605239680"}, {"file": "packages/core/tests/unit-test/prompt/__snapshots__/assertion.test.ts.snap", "hash": "6892900420859376485"}, {"file": "packages/core/tests/unit-test/prompt/__snapshots__/describe.test.ts.snap", "hash": "6875608163301608459"}, {"file": "packages/core/tests/unit-test/prompt/__snapshots__/prompt.test.ts.snap", "hash": "13904911970370606135"}, {"file": "packages/core/tests/unit-test/prompt/assertion.test.ts", "hash": "13947506825725479388"}, {"file": "packages/core/tests/unit-test/prompt/describe.test.ts", "hash": "6929343440120743715"}, {"file": "packages/core/tests/unit-test/prompt/playwright-generator.test.ts", "hash": "6077760875343137779"}, {"file": "packages/core/tests/unit-test/prompt/prompt.test.ts", "hash": "6069631678859772101"}, {"file": "packages/core/tests/unit-test/prompt/utils.test.ts", "hash": "3407705293350711006"}, {"file": "packages/core/tests/unit-test/types-rightclick.test.ts", "hash": "17505019863359492109"}, {"file": "packages/core/tests/unit-test/utils.test.ts", "hash": "16246296033845713477"}, {"file": "packages/core/tests/utils.ts", "hash": "7895308599431105686"}, {"file": "packages/core/third-party-licenses.txt", "hash": "16258698638460790472"}, {"file": "packages/core/tsconfig.json", "hash": "18358431546005423136"}, {"file": "packages/core/vitest.config.ts", "hash": "11817817224630551474"}], "recorder-form": [{"file": "apps/recorder-form/.gitignore", "hash": "7031366407337426688"}, {"file": "apps/recorder-form/README.md", "hash": "2523373150815898956"}, {"file": "apps/recorder-form/package.json", "hash": "16469276594764166166", "deps": ["npm:@rsbuild/plugin-node-polyfill", "npm:@rsbuild/core@1.3.22", "npm:@rsbuild/plugin-react", "npm:@types/react@18.3.23", "npm:@types/react-dom@18.3.7", "npm:typescript", "npm:antd", "npm:dayjs", "npm:react@18.3.1", "npm:react-dom@18.3.1", "@midscene/recorder"]}, {"file": "apps/recorder-form/rsbuild.config.ts", "hash": "10983430323612364645"}, {"file": "apps/recorder-form/src/App.css", "hash": "14302800428617545415"}, {"file": "apps/recorder-form/src/App.tsx", "hash": "12142214596269621853"}, {"file": "apps/recorder-form/src/components/CanvasSelector.tsx", "hash": "6191809504900799395"}, {"file": "apps/recorder-form/src/env.d.ts", "hash": "17902477445153278673"}, {"file": "apps/recorder-form/src/index.tsx", "hash": "17444832320215641181"}, {"file": "apps/recorder-form/tsconfig.json", "hash": "3606420555592674856"}], "@midscene/visualizer": [{"file": "packages/visualizer/.gitignore", "hash": "17104823628668233301"}, {"file": "packages/visualizer/.npmignore", "hash": "473519305693261888"}, {"file": "packages/visualizer/README.md", "hash": "7680732398219284019"}, {"file": "packages/visualizer/modern.config.ts", "hash": "2731505965224860254"}, {"file": "packages/visualizer/package.json", "hash": "7553385689376355277", "deps": ["npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:@modern-js/module-tools", "npm:@modern-js/plugin-module-doc", "npm:@modern-js/plugin-module-node-polyfill", "npm:@modern-js/runtime", "npm:@pixi/unsafe-eval", "npm:@types/chrome", "npm:@types/node@18.19.62", "npm:@types/react@18.3.23", "npm:@types/react-dom@18.3.7", "npm:execa@9.3.0", "npm:http-server", "npm:npm-watch", "npm:pixi-filters", "npm:pixi.js", "npm:query-string", "npm:react-resizable-panels", "npm:rimraf@3.0.2", "npm:tsx", "npm:typescript", "npm:zu<PERSON>", "npm:buffer@6.0.3", "npm:@ant-design/icons", "@midscene/core", "@midscene/shared", "@midscene/web", "npm:antd", "npm:dayjs"]}, {"file": "packages/visualizer/src/blank_polyfill.ts", "hash": "15995089251912753983"}, {"file": "packages/visualizer/src/component/blackboard.less", "hash": "2402543024878365340"}, {"file": "packages/visualizer/src/component/blackboard.tsx", "hash": "7612374103625960802"}, {"file": "packages/visualizer/src/component/color.tsx", "hash": "12247650510956902236"}, {"file": "packages/visualizer/src/component/common.less", "hash": "17925353654452284279"}, {"file": "packages/visualizer/src/component/describer.less", "hash": "6123977779394754181"}, {"file": "packages/visualizer/src/component/describer.tsx", "hash": "5995845619367944792"}, {"file": "packages/visualizer/src/component/env-config.tsx", "hash": "14160942856387342288"}, {"file": "packages/visualizer/src/component/github-star.less", "hash": "321364965794857834"}, {"file": "packages/visualizer/src/component/github-star.tsx", "hash": "4322884280851665481"}, {"file": "packages/visualizer/src/component/logo.less", "hash": "5193577380251518436"}, {"file": "packages/visualizer/src/component/logo.tsx", "hash": "10048342599078216283"}, {"file": "packages/visualizer/src/component/misc.tsx", "hash": "1668840468004556449"}, {"file": "packages/visualizer/src/component/pixi-loader.tsx", "hash": "18123559960294253201"}, {"file": "packages/visualizer/src/component/player.less", "hash": "10809114600497508949"}, {"file": "packages/visualizer/src/component/player.tsx", "hash": "7931200064125517162"}, {"file": "packages/visualizer/src/component/playground-demo-ui-context.json", "hash": "8524696382372455459"}, {"file": "packages/visualizer/src/component/playground/ConfigSelector.tsx", "hash": "16858047840856614495"}, {"file": "packages/visualizer/src/component/playground/ContextPreview.tsx", "hash": "6788578842737167047"}, {"file": "packages/visualizer/src/component/playground/HistorySelector.tsx", "hash": "16565714953233513818"}, {"file": "packages/visualizer/src/component/playground/PlaygroundResult.tsx", "hash": "4005576499692887267"}, {"file": "packages/visualizer/src/component/playground/PromptInput.tsx", "hash": "7439818362315953455"}, {"file": "packages/visualizer/src/component/playground/ServiceModeControl.tsx", "hash": "13834543846003833170"}, {"file": "packages/visualizer/src/component/playground/index.less", "hash": "2555477842582139284"}, {"file": "packages/visualizer/src/component/playground/playground-constants.tsx", "hash": "16908443328882542337"}, {"file": "packages/visualizer/src/component/playground/playground-types.ts", "hash": "6204600209580804999"}, {"file": "packages/visualizer/src/component/playground/playground-utils.ts", "hash": "11431389712939658051"}, {"file": "packages/visualizer/src/component/playground/useServerValid.ts", "hash": "12409231552011409828"}, {"file": "packages/visualizer/src/component/playground/useStaticPageAgent.ts", "hash": "18295132385716134327"}, {"file": "packages/visualizer/src/component/replay-scripts.tsx", "hash": "1692304815601001846"}, {"file": "packages/visualizer/src/component/shiny-text.less", "hash": "7657692470726135066"}, {"file": "packages/visualizer/src/component/shiny-text.tsx", "hash": "11135579093853869150"}, {"file": "packages/visualizer/src/component/store/history.ts", "hash": "14290025274779000328"}, {"file": "packages/visualizer/src/component/store/store.tsx", "hash": "6725071169892794933"}, {"file": "packages/visualizer/src/extension/jimp.d.ts", "hash": "15958916673157298193"}, {"file": "packages/visualizer/src/extension/utils.ts", "hash": "14071085746159465473"}, {"file": "packages/visualizer/src/global.d.ts", "hash": "4748762166799291180"}, {"file": "packages/visualizer/src/index.tsx", "hash": "4642939764086619411"}, {"file": "packages/visualizer/src/init.ts", "hash": "9209551330962519097"}, {"file": "packages/visualizer/src/types.d.ts", "hash": "17556842986547885281"}, {"file": "packages/visualizer/src/utils.ts", "hash": "10972151861774527773"}, {"file": "packages/visualizer/tsconfig.json", "hash": "16134518086501880060"}], "android-playground": [{"file": "apps/android-playground/README.md", "hash": "12029486699017304560"}, {"file": "apps/android-playground/package.json", "hash": "10213057748034466017", "deps": ["npm:@rsbuild/core@1.3.22", "npm:@rsbuild/plugin-less", "npm:@rsbuild/plugin-node-polyfill", "npm:@rsbuild/plugin-react", "npm:@rsbuild/plugin-svgr", "npm:@types/react@18.3.23", "npm:@types/react-dom@18.3.7", "npm:archiver@6.0.2", "npm:less@4.2.2", "npm:typescript", "npm:@ant-design/icons", "@midscene/android", "@midscene/core", "@midscene/shared", "@midscene/visualizer", "@midscene/web", "npm:@yume-chan/scrcpy", "npm:@yume-chan/scrcpy-decoder-webcodecs", "npm:antd", "npm:dayjs", "npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:socket.io-client"]}, {"file": "apps/android-playground/rsbuild.config.ts", "hash": "11612904851986564331"}, {"file": "apps/android-playground/src/App.less", "hash": "17186739894087684509"}, {"file": "apps/android-playground/src/App.tsx", "hash": "9283355305743436243"}, {"file": "apps/android-playground/src/adb-device/index.less", "hash": "99994458245587609"}, {"file": "apps/android-playground/src/adb-device/index.tsx", "hash": "6140415828230493600"}, {"file": "apps/android-playground/src/env.d.ts", "hash": "12327062444564399623"}, {"file": "apps/android-playground/src/favicon.ico", "hash": "1660729203213343947"}, {"file": "apps/android-playground/src/icons/linked.svg", "hash": "16517739271060102073"}, {"file": "apps/android-playground/src/icons/screenshot.svg", "hash": "8934742825725563320"}, {"file": "apps/android-playground/src/icons/unlink.svg", "hash": "12676688523793231503"}, {"file": "apps/android-playground/src/index.tsx", "hash": "10660923863181215402"}, {"file": "apps/android-playground/src/scrcpy-player/index.less", "hash": "13295732261744948251"}, {"file": "apps/android-playground/src/scrcpy-player/index.tsx", "hash": "8231319350308312429"}, {"file": "apps/android-playground/src/scripts/blank_polyfill.ts", "hash": "15995089251912753983"}, {"file": "apps/android-playground/tsconfig.json", "hash": "4967291434198159991"}], "@midscene/report": [{"file": "apps/report/.gitignore", "hash": "14103577076814102172"}, {"file": "apps/report/README.md", "hash": "15225412960142351561"}, {"file": "apps/report/e2e/check-html.yaml", "hash": "12751052195677017523"}, {"file": "apps/report/package.json", "hash": "13828697230105600199", "deps": ["npm:@rsbuild/plugin-node-polyfill", "npm:@rsdoctor/rspack-plugin", "npm:@types/react@18.3.23", "npm:@types/react-dom@18.3.7", "npm:less@4.2.2", "npm:typescript", "npm:@ant-design/icons", "@midscene/core", "@midscene/visualizer", "@midscene/web", "@midscene/shared", "npm:@modern-js/runtime", "npm:@rsbuild/core@1.3.22", "npm:@rsbuild/plugin-less", "npm:@rsbuild/plugin-react", "npm:@types/chrome", "npm:antd", "npm:pixi-filters", "npm:pixi.js", "npm:react@18.3.1", "npm:react-dom@18.3.1", "npm:react-resizable-panels", "npm:zu<PERSON>"]}, {"file": "apps/report/rsbuild.config.ts", "hash": "5621349409542264156"}, {"file": "apps/report/src/App.less", "hash": "3244421341483603138"}, {"file": "apps/report/src/App.tsx", "hash": "12868938511613290945"}, {"file": "apps/report/src/blank_polyfill.ts", "hash": "15995089251912753983"}, {"file": "apps/report/src/components/PlaywrightCaseSelector.tsx", "hash": "11499804763385727934"}, {"file": "apps/report/src/components/common.less", "hash": "12791313915783565982"}, {"file": "apps/report/src/components/detail-panel.less", "hash": "8378131070360428219"}, {"file": "apps/report/src/components/detail-panel.tsx", "hash": "15705390215406898038"}, {"file": "apps/report/src/components/detail-side.less", "hash": "12777268085759923140"}, {"file": "apps/report/src/components/detail-side.tsx", "hash": "14622407977647185611"}, {"file": "apps/report/src/components/global-hover-preview.less", "hash": "10498195787759294845"}, {"file": "apps/report/src/components/global-hover-preview.tsx", "hash": "8560897032346709450"}, {"file": "apps/report/src/components/open-in-playground.less", "hash": "2499843724875864881"}, {"file": "apps/report/src/components/open-in-playground.tsx", "hash": "5966917437136181418"}, {"file": "apps/report/src/components/panel-title.less", "hash": "13011864583607085159"}, {"file": "apps/report/src/components/panel-title.tsx", "hash": "7422965247302904713"}, {"file": "apps/report/src/components/pixi-loader.tsx", "hash": "18123559960294253201"}, {"file": "apps/report/src/components/playground-demo-ui-context.json", "hash": "8524696382372455459"}, {"file": "apps/report/src/components/playground.tsx", "hash": "13507000032284453785"}, {"file": "apps/report/src/components/side-item.tsx", "hash": "3244421341483603138"}, {"file": "apps/report/src/components/sidebar.less", "hash": "8695169071352019095"}, {"file": "apps/report/src/components/sidebar.tsx", "hash": "2690167264540805953"}, {"file": "apps/report/src/components/store.tsx", "hash": "15499375588077786744"}, {"file": "apps/report/src/components/timeline.less", "hash": "16054556457447607383"}, {"file": "apps/report/src/components/timeline.tsx", "hash": "8649631549018939793"}, {"file": "apps/report/src/components/yaml-player-component.tsx", "hash": "13200921878756090969"}, {"file": "apps/report/src/env.d.ts", "hash": "17902477445153278673"}, {"file": "apps/report/src/index.less", "hash": "636279783731559580"}, {"file": "apps/report/src/index.tsx", "hash": "8149022543529648887"}, {"file": "apps/report/src/types.ts", "hash": "5842276803434029314"}, {"file": "apps/report/template/index.html", "hash": "16279896606497498720"}, {"file": "apps/report/test-data/ai-shop.json", "hash": "14879571024337776109"}, {"file": "apps/report/test-data/ai-todo.json", "hash": "10990557956508271556"}, {"file": "apps/report/test-data/error.json", "hash": "7753435355493057122"}, {"file": "apps/report/test-data/online-order.json", "hash": "1009436269023732999"}, {"file": "apps/report/test-data/query-only.json", "hash": "8520649868203637065"}, {"file": "apps/report/test-data/search-headphone-on-ebay.json", "hash": "6137345200566348582"}, {"file": "apps/report/test-data/swag-lab.json", "hash": "2585943450546387919"}, {"file": "apps/report/test-data/taobao.json", "hash": "10819926587373870960"}, {"file": "apps/report/tsconfig.json", "hash": "12723751545865114471"}], "doc": [{"file": "apps/site/.gitignore", "hash": "13409952037442023248"}, {"file": "apps/site/build.sh", "hash": "882960692900944254"}, {"file": "apps/site/docs/en/API.mdx", "hash": "4502702736510837883"}, {"file": "apps/site/docs/en/automate-with-scripts-in-yaml.mdx", "hash": "5241565651628390703"}, {"file": "apps/site/docs/en/blog-introducing-instant-actions-and-deep-think.md", "hash": "6582878571378069348"}, {"file": "apps/site/docs/en/blog-programming-practice-using-structured-api.md", "hash": "6470993285723776681"}, {"file": "apps/site/docs/en/blog-support-android-automation.mdx", "hash": "1588789483284603767"}, {"file": "apps/site/docs/en/bridge-mode-by-chrome-extension.mdx", "hash": "10495556480993024331"}, {"file": "apps/site/docs/en/caching.mdx", "hash": "8623785021661991217"}, {"file": "apps/site/docs/en/changelog.mdx", "hash": "17536556656746520431"}, {"file": "apps/site/docs/en/choose-a-model.mdx", "hash": "13582363513678451695"}, {"file": "apps/site/docs/en/common/prepare-android.mdx", "hash": "1340298092202775535"}, {"file": "apps/site/docs/en/common/prepare-key-for-further-use.mdx", "hash": "5675810593628152967"}, {"file": "apps/site/docs/en/common/setup-env.mdx", "hash": "16768134527846092013"}, {"file": "apps/site/docs/en/common/start-experience.mdx", "hash": "15262806581016757281"}, {"file": "apps/site/docs/en/data-privacy.md", "hash": "17703656764638749972"}, {"file": "apps/site/docs/en/faq.md", "hash": "15401535919023321533"}, {"file": "apps/site/docs/en/index.mdx", "hash": "6824251633015142065"}, {"file": "apps/site/docs/en/integrate-with-android.mdx", "hash": "13034238064852584652"}, {"file": "apps/site/docs/en/integrate-with-playwright.mdx", "hash": "10676831263763273845"}, {"file": "apps/site/docs/en/integrate-with-puppeteer.mdx", "hash": "10048568983182905352"}, {"file": "apps/site/docs/en/llm-txt.mdx", "hash": "3179494033913075413"}, {"file": "apps/site/docs/en/mcp.mdx", "hash": "15051995383591531878"}, {"file": "apps/site/docs/en/model-provider.mdx", "hash": "2014594966488226779"}, {"file": "apps/site/docs/en/prompting-tips.md", "hash": "2919709420524396673"}, {"file": "apps/site/docs/en/quick-experience-with-android.mdx", "hash": "7564587783348228680"}, {"file": "apps/site/docs/en/quick-experience.mdx", "hash": "3289981856211350428"}, {"file": "apps/site/docs/public/android-playground.png", "hash": "6015681429626551957"}, {"file": "apps/site/docs/public/android-set-env.png", "hash": "6996678489306359498"}, {"file": "apps/site/docs/public/android-usb-debug-en.png", "hash": "9227594766727129267"}, {"file": "apps/site/docs/public/android-usb-debug.png", "hash": "13613628426285410200"}, {"file": "apps/site/docs/public/blog/0.10.0-2.png", "hash": "10529981134623018926"}, {"file": "apps/site/docs/public/blog/0.10.0.png", "hash": "16946762879085424199"}, {"file": "apps/site/docs/public/blog/0.11.0-2.png", "hash": "17051258253775926051"}, {"file": "apps/site/docs/public/blog/0.11.0.png", "hash": "16195631816835353497"}, {"file": "apps/site/docs/public/blog/0.13.0.jpeg", "hash": "14832915640121902760"}, {"file": "apps/site/docs/public/blog/0.5.0-2.png", "hash": "13334820530065356427"}, {"file": "apps/site/docs/public/blog/0.5.0.png", "hash": "7925524427428005306"}, {"file": "apps/site/docs/public/blog/0.6.0-2.png", "hash": "14904195058163243655"}, {"file": "apps/site/docs/public/blog/0.6.0-3.png", "hash": "14959938313557102003"}, {"file": "apps/site/docs/public/blog/0.6.0-4.png", "hash": "5121580099488399235"}, {"file": "apps/site/docs/public/blog/0.6.0-5.png", "hash": "1590469292171955064"}, {"file": "apps/site/docs/public/blog/0.6.0-6.png", "hash": "16410980548740272990"}, {"file": "apps/site/docs/public/blog/0.6.0.png", "hash": "4776478925022918163"}, {"file": "apps/site/docs/public/blog/0.9.0.png", "hash": "1966249871568659247"}, {"file": "apps/site/docs/public/blog/ai-ide-convert-prompt-result.png", "hash": "3414615692822620699"}, {"file": "apps/site/docs/public/blog/ai-ide-convert-prompt.png", "hash": "10642145268949231534"}, {"file": "apps/site/docs/public/blog/android-playground-lark-poster-cn.png", "hash": "11391205069080862163"}, {"file": "apps/site/docs/public/blog/android-playground-lark-poster-en.png", "hash": "13521757711246980889"}, {"file": "apps/site/docs/public/blog/coze-sidebar.png", "hash": "2671118589469655117"}, {"file": "apps/site/docs/public/blog/export-video.png", "hash": "14212361379211851351"}, {"file": "apps/site/docs/public/blog/logScreenshot-api.png", "hash": "16594682792011512995"}, {"file": "apps/site/docs/public/blog/report-coze-deep-think.png", "hash": "4538353355060643362"}, {"file": "apps/site/docs/public/blog/report-instant-action.png", "hash": "7410767423438095476"}, {"file": "apps/site/docs/public/blog/report-planning.png", "hash": "2746489029267835728"}, {"file": "apps/site/docs/public/bridge_in_extension.jpg", "hash": "4551246927883920109"}, {"file": "apps/site/docs/public/cache/no-cache-time.png", "hash": "15408884568344978393"}, {"file": "apps/site/docs/public/cache/use-cache-time.png", "hash": "17029632661078375667"}, {"file": "apps/site/docs/public/midescene-playground-entry.jpg", "hash": "5181876963346052449"}, {"file": "apps/site/docs/public/midscene-bridge-mode.jpg", "hash": "15879013891133856379"}, {"file": "apps/site/docs/public/midscene-extension.jpg", "hash": "2893441268261048870"}, {"file": "apps/site/docs/public/midscene-icon.png", "hash": "1660729203213343947"}, {"file": "apps/site/docs/public/midscene_with_text_light.png", "hash": "14286654518017544245"}, {"file": "apps/site/docs/public/playground.png", "hash": "17343510639556233721"}, {"file": "apps/site/docs/public/report.gif", "hash": "18196746886331096674"}, {"file": "apps/site/docs/zh/API.mdx", "hash": "11725304759796381447"}, {"file": "apps/site/docs/zh/automate-with-scripts-in-yaml.mdx", "hash": "3908262562325292948"}, {"file": "apps/site/docs/zh/blog-introducing-instant-actions-and-deep-think.md", "hash": "689297392581171645"}, {"file": "apps/site/docs/zh/blog-programming-practice-using-structured-api.md", "hash": "10509000100015416088"}, {"file": "apps/site/docs/zh/blog-support-android-automation.mdx", "hash": "8680595233221122001"}, {"file": "apps/site/docs/zh/bridge-mode-by-chrome-extension.mdx", "hash": "5663705028186917041"}, {"file": "apps/site/docs/zh/caching.mdx", "hash": "16934672325853431845"}, {"file": "apps/site/docs/zh/changelog.mdx", "hash": "10502636131380741671"}, {"file": "apps/site/docs/zh/choose-a-model.mdx", "hash": "14987783948195242753"}, {"file": "apps/site/docs/zh/common/prepare-android.mdx", "hash": "13292174790589008223"}, {"file": "apps/site/docs/zh/common/prepare-key-for-further-use.mdx", "hash": "13073526116690968041"}, {"file": "apps/site/docs/zh/common/setup-env.mdx", "hash": "4412835096634903195"}, {"file": "apps/site/docs/zh/common/start-experience.mdx", "hash": "7551498940801751521"}, {"file": "apps/site/docs/zh/data-privacy.md", "hash": "9893692753114299124"}, {"file": "apps/site/docs/zh/faq.md", "hash": "10475684149680126974"}, {"file": "apps/site/docs/zh/index.mdx", "hash": "16309112605833945062"}, {"file": "apps/site/docs/zh/integrate-with-android.mdx", "hash": "4510478727133138257"}, {"file": "apps/site/docs/zh/integrate-with-playwright.mdx", "hash": "16559263723362742222"}, {"file": "apps/site/docs/zh/integrate-with-puppeteer.mdx", "hash": "12869991997383892731"}, {"file": "apps/site/docs/zh/llm-txt.mdx", "hash": "8946970628461792980"}, {"file": "apps/site/docs/zh/mcp.mdx", "hash": "4143923423187882478"}, {"file": "apps/site/docs/zh/model-provider.mdx", "hash": "8419004967977756812"}, {"file": "apps/site/docs/zh/prompting-tips.md", "hash": "624362193930896808"}, {"file": "apps/site/docs/zh/quick-experience-with-android.mdx", "hash": "3506891077897707986"}, {"file": "apps/site/docs/zh/quick-experience.mdx", "hash": "2588947486063982738"}, {"file": "apps/site/i18n.json", "hash": "16413011216606950331"}, {"file": "apps/site/package.json", "hash": "5050499852221294258", "deps": ["npm:@rspress/plugin-llms", "npm:rspress-plugin-sitemap", "npm:@types/node@18.19.62", "npm:rspress", "npm:querystring", "npm:react@18.3.1", "npm:react-dom@18.3.1"]}, {"file": "apps/site/route.json", "hash": "4209469626875761088"}, {"file": "apps/site/rspress.config.ts", "hash": "14298665500368445365"}, {"file": "apps/site/styles/index.css", "hash": "10801707522020848886"}, {"file": "apps/site/tsconfig.json", "hash": "12431594634285941377"}]}}}