Write-Host "Starting Unified Agent Dashboard..." -ForegroundColor Cyan

# Set the current directory to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptPath

# Run the dashboard and keep the window open for errors
python run_unified_dashboard.py

Write-Host "Unified Agent Dashboard process exited." -ForegroundColor Yellow
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")