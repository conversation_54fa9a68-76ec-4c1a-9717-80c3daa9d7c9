name: Gemini Scheduled Issue Triage

on:
  schedule:
    - cron: '0 * * * *' # Runs every hour
  workflow_dispatch: {}

jobs:
  triage-issues:
    timeout-minutes: 10
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
      issues: write
    steps:
      - name: Generate GitHub App Token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.PRIVATE_KEY }}

      - name: Find untriaged issues
        id: find_issues
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        run: |
          echo "🔍 Finding issues without labels..."
          NO_LABEL_ISSUES=$(gh issue list --repo ${{ github.repository }} --search "is:open is:issue no:label" --json number,title,body)

          echo "🏷️ Finding issues that need triage..."
          NEED_TRIAGE_ISSUES=$(gh issue list --repo ${{ github.repository }} --search "is:open is:issue label:\"status/need-triage\"" --json number,title,body)

          echo "🔄 Merging and deduplicating issues..."
          ISSUES=$(echo "$NO_LABEL_ISSUES" "$NEED_TRIAGE_ISSUES" | jq -c -s 'add | unique_by(.number)')

          echo "📝 Setting output for GitHub Actions..."
          echo "issues_to_triage=$ISSUES" >> "$GITHUB_OUTPUT"

          echo "💾 Writing issues to temporary file for Gemini CLI..."
          echo "$ISSUES" > /tmp/issues_to_triage.json

          echo "✅ Found $(echo "$ISSUES" | jq 'length') issues to triage! 🎯"

      - name: Run Gemini Issue Triage
        if: steps.find_issues.outputs.issues_to_triage != '[]'
        uses: google-gemini/gemini-cli-action@41c0f1b3cbd1a0b284251bd1aac034edd07a3a2f
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        with:
          version: 0.1.8-rc.0
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          OTLP_GCP_WIF_PROVIDER: ${{ secrets.OTLP_GCP_WIF_PROVIDER }}
          OTLP_GCP_SERVICE_ACCOUNT: ${{ secrets.OTLP_GCP_SERVICE_ACCOUNT }}
          OTLP_GOOGLE_CLOUD_PROJECT: ${{ secrets.OTLP_GOOGLE_CLOUD_PROJECT }}
          settings_json: |
            {
              "coreTools": [
                "run_shell_command(gh label list)",
                "run_shell_command(gh issue edit)",
                "run_shell_command(gh issue list)",
                "run_shell_command(cat /tmp/issues_to_triage.json)"
              ]
            }
          prompt: |
            You are an issue triage assistant. Analyze issues and apply appropriate labels ONE AT A TIME.

            Repository: ${{ github.repository }}

            Steps:
            1. Run: `gh label list --repo ${{ github.repository }} --limit 100` to see available labels
            2. Run: `cat /tmp/issues_to_triage.json` to get the issues that need triaging
            3. Parse the JSON array from step 2 and for EACH INDIVIDUAL issue, apply appropriate labels using separate commands:
               - `gh issue edit ISSUE_NUMBER --repo ${{ github.repository }} --add-label "label1"`
               - `gh issue edit ISSUE_NUMBER --repo ${{ github.repository }} --add-label "label2"`
               - Continue for each label separately

            IMPORTANT: Label each issue individually, one command per issue, one label at a time if needed.

            Guidelines:
            - Only use existing repository labels from step 1
            - Do not add comments to issues
            - Triage each issue independently based on title and body content
            - Focus on applying: kind/* (bug/enhancement/documentation), area/* (core/cli/testing/windows), and priority/* labels
            - If an issue has insufficient information, consider applying "status/need-information"
            - After applying appropriate labels to an issue, remove the "status/need-triage" label if present: `gh issue edit ISSUE_NUMBER --repo ${{ github.repository }} --remove-label "status/need-triage"`
            - Execute one `gh issue edit` command per issue, wait for success before proceeding to the next

            Example triage logic:
            - Issues with "bug", "error", "broken" → kind/bug
            - Issues with "feature", "enhancement", "improve" → kind/enhancement
            - Issues about Windows/performance → area/windows, area/performance
            - Critical bugs → priority/p0, other bugs → priority/p1, enhancements → priority/p2

            Process each issue sequentially and confirm each labeling operation before moving to the next issue.
