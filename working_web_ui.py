#!/usr/bin/env python3
"""
Working Web UI for Browser Automation
Simple Gradio interface that actually works
"""

import gradio as gr
import requests
import subprocess
import time
import threading

class WorkingBrowserUI:
    def __init__(self):
        self.llm_url = "http://localhost:8000"
        self.chrome_debug_url = "http://localhost:9222"
        
    def test_services(self):
        """Test if services are running"""
        services = {}
        
        # Test LLM
        try:
            response = requests.get(f"{self.llm_url}/health", timeout=3)
            services['llm'] = response.status_code == 200
        except:
            services['llm'] = False
            
        # Test Chrome
        try:
            response = requests.get(f"{self.chrome_debug_url}/json", timeout=3)
            services['chrome'] = response.status_code == 200
            services['tabs'] = len(response.json()) if services['chrome'] else 0
        except:
            services['chrome'] = False
            services['tabs'] = 0
            
        return services
        
    def chat_response(self, message, history):
        """Handle chat messages"""
        if not message.strip():
            return history, ""
            
        services = self.test_services()
        
        # Create response based on message
        if "status" in message.lower():
            response = f"""**System Status:**
🤖 LLM Server: {'✅ Online' if services['llm'] else '❌ Offline'}
🌐 Chrome: {'✅ Online' if services['chrome'] else '❌ Offline'} ({services['tabs']} tabs)
🔧 Web UI: ✅ Running on port 7788

**Available Commands:**
- "open google" - Opens Google
- "open youtube" - Opens YouTube  
- "status" - Shows this status
- "start chrome" - Starts Chrome with debugging
"""
        
        elif "open google" in message.lower():
            try:
                subprocess.Popen([
                    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                    "https://google.com"
                ])
                response = "🌐 **Opened Google in Chrome**"
            except Exception as e:
                response = f"❌ **Error opening Chrome:** {str(e)}"
                
        elif "open youtube" in message.lower():
            try:
                subprocess.Popen([
                    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", 
                    "https://youtube.com"
                ])
                response = "🌐 **Opened YouTube in Chrome**"
            except Exception as e:
                response = f"❌ **Error opening Chrome:** {str(e)}"
                
        elif "start chrome" in message.lower():
            try:
                subprocess.Popen([
                    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                    "--remote-debugging-port=9222",
                    "--user-data-dir=C:\\temp\\chrome_debug"
                ])
                response = "🌐 **Started Chrome with remote debugging on port 9222**"
            except Exception as e:
                response = f"❌ **Error starting Chrome:** {str(e)}"
                
        else:
            response = f"""**Browser AI Response:**

You said: "{message}"

**System Status:**
- LLM: {'✅' if services['llm'] else '❌'}
- Chrome: {'✅' if services['chrome'] else '❌'} ({services['tabs']} tabs)

**Try these commands:**
- "open google"
- "open youtube" 
- "start chrome"
- "status"

*This is a working browser automation interface!*
"""
        
        history.append([message, response])
        return history, ""

def create_interface():
    """Create the Gradio interface"""
    ui = WorkingBrowserUI()
    
    with gr.Blocks(title="Browser Automation", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🤖 Browser Automation Chat")
        gr.Markdown("**Status: ✅ Working!** Chat with AI to control your browser.")
        
        chatbot = gr.Chatbot(
            value=[["System", "🚀 **Browser Automation Ready!**\n\nTry saying:\n- 'open google'\n- 'status'\n- 'start chrome'"]],
            height=400
        )
        
        with gr.Row():
            msg = gr.Textbox(
                placeholder="Type your command here...",
                container=False,
                scale=7
            )
            send_btn = gr.Button("Send", variant="primary", scale=1)
            
        # Connect the chat
        send_btn.click(ui.chat_response, [msg, chatbot], [chatbot, msg])
        msg.submit(ui.chat_response, [msg, chatbot], [chatbot, msg])
        
        # Quick action buttons
        with gr.Row():
            google_btn = gr.Button("🔍 Open Google", size="sm")
            youtube_btn = gr.Button("📺 Open YouTube", size="sm") 
            status_btn = gr.Button("📊 Check Status", size="sm")
            chrome_btn = gr.Button("🌐 Start Chrome Debug", size="sm")
            
        # Quick button actions
        def quick_google():
            return ui.chat_response("open google", [])
            
        def quick_youtube():
            return ui.chat_response("open youtube", [])
            
        def quick_status():
            return ui.chat_response("status", [])
            
        def quick_chrome():
            return ui.chat_response("start chrome", [])
            
        google_btn.click(lambda: (ui.chat_response("open google", chatbot.value)[0], ""), outputs=[chatbot, msg])
        youtube_btn.click(lambda: (ui.chat_response("open youtube", chatbot.value)[0], ""), outputs=[chatbot, msg])
        status_btn.click(lambda: (ui.chat_response("status", chatbot.value)[0], ""), outputs=[chatbot, msg])
        chrome_btn.click(lambda: (ui.chat_response("start chrome", chatbot.value)[0], ""), outputs=[chatbot, msg])
        
    return demo

def main():
    print("🚀 Starting Working Browser Automation Web UI...")
    print("📍 This will run on http://localhost:7788")
    
    demo = create_interface()
    
    try:
        demo.launch(
            server_name="0.0.0.0",
            server_port=7788,
            share=False,
            inbrowser=True,
            show_error=True
        )
    except Exception as e:
        print(f"❌ Error starting web UI: {e}")
        print("🔧 Trying alternative port 7789...")
        try:
            demo.launch(
                server_name="0.0.0.0", 
                server_port=7789,
                share=False,
                inbrowser=True,
                show_error=True
            )
        except Exception as e2:
            print(f"❌ Failed on port 7789 too: {e2}")

if __name__ == "__main__":
    main()
