"""
LangChain tools integration for the AI Agent System.
"""
from typing import List, Dict, Any, Optional, Callable, Union
import os
import inspect

from langchain.tools import Tool, BaseTool
from langchain.agents import AgentType, initialize_agent, Tool
from langchain_core.language_models import BaseLanguageModel


def register_langchain_tools(
    tools_list: List[Union[BaseTool, Dict[str, Any]]],
    llm: Optional[BaseLanguageModel] = None,
    agent_type: Optional[AgentType] = None,
    verbose: bool = False
):
    """
    Register tools with <PERSON><PERSON><PERSON><PERSON> and optionally initialize an agent.

    Args:
        tools_list: List of BaseTool instances or tool definition dicts
        llm: Language model to power the agent (if creating an agent)
        agent_type: Type of agent to create
        verbose: Whether to print agent execution info

    Returns:
        If llm is provided, an agent executor; otherwise, list of tools
    """
    # Process tools if provided as dictionaries
    processed_tools = []
    for tool in tools_list:
        if isinstance(tool, BaseTool):
            processed_tools.append(tool)
        elif isinstance(tool, dict):
            # Convert dict definition to Tool instance
            if "func" not in tool or "name" not in tool:
                raise ValueError("Tool dict must contain 'func' and 'name' keys")

            processed_tools.append(Tool(
                name=tool["name"],
                description=tool.get("description", ""),
                func=tool["func"]
            ))
        else:
            raise TypeError(f"Expected BaseTool or dict, got {type(tool)}")

    # If LLM provided, create an agent
    if llm is not None:
        if agent_type is None:
            agent_type = AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION

        agent_executor = initialize_agent(
            tools=processed_tools,
            llm=llm,
            agent=agent_type,
            verbose=verbose
        )
        return agent_executor

    # Otherwise, just return the tools
    return processed_tools


def create_function_tool(
    func: Callable,
    name: Optional[str] = None,
    description: Optional[str] = None
) -> Tool:
    """
    Create a tool from a Python function.

    Args:
        func: The function to wrap as a tool
        name: Name of the tool (defaults to function name)
        description: Description of the tool (defaults to function docstring)

    Returns:
        A Tool instance wrapping the function
    """
    # Use function name if name not provided
    if name is None:
        name = func.__name__

    # Use function docstring if description not provided
    if description is None:
        description = inspect.getdoc(func) or ""

    return Tool(
        name=name,
        description=description,
        func=func
    )


class ToolRegistry:
    """Registry to manage and organize tools for agents."""

    def __init__(self):
        """Initialize an empty tool registry."""
        self.tools: Dict[str, BaseTool] = {}
        self.categories: Dict[str, List[str]] = {}

    def register_tool(self, tool: BaseTool, categories: Optional[List[str]] = None):
        """
        Register a tool in the registry.

        Args:
            tool: The tool to register
            categories: Optional list of categories to assign to the tool
        """
        self.tools[tool.name] = tool

        if categories:
            for category in categories:
                if category not in self.categories:
                    self.categories[category] = []
                self.categories[category].append(tool.name)

    def get_tools(self, category: Optional[str] = None) -> List[BaseTool]:
        """
        Get tools, optionally filtered by category.

        Args:
            category: Optional category to filter by

        Returns:
            List of tools
        """
        if category is None:
            return list(self.tools.values())

        return [self.tools[name] for name in self.categories.get(category, [])]
