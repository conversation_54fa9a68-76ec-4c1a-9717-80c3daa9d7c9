import requests

class OllamaManager:
    def __init__(self, base_url="http://localhost:11434"):
        self.base_url = base_url

    def query_model(self, model_name, prompt):
        """Query the Ollama model with a given prompt."""
        url = f"{self.base_url}/api/generate"
        payload = {
            "model": model_name,
            "prompt": prompt,
            "stream": False
        }
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            return result.get('response', result)
        except requests.exceptions.RequestException as e:
            print(f"Error querying Ollama model: {e}")
            return None

    def list_models(self):
        """List all available models in Ollama."""
        url = f"{self.base_url}/api/tags"
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error listing Ollama models: {e}")
            return None
