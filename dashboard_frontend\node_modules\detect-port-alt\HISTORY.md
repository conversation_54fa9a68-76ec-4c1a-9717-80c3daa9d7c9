
1.1.2 / 2017-05-11
==================

  * fix: should double check 0.0.0.0 and localhost (#20)
  * docs: ignore type of port when checking if it's occupied (#18)

# 1.1.1 / 2017-03-17

  * fix: try to use next available port (#16)

# 1.1.0 / 2016-01-17

  * Use server listen to detect port

# 1.0.7 / 2016-12-11

  * Early return for rejected promise
  * Prevent promsie swallow in callback

# 1.0.6 / 2016-11-29

  * Bump version for new Repo

# 0.1.4 / 2015-08-24

  * Support promise

# 0.1.2 / 2014-05-31

  * Fix commander

# 0.1.1 / 2014-05-30

  * Add command line support

# 0.1.0  / 2014-05-29

  * Initial release
