"""
OnDemand.io integration for the Multi-Agent AI System.
"""
import os
import json
import requests
from typing import Dict, Any, List, Optional

class OnDemandIOClient:
    """Client for interacting with OnDemand.io API."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the OnDemand.io client.

        Args:
            api_key: OnDemand.io API key. If None, will use the ONDEMAND_API_KEY environment variable.
        """
        # Use provided API key or fall back to environment variable
        self.api_key = api_key or os.environ.get("ONDEMAND_API_KEY")
        if not self.api_key:
            raise ValueError("OnDemand.io API key not provided and not found in environment variables")

        # API base URL
        self.base_url = "https://api.ondemand.io/v1"

        # Set up headers for API requests
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def list_applications(self) -> List[Dict[str, Any]]:
        """
        List all applications on OnDemand.io.

        Returns:
            List of application data
        """
        url = f"{self.base_url}/applications"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()["applications"]

    def get_application(self, app_id: str) -> Dict[str, Any]:
        """
        Get details of a specific application.

        Args:
            app_id: ID of the application

        Returns:
            Application data
        """
        url = f"{self.base_url}/applications/{app_id}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def get_application_logs(self, app_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get logs for a specific application.

        Args:
            app_id: ID of the application
            limit: Maximum number of log entries to retrieve

        Returns:
            List of log entries
        """
        url = f"{self.base_url}/applications/{app_id}/logs"
        params = {"limit": limit}
        response = requests.get(url, headers=self.headers, params=params)
        response.raise_for_status()
        return response.json()["logs"]


class DeploymentManager:
    """Manager for deploying applications to OnDemand.io."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the DeploymentManager.

        Args:
            api_key: OnDemand.io API key. If None, will use the ONDEMAND_API_KEY environment variable.
        """
        self.client = OnDemandIOClient(api_key)

    def deploy_from_git(
        self,
        app_name: str,
        repo_url: str,
        branch: str = "main",
        start_command: str = "python app.py",
        environment_variables: Optional[Dict[str, str]] = None,
        port: int = 8000
    ) -> Dict[str, Any]:
        """
        Deploy an application from a Git repository.

        Args:
            app_name: Name of the application
            repo_url: URL of the Git repository
            branch: Branch to deploy
            start_command: Command to start the application
            environment_variables: Environment variables to set
            port: Port the application listens on

        Returns:
            Deployment data
        """
        url = f"{self.client.base_url}/deployments"

        payload = {
            "name": app_name,
            "source": {
                "type": "git",
                "url": repo_url,
                "branch": branch
            },
            "configuration": {
                "start_command": start_command,
                "port": port
            }
        }

        # Add environment variables if provided
        if environment_variables:
            payload["configuration"]["environment"] = environment_variables

        response = requests.post(url, headers=self.client.headers, json=payload)
        response.raise_for_status()
        return response.json()

    def create_rest_api_agent(
        self,
        name: str,
        endpoint_url: str,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a REST API agent on OnDemand.io.

        Args:
            name: Name of the agent
            endpoint_url: URL of the REST API endpoint
            description: Description of the agent

        Returns:
            Agent data
        """
        url = f"{self.client.base_url}/agents"

        payload = {
            "name": name,
            "type": "rest_api",
            "endpoint_url": endpoint_url
        }

        if description:
            payload["description"] = description

        response = requests.post(url, headers=self.client.headers, json=payload)
        response.raise_for_status()
        return response.json()
