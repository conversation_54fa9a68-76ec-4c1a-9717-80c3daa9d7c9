{"name": "@rushstack/eslint-patch", "entries": [{"version": "1.11.0", "tag": "@rushstack/eslint-patch_v1.11.0", "date": "<PERSON><PERSON>, 11 Mar 2025 02:12:33 GMT", "comments": {"minor": [{"comment": "Bump the `@typescript-eslint/*` packages to add support for TypeScript 5.8."}]}}, {"version": "1.10.5", "tag": "@rushstack/eslint-patch_v1.10.5", "date": "<PERSON><PERSON>, 07 Jan 2025 16:11:06 GMT", "comments": {"patch": [{"comment": "Fix a performance issue when locating \".eslint-bulk-suppressions.json\"."}]}}, {"version": "1.10.4", "tag": "@rushstack/eslint-patch_v1.10.4", "date": "Sat, 27 Jul 2024 00:10:27 GMT", "comments": {"patch": [{"comment": "Include CHANGELOG.md in published releases again"}]}}, {"version": "1.10.3", "tag": "@rushstack/eslint-patch_v1.10.3", "date": "Fri, 17 May 2024 00:10:40 GMT", "comments": {"patch": [{"comment": "[eslint-patch] Allow use of ESLint v9"}]}}, {"version": "1.10.2", "tag": "@rushstack/eslint-patch_v1.10.2", "date": "Wed, 10 Apr 2024 21:59:39 GMT", "comments": {"patch": [{"comment": "Bump maximum supported ESLint version for the bulk-suppressions tool to `8.57.0`."}]}}, {"version": "1.10.1", "tag": "@rushstack/eslint-patch_v1.10.1", "date": "Fri, 29 Mar 2024 05:46:41 GMT", "comments": {"patch": [{"comment": "Fix an issue where the `eslint-bulk prune` command would crash if a bulk suppressions file exists that speicifies no suppressions."}, {"comment": "Exit with success under normal conditions."}]}}, {"version": "1.10.0", "tag": "@rushstack/eslint-patch_v1.10.0", "date": "Thu, 28 Mar 2024 18:11:12 GMT", "comments": {"patch": [{"comment": "Fix an issue with running `eslint-bulk prune` in a project with suppressions that refer to deleted files."}], "minor": [{"comment": "Delete the `.eslint-bulk-suppressions.json` file during pruning if all suppressions have been eliminated."}]}}, {"version": "1.9.0", "tag": "@rushstack/eslint-patch_v1.9.0", "date": "Wed, 27 Mar 2024 19:47:21 GMT", "comments": {"minor": [{"comment": "Fix an issue where `eslint-bulk prune` does not work if there are no files to lint in the project root."}]}}, {"version": "1.8.0", "tag": "@rushstack/eslint-patch_v1.8.0", "date": "Wed, 20 Mar 2024 02:09:14 GMT", "comments": {"minor": [{"comment": "Refactor the bulk-suppressions feature to fix some performance issues."}], "patch": [{"comment": "Fix an issue where linting issues that were already suppressed via suppression comments were recorded in the bulk suppressions list."}]}}, {"version": "1.7.2", "tag": "@rushstack/eslint-patch_v1.7.2", "date": "Thu, 25 Jan 2024 23:03:57 GMT", "comments": {"patch": [{"comment": "Some minor documentation updates"}]}}, {"version": "1.7.1", "tag": "@rushstack/eslint-patch_v1.7.1", "date": "Wed, 24 Jan 2024 07:38:34 GMT", "comments": {"patch": [{"comment": "Update documentation"}]}}, {"version": "1.7.0", "tag": "@rushstack/eslint-patch_v1.7.0", "date": "<PERSON><PERSON>, 16 Jan 2024 18:30:10 GMT", "comments": {"minor": [{"comment": "Add support for TypeScript 5.3 with @typescript-eslint 6.19.x"}]}}, {"version": "1.6.1", "tag": "@rushstack/eslint-patch_v1.6.1", "date": "Fri, 15 Dec 2023 01:10:06 GMT", "comments": {"patch": [{"comment": "Fix bulk suppression patch's eslintrc detection in polyrepos"}]}}, {"version": "1.6.0", "tag": "@rushstack/eslint-patch_v1.6.0", "date": "Wed, 22 Nov 2023 01:45:18 GMT", "comments": {"minor": [{"comment": "Add an experimental new feature for ESLint bulk suppressions; for details see GitHub #4303"}]}}, {"version": "1.5.1", "tag": "@rushstack/eslint-patch_v1.5.1", "date": "Sun, 01 Oct 2023 02:56:29 GMT", "comments": {"patch": [{"comment": "Fix patch compatibility with ESLint 7 for versions matching <7.12.0"}]}}, {"version": "1.5.0", "tag": "@rushstack/eslint-patch_v1.5.0", "date": "<PERSON><PERSON>, 26 Sep 2023 09:30:33 GMT", "comments": {"minor": [{"comment": "Add an optional patch which can be used to allow ESLint to extend configurations from packages that do not have the \"eslint-config-\" prefix"}]}}, {"version": "1.4.0", "tag": "@rushstack/eslint-patch_v1.4.0", "date": "Fri, 15 Sep 2023 00:36:58 GMT", "comments": {"minor": [{"comment": "Update @types/node from 14 to 18"}]}}, {"version": "1.3.3", "tag": "@rushstack/eslint-patch_v1.3.3", "date": "Tue, 08 Aug 2023 07:10:39 GMT", "comments": {"patch": [{"comment": "Fix patching for running eslint via eslint/use-at-your-own-risk, which VS Code's eslint extension does when enabling flat config support"}]}}, {"version": "1.3.2", "tag": "@rushstack/eslint-patch_v1.3.2", "date": "Thu, 15 Jun 2023 00:21:01 GMT", "comments": {"patch": [{"comment": "[eslint-patch] add invalid importer path test to ESLint 7.x || 8.x block"}]}}, {"version": "1.3.1", "tag": "@rushstack/eslint-patch_v1.3.1", "date": "Wed, 07 Jun 2023 22:45:16 GMT", "comments": {"patch": [{"comment": "Add test for invalid importer path to fallback to relative path when loading eslint 6 plugins"}]}}, {"version": "1.3.0", "tag": "@rushstack/eslint-patch_v1.3.0", "date": "Mon, 22 May 2023 06:34:32 GMT", "comments": {"minor": [{"comment": "Upgrade the @typescript-eslint/* dependencies to ~5.59.2"}]}}, {"version": "1.2.0", "tag": "@rushstack/eslint-patch_v1.2.0", "date": "Thu, 15 Sep 2022 00:18:51 GMT", "comments": {"minor": [{"comment": "Use original resolver if patched resolver fails."}]}}, {"version": "1.1.4", "tag": "@rushstack/eslint-patch_v1.1.4", "date": "Tu<PERSON>, 28 Jun 2022 00:23:32 GMT", "comments": {"patch": [{"comment": "Update the README to mention support for ESLint 8."}]}}, {"version": "1.1.3", "tag": "@rushstack/eslint-patch_v1.1.3", "date": "Fri, 15 Apr 2022 00:12:36 GMT", "comments": {"patch": [{"comment": "Fix an issue where tools could not determine the module type as CommonJS"}]}}, {"version": "1.1.2", "tag": "@rushstack/eslint-patch_v1.1.2", "date": "Sat, 09 Apr 2022 02:24:26 GMT", "comments": {"patch": [{"comment": "Rename the \"master\" branch to \"main\"."}]}}, {"version": "1.1.1", "tag": "@rushstack/eslint-patch_v1.1.1", "date": "<PERSON><PERSON>, 15 Mar 2022 19:15:53 GMT", "comments": {"patch": [{"comment": "Fix the path in the package.json \"directory\" field."}]}}, {"version": "1.1.0", "tag": "@rushstack/eslint-patch_v1.1.0", "date": "Fri, 05 Nov 2021 15:09:18 GMT", "comments": {"minor": [{"comment": "feat(eslint-patch): Find patch targets independently of disk layout"}]}}, {"version": "1.0.9", "tag": "@rushstack/eslint-patch_v1.0.9", "date": "Wed, 27 Oct 2021 00:08:15 GMT", "comments": {"patch": [{"comment": "Update the package.json repository field to include the directory property."}]}}, {"version": "1.0.8", "tag": "@rushstack/eslint-patch_v1.0.8", "date": "Wed, 13 Oct 2021 15:09:54 GMT", "comments": {"patch": [{"comment": "Add support for ESLint 8.0.0"}]}}, {"version": "1.0.7", "tag": "@rushstack/eslint-patch_v1.0.7", "date": "Thu, 23 Sep 2021 00:10:40 GMT", "comments": {"patch": [{"comment": "Upgrade the `@types/node` dependency to version to version 12."}]}}, {"version": "1.0.6", "tag": "@rushstack/eslint-patch_v1.0.6", "date": "Fri, 30 Oct 2020 00:10:14 GMT", "comments": {"patch": [{"comment": "Update the \"modern-module-resolution\" patch to support ESLint 7.8.0 and newer"}]}}, {"version": "1.0.5", "tag": "@rushstack/eslint-patch_v1.0.5", "date": "Wed, 30 Sep 2020 18:39:17 GMT", "comments": {"patch": [{"comment": "Update to build with @rushstack/heft-node-rig"}]}}, {"version": "1.0.4", "tag": "@rushstack/eslint-patch_v1.0.4", "date": "Wed, 30 Sep 2020 06:53:53 GMT", "comments": {"patch": [{"comment": "Update README.md"}]}}, {"version": "1.0.3", "tag": "@rushstack/eslint-patch_v1.0.3", "date": "Wed, 12 Aug 2020 00:10:05 GMT", "comments": {"patch": [{"comment": "Updated project to build with Heft"}]}}, {"version": "1.0.2", "tag": "@rushstack/eslint-patch_v1.0.2", "date": "Wed, 24 Jun 2020 09:50:48 GMT", "comments": {"patch": [{"comment": "Fix an issue with the published file set"}]}}, {"version": "1.0.1", "tag": "@rushstack/eslint-patch_v1.0.1", "date": "Wed, 24 Jun 2020 09:04:28 GMT", "comments": {"patch": [{"comment": "Initial release"}]}}]}