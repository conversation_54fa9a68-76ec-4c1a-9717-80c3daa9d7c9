"""
Abacus DeepAgent client implementation.
"""
import os
import json
import logging
import aiohttp
import asyncio
from typing import Dict, List, Optional, Any, Union

logger = logging.getLogger(__name__)

class AbacusDeepAgentClient:
    """
    Client for interacting with the Abacus DeepAgent platform.

    Abacus DeepAgent is an advanced platform for building and deploying intelligent
    AI agents that can perform complex reasoning and problem-solving.
    """

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        Initialize the Abacus DeepAgent client.

        Args:
            api_key: API key for authentication. If not provided, it will be read from
                     the ABACUS_API_KEY environment variable.
            base_url: Base URL for the Abacus DeepAgent API.
        """
        self.api_key = api_key or os.getenv("ABACUS_API_KEY")
        self.base_url = base_url or os.getenv("ABACUS_BASE_URL", "https://api.abacus.ai/v1")

        if not self.api_key:
            logger.warning("Abacus DeepAgent API key not provided. Some functionality may be limited.")

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                          params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """
        Make an async request to the Abacus DeepAgent API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request data
            params: Query parameters
            headers: Custom headers

        Returns:
            Response data as dictionary
        """
        url = f"{self.base_url}/{endpoint}"

        # Set up headers
        _headers = {
            "Content-Type": "application/json"
        }

        if self.api_key:
            _headers["Authorization"] = f"Bearer {self.api_key}"

        if headers:
            _headers.update(headers)

        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method,
                    url=url,
                    headers=_headers,
                    params=params,
                    json=data
                ) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        raise Exception(f"API error {response.status}: {error_text}")

                    return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"Error making request to Abacus DeepAgent API: {e}")
            raise

    async def create_agent(self, name: str, description: str, model_type: str,
                         capabilities: List[str], parameters: Optional[Dict] = None) -> Dict:
        """
        Create a new DeepAgent on the Abacus platform.

        Args:
            name: Name of the agent
            description: Description of the agent
            model_type: Type of model to use (e.g., 'gpt-4', 'claude-3', etc.)
            capabilities: List of agent capabilities
            parameters: Additional parameters for the agent

        Returns:
            Agent information
        """
        data = {
            "name": name,
            "description": description,
            "model_type": model_type,
            "capabilities": capabilities
        }

        if parameters:
            data["parameters"] = parameters

        return await self._make_request("POST", "agents", data=data)

    async def get_agent(self, agent_id: str) -> Dict:
        """
        Get information about an agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent information
        """
        return await self._make_request("GET", f"agents/{agent_id}")

    async def list_agents(self) -> Dict:
        """
        List all agents.

        Returns:
            List of agents
        """
        return await self._make_request("GET", "agents")

    async def delete_agent(self, agent_id: str) -> Dict:
        """
        Delete an agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Result of the operation
        """
        return await self._make_request("DELETE", f"agents/{agent_id}")

    async def run_agent(self, agent_id: str, input_data: Dict, context: Optional[Dict] = None,
                      tools: Optional[List[Dict]] = None, stream: bool = False) -> Union[Dict, Any]:
        """
        Run an agent with given input.

        Args:
            agent_id: ID of the agent
            input_data: Input data for the agent
            context: Context information for the agent
            tools: Tools available to the agent
            stream: Whether to stream the response

        Returns:
            Agent execution result or stream
        """
        data = {
            "input": input_data
        }

        if context:
            data["context"] = context

        if tools:
            data["tools"] = tools

        if stream:
            headers = {"Accept": "text/event-stream"}
            return await self._make_request("POST", f"agents/{agent_id}/run",
                                         data=data, headers=headers)
        else:
            return await self._make_request("POST", f"agents/{agent_id}/run", data=data)

    async def add_knowledge(self, agent_id: str, knowledge_data: Dict) -> Dict:
        """
        Add knowledge to an agent.

        Args:
            agent_id: ID of the agent
            knowledge_data: Knowledge data

        Returns:
            Result of the operation
        """
        return await self._make_request("POST", f"agents/{agent_id}/knowledge", data=knowledge_data)

    async def add_tool(self, agent_id: str, tool_data: Dict) -> Dict:
        """
        Add a tool to an agent.

        Args:
            agent_id: ID of the agent
            tool_data: Tool data

        Returns:
            Result of the operation
        """
        return await self._make_request("POST", f"agents/{agent_id}/tools", data=tool_data)

    async def train_agent(self, agent_id: str, training_data: List[Dict]) -> Dict:
        """
        Train an agent with examples.

        Args:
            agent_id: ID of the agent
            training_data: Training data

        Returns:
            Result of the operation
        """
        return await self._make_request("POST", f"agents/{agent_id}/train",
                                     data={"examples": training_data})

    async def get_agent_logs(self, agent_id: str, limit: int = 100) -> Dict:
        """
        Get logs for an agent.

        Args:
            agent_id: ID of the agent
            limit: Maximum number of logs to return

        Returns:
            Agent logs
        """
        params = {"limit": limit}
        return await self._make_request("GET", f"agents/{agent_id}/logs", params=params)

    async def get_agent_metrics(self, agent_id: str) -> Dict:
        """
        Get metrics for an agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent metrics
        """
        return await self._make_request("GET", f"agents/{agent_id}/metrics")
