"""
Chain implementations using <PERSON><PERSON><PERSON><PERSON> for the AI Agent System.
"""
import os
from typing import Dict, List, Any, Optional, Union

from langchain.chains import <PERSON>vers<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Se<PERSON><PERSON>hai<PERSON>, SimpleSequential<PERSON>hain
from langchain.chains.router import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain.memory import Conversation<PERSON>uffer<PERSON>emory
from langchain.prompts import PromptTemplate
from langchain_core.language_models import LLM, BaseChatModel
from langchain_core.prompts import ChatPromptTemplate

from .memory import create_conversation_memory


def create_reasoning_chain(
    llm: Union[LLM, BaseChatModel],
    prompt_template: str = None,
    memory: Optional[ConversationBufferMemory] = None,
    verbose: bool = False
) -> ConversationChain:
    """
    Create a conversation chain for reasoning tasks.

    Args:
        llm: Language model to use
        prompt_template: Custom prompt template (optional)
        memory: Memory instance (optional)
        verbose: Whether to print chain execution info

    Returns:
        ConversationChain for reasoning tasks
    """
    if memory is None:
        memory = create_conversation_memory()

    # Default reasoning prompt if none provided
    if prompt_template is None:
        prompt_template = """
        You are an AI assistant tasked with solving complex problems through step-by-step reasoning.

        Current conversation:
        {history}

        Human: {input}
        AI:
        """

    prompt = PromptTemplate(
        input_variables=["history", "input"],
        template=prompt_template
    )

    return ConversationChain(
        llm=llm,
        prompt=prompt,
        memory=memory,
        verbose=verbose
    )


def create_sequential_chain(
    chains: List[LLMChain],
    input_variables: List[str],
    output_variables: List[str],
    verbose: bool = False
) -> Union[SimpleSequentialChain, SequentialChain]:
    """
    Create a sequential chain from multiple LLM chains.

    Args:
        chains: List of LLMChain objects
        input_variables: List of input variable names
        output_variables: List of output variable names
        verbose: Whether to print chain execution info

    Returns:
        A sequential chain combining the provided chains
    """
    if len(chains) == 0:
        raise ValueError("At least one chain must be provided")

    # If only simple input/output is needed
    if len(chains) == 1 or (len(input_variables) == 1 and len(output_variables) == 1):
        return SimpleSequentialChain(chains=chains, verbose=verbose)

    return SequentialChain(
        chains=chains,
        input_variables=input_variables,
        output_variables=output_variables,
        verbose=verbose
    )


def create_router_chain(
    llm: Union[LLM, BaseChatModel],
    destination_chains: Dict[str, LLMChain],
    default_chain: LLMChain,
    verbose: bool = False
) -> RouterChain:
    """
    Create a router chain that directs inputs to specialized chains.

    Args:
        llm: Language model to use for routing decisions
        destination_chains: Dictionary mapping descriptors to chains
        default_chain: Chain to use when no specialized chain matches
        verbose: Whether to print chain execution info

    Returns:
        A routing chain system
    """
    return MultiPromptChain(
        router_chain=llm,
        destination_chains=destination_chains,
        default_chain=default_chain,
        verbose=verbose
    )
