
/* Enhanced Modern AI Control Hub */
:root {
    --primary-color: #00ff41;
    --secondary-color: #0078d4;
    --accent-color: #ff6b35;
    --background: #0a0a0a;
    --surface: #1a1a1a;
    --surface-light: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --success: #00ff88;
    --warning: #ffaa00;
    --danger: #ff4444;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', system-ui, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    overflow-x: hidden;
}

.enhanced-header {
    background: linear-gradient(135deg, var(--surface) 0%, #001122 100%);
    padding: 1rem 2rem;
    border-bottom: 2px solid var(--primary-color);
    box-shadow: 0 4px 20px rgba(0, 255, 65, 0.3);
}

.enhanced-header h1 {
    color: var(--primary-color);
    font-size: 2rem;
    text-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
    margin-bottom: 1rem;
}

.services-status {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.service-status {
    padding: 0.5rem 1rem;
    background: var(--surface-light);
    border-radius: 20px;
    border: 1px solid var(--primary-color);
    font-weight: 500;
    animation: pulse 2s infinite;
}

.main-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.5rem;
    padding: 2rem;
    min-height: calc(100vh - 120px);
}

.panel {
    background: linear-gradient(135deg, var(--surface) 0%, #1a1a2e 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid var(--primary-color);
    box-shadow: 0 8px 32px rgba(0, 255, 65, 0.1);
    backdrop-filter: blur(10px);
}

.panel h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    text-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

/* Natural Language Panel */
.nl-panel {
    grid-column: span 2;
    border-color: var(--accent-color);
}

.nl-input-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

#nl-input {
    flex: 1;
    background: var(--surface-light);
    border: 1px solid var(--accent-color);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 10px;
    font-size: 1rem;
    min-height: 120px;
    resize: vertical;
    font-family: inherit;
}

#nl-execute {
    background: linear-gradient(135deg, var(--accent-color), #ff8c42);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

#nl-execute:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4);
}

.nl-response {
    background: var(--surface);
    border-radius: 10px;
    padding: 1rem;
    min-height: 100px;
    border: 1px solid var(--surface-light);
    font-family: 'Consolas', monospace;
    font-size: 0.9rem;
}

.response-placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

/* Browser Panel */
.browser-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.browser-controls input {
    flex: 1;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: 8px;
    min-width: 200px;
}

.browser-controls button {
    background: var(--secondary-color);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.browser-controls button:hover {
    background: #106ebe;
    transform: translateY(-1px);
}

.browser-live-view {
    background: var(--surface);
    border: 2px dashed var(--secondary-color);
    border-radius: 10px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.live-placeholder {
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
    line-height: 1.6;
}

/* Workflow Panel */
.workflow-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tab-button {
    background: var(--surface-light);
    border: 1px solid var(--surface-light);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-button.active {
    background: var(--primary-color);
    color: var(--background);
    border-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content textarea {
    width: 100%;
    background: var(--surface-light);
    border: 1px solid var(--primary-color);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 8px;
    min-height: 200px;
    margin-bottom: 1rem;
    font-family: 'Consolas', monospace;
    font-size: 0.9rem;
    resize: vertical;
}

.tab-content button {
    background: var(--primary-color);
    border: none;
    color: var(--background);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-content button:hover {
    background: #00cc00;
    transform: translateY(-1px);
}

.existing-workflows {
    margin-bottom: 1rem;
}

.workflow-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--surface-light);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid var(--primary-color);
}

.workflow-item button {
    background: var(--secondary-color);
    border: none;
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

/* AI Panel */
.ai-chat {
    background: var(--surface);
    border: 1px solid var(--surface-light);
    border-radius: 10px;
    min-height: 200px;
    padding: 1rem;
    margin-bottom: 1rem;
    overflow-y: auto;
    max-height: 300px;
}

.ai-message {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    align-items: flex-start;
}

.ai-message.system {
    background: rgba(0, 255, 65, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.ai-message.user {
    background: rgba(0, 120, 212, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    border-left: 3px solid var(--secondary-color);
}

.message-type {
    font-size: 1.2rem;
}

.message-text {
    flex: 1;
    line-height: 1.4;
}

.ai-input-container {
    display: flex;
    gap: 1rem;
}

.ai-input-container input {
    flex: 1;
    background: var(--surface-light);
    border: 1px solid var(--primary-color);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: 8px;
}

.ai-input-container button {
    background: var(--primary-color);
    border: none;
    color: var(--background);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
}

/* Monitor Panel */
.monitor-panel {
    grid-column: span 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.service-card {
    background: var(--surface-light);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid var(--primary-color);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 255, 65, 0.2);
}

.service-card h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.service-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.service-details .status {
    font-weight: bold;
}

.service-details .port {
    color: var(--text-secondary);
}

.service-details .feature {
    color: var(--accent-color);
    font-style: italic;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        grid-template-columns: 1fr;
    }

    .nl-panel {
        grid-column: span 1;
    }

    .monitor-panel {
        grid-column: span 1;
    }

    .browser-controls {
        flex-direction: column;
    }

    .nl-input-container {
        flex-direction: column;
    }

    .services-status {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Input and button focus states */
input:focus, textarea:focus, button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--surface-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
        