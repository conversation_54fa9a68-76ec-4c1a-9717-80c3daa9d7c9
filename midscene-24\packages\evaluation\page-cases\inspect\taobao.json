{"testDataPath": "<PERSON><PERSON><PERSON>", "testCases": [{"prompt": "商品搜索框", "multi": false, "annotation_index_id": 1, "response_rect": {"left": 319, "top": 54, "width": 533, "height": 40}, "response_element": {"id": "aljah"}}, {"prompt": "搜索按钮", "multi": false, "annotation_index_id": 2, "response_rect": {"left": 792, "top": 65, "width": 38, "height": 16}, "response_element": {"id": "ondpi", "indexId": 26}}, {"prompt": "产品分类里面的：男鞋（文字）", "deepThink": true, "multi": false, "annotation_index_id": 3, "response_rect": {"left": 118, "top": 428, "width": 53, "height": 12}, "response_element": {"id": "cjfcl", "indexId": 99}}, {"prompt": "右侧“立即登录”下方的收藏夹 icon", "deepThink": true, "multi": false, "annotation_index_id": 4, "response_rect": {"left": 1065, "top": 385, "width": 21, "height": 19}, "response_element": {"id": "fkfdl", "indexId": 190}}, {"prompt": "最右侧五个悬浮按钮的第二个", "deepThink": true, "multi": false, "annotation_index_id": 5, "response_rect": {"left": 1251, "top": 352, "width": 22, "height": 22}, "response_element": {"id": "iegkg", "indexId": 212}}, {"prompt": "顶部工具栏的购物车 icon", "deepThink": true, "response_rect": {"left": 837, "top": 12, "width": 17, "height": 14}, "annotation_index_id": 6, "response_element": {"id": "aefln", "indexId": 12}}]}