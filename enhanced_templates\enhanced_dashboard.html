
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced AI Control Hub</title>
    <link rel="stylesheet" href="/static/enhanced_dashboard.css">
</head>
<body>
    <div id="app">
        <header class="enhanced-header">
            <h1>🚀 Enhanced AI Control Hub</h1>
            <div class="services-status">
                <span id="chrome-status" class="service-status">🌐 Chrome: Starting...</span>
                <span id="n8n-status" class="service-status">⚡ N8N: Starting...</span>
                <span id="mcp-status" class="service-status">🔌 MCP: Starting...</span>
                <span id="ai-status" class="service-status">🤖 AI: Starting...</span>
            </div>
        </header>

        <div class="main-container">
            <!-- Natural Language Input Panel -->
            <section class="panel nl-panel">
                <h2>💬 Natural Language Control</h2>
                <div class="nl-input-container">
                    <textarea id="nl-input" placeholder="Tell me what you want to do...

Examples:
- Browse to google.com and search for AI news
- Create a workflow that sends email notifications
- Take a screenshot of the current page
- Click on the login button
- Navigate to https://example.com"></textarea>
                    <button id="nl-execute" onclick="executeNaturalLanguage()">🚀 Execute</button>
                </div>
                <div id="nl-response" class="nl-response">
                    <div class="response-placeholder">Command results will appear here...</div>
                </div>
            </section>

            <!-- Real-time Browser Control -->
            <section class="panel browser-panel">
                <h2>🌐 Real-time Browser Control</h2>
                <div class="browser-controls">
                    <input type="url" id="browser-url" placeholder="Enter URL..." value="https://google.com">
                    <button onclick="navigateRealTime()">🌐 Navigate</button>
                    <button onclick="takeScreenshot()">📸 Screenshot</button>
                    <button onclick="startLiveView()">📹 Live View</button>
                </div>
                <div id="browser-live-view" class="browser-live-view">
                    <div class="live-placeholder">Real-time browser view will appear here
                    <br><br>
                    Try:
                    <br>• Click "Navigate" to visit a website
                    <br>• Click "Screenshot" to capture the page
                    <br>• Click "Live View" for real-time streaming
                    </div>
                </div>
            </section>

            <!-- N8N Workflow Creator -->
            <section class="panel workflow-panel">
                <h2>⚡ N8N Workflow Creator</h2>
                <div class="workflow-tabs">
                    <button class="tab-button active" onclick="switchTab('code')">📝 Code</button>
                    <button class="tab-button" onclick="switchTab('description')">💭 Description</button>
                    <button class="tab-button" onclick="switchTab('existing')">📁 Existing</button>
                </div>

                <div id="code-tab" class="tab-content active">
                    <textarea id="workflow-code" placeholder="Paste your N8N workflow JSON or JavaScript code here...

Example:
{
  &quot;name&quot;: &quot;Email Automation&quot;,
  &quot;nodes&quot;: [
    {
      &quot;name&quot;: &quot;Webhook&quot;,
      &quot;type&quot;: &quot;n8n-nodes-base.webhook&quot;
    }
  ]
}"></textarea>
                    <button onclick="createWorkflowFromCode()">🚀 Create from Code</button>
                </div>

                <div id="description-tab" class="tab-content">
                    <textarea id="workflow-description" placeholder="Describe the workflow you want to create...

Example: Create a workflow that monitors my email, extracts attachments, and saves them to Google Drive

Other examples:
- Send Slack notifications when new leads come in
- Process CSV files and update a database
- Monitor website changes and send alerts"></textarea>
                    <button onclick="createWorkflowFromDescription()">✨ Generate Workflow</button>
                </div>

                <div id="existing-tab" class="tab-content">
                    <div id="existing-workflows" class="existing-workflows">
                        <div class="workflow-item">
                            <span>📧 Email Monitor</span>
                            <button onclick="editWorkflow('email-monitor')">Edit</button>
                        </div>
                        <div class="workflow-item">
                            <span>📊 Data Processor</span>
                            <button onclick="editWorkflow('data-processor')">Edit</button>
                        </div>
                    </div>
                    <button onclick="openN8N()">🔗 Open N8N Editor</button>
                </div>
            </section>

            <!-- AI Chat & Control -->
            <section class="panel ai-panel">
                <h2>🤖 AI Assistant</h2>
                <div id="ai-chat" class="ai-chat">
                    <div class="ai-message system">
                        <span class="message-type">🤖</span>
                        <span class="message-text">Hello! I'm your AI assistant. Ask me about browser automation, workflow creation, or system status.</span>
                    </div>
                </div>
                <div class="ai-input-container">
                    <input type="text" id="ai-input" placeholder="Chat with AI assistant...">
                    <button onclick="sendAIMessage()">💬 Send</button>
                </div>
            </section>

            <!-- Live System Monitor -->
            <section class="panel monitor-panel">
                <h2>📊 Live System Monitor</h2>
                <div id="services-grid" class="services-grid">
                    <div class="service-card" id="chrome-card">
                        <h3>🌐 Chrome Browser</h3>
                        <div class="service-details">
                            <span class="status">Status: <span id="chrome-detail-status">Starting...</span></span>
                            <span class="port">Debug Port: 9222</span>
                            <span class="feature">DevTools Protocol Enabled</span>
                        </div>
                    </div>
                    <div class="service-card" id="n8n-card">
                        <h3>⚡ N8N Server</h3>
                        <div class="service-details">
                            <span class="status">Status: <span id="n8n-detail-status">Starting...</span></span>
                            <span class="port">Port: 5678</span>
                            <span class="feature">Workflow Automation</span>
                        </div>
                    </div>
                    <div class="service-card" id="mcp-card">
                        <h3>🔌 MCP Servers</h3>
                        <div class="service-details">
                            <div id="mcp-servers-list">Loading...</div>
                        </div>
                    </div>
                    <div class="service-card" id="ai-card">
                        <h3>🤖 AI Agents</h3>
                        <div class="service-details">
                            <div id="ai-agents-list">Loading...</div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script src="/static/enhanced_dashboard.js"></script>
</body>
</html>
        