#!/usr/bin/env python3
"""
Conversational Browser AI with Memory
Advanced browser automation with conversational AI that can handle complex tasks
"""

import gradio as gr
import asyncio
import json
import sys
import os
import subprocess
import requests
import time
import threading
import socket
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ConversationMemory:
    """Manages conversation memory and context"""

    def __init__(self):
        self.conversations = []
        self.task_context = {}
        self.user_preferences = {}
        self.session_start = datetime.now()

    def add_message(self, user_msg: str, ai_response: str, task_type: str = "general"):
        """Add a message to conversation memory"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            "user_message": user_msg,
            "ai_response": ai_response,
            "task_type": task_type
        }
        self.conversations.append(entry)

        # Keep only last 50 conversations to manage memory
        if len(self.conversations) > 50:
            self.conversations = self.conversations[-50:]

    def get_context(self, last_n: int = 5) -> str:
        """Get recent conversation context"""
        if not self.conversations:
            return "No previous conversation context."

        recent = self.conversations[-last_n:]
        context = "Recent conversation context:\n"
        for entry in recent:
            context += f"User: {entry['user_message']}\n"
            context += f"AI: {entry['ai_response'][:100]}...\n\n"
        return context

    def extract_task_info(self, message: str) -> Dict[str, Any]:
        """Extract task information from user message"""
        task_info = {
            "complexity": "simple",
            "requires_steps": False,
            "domain": "general"
        }

        # Detect complex tasks
        complex_indicators = ["plan", "strategy", "multiple", "steps", "process", "workflow", "automate"]
        if any(indicator in message.lower() for indicator in complex_indicators):
            task_info["complexity"] = "complex"
            task_info["requires_steps"] = True

        # Detect domain
        if any(word in message.lower() for word in ["email", "gmail", "send"]):
            task_info["domain"] = "email"
        elif any(word in message.lower() for word in ["search", "google", "find"]):
            task_info["domain"] = "search"
        elif any(word in message.lower() for word in ["social", "facebook", "twitter", "linkedin"]):
            task_info["domain"] = "social"

        return task_info

class ChromeDebugManager:
    """Manages Chrome debugging connection"""

    def __init__(self):
        self.debug_port = 9222
        self.chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        self.user_data_dir = project_root / "chrome_debug_data"
        self.chrome_process = None

    def is_chrome_debug_available(self) -> bool:
        """Check if Chrome debugging is available"""
        try:
            response = requests.get(f"http://localhost:{self.debug_port}/json", timeout=3)
            return response.status_code == 200
        except:
            return False

    def kill_existing_chrome(self):
        """Kill existing Chrome processes"""
        try:
            subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"],
                         capture_output=True, check=False)
            time.sleep(2)
        except:
            pass

    def start_chrome_debug(self) -> Dict[str, Any]:
        """Start Chrome with remote debugging"""
        try:
            # Kill existing Chrome first
            self.kill_existing_chrome()

            # Ensure user data directory exists
            self.user_data_dir.mkdir(exist_ok=True)

            # Chrome command with debugging
            cmd = [
                self.chrome_path,
                f"--remote-debugging-port={self.debug_port}",
                f"--user-data-dir={self.user_data_dir}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--start-maximized",
                "--new-window"
            ]

            print(f"Starting Chrome with command: {' '.join(cmd)}")

            # Start Chrome
            self.chrome_process = subprocess.Popen(cmd)

            # Wait for Chrome to start
            for i in range(15):  # Wait up to 15 seconds
                time.sleep(1)
                if self.is_chrome_debug_available():
                    return {
                        "success": True,
                        "message": f"✅ Chrome started with debugging on port {self.debug_port}",
                        "port": self.debug_port,
                        "process_id": self.chrome_process.pid
                    }

            return {
                "success": False,
                "message": "⚠️ Chrome started but debugging port not accessible"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"❌ Error starting Chrome: {str(e)}"
            }

    def get_chrome_tabs(self) -> List[Dict]:
        """Get list of Chrome tabs"""
        try:
            response = requests.get(f"http://localhost:{self.debug_port}/json", timeout=3)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return []

class LLMConnector:
    """Connects to local and cloud LLMs like OpenAI Operator"""

    def __init__(self):
        self.local_llm_url = "http://localhost:8000/v1"
        self.ollama_url = "http://localhost:11434"
        self.available_models = []
        self.active_model = None
        self.check_available_models()

    def check_available_models(self):
        """Check what LLM models are available"""
        models = []

        # Check local LLM server
        try:
            response = requests.get(f"{self.local_llm_url}/models", timeout=3)
            if response.status_code == 200:
                models.append({"name": "Local LLM", "url": self.local_llm_url, "type": "local"})
        except:
            pass

        # Check Ollama
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=3)
            if response.status_code == 200:
                ollama_models = response.json().get("models", [])
                for model in ollama_models:
                    models.append({
                        "name": f"Ollama: {model['name']}",
                        "url": self.ollama_url,
                        "type": "ollama",
                        "model": model['name']
                    })
        except:
            pass

        self.available_models = models
        if models:
            self.active_model = models[0]

    def chat_with_llm(self, message: str, context: str = "") -> str:
        """Chat with the active LLM"""
        if not self.active_model:
            return "❌ No LLM models available. Please start a local LLM server."

        try:
            if self.active_model["type"] == "ollama":
                return self.chat_ollama(message, context)
            else:
                return self.chat_local_llm(message, context)
        except Exception as e:
            return f"❌ LLM Error: {str(e)}"

    def chat_ollama(self, message: str, context: str = "") -> str:
        """Chat with Ollama model"""
        payload = {
            "model": self.active_model["model"],
            "prompt": f"Context: {context}\n\nUser: {message}\n\nAssistant:",
            "stream": False
        }

        response = requests.post(f"{self.ollama_url}/api/generate",
                               json=payload, timeout=30)
        if response.status_code == 200:
            return response.json().get("response", "No response from LLM")
        else:
            return f"❌ Ollama Error: {response.status_code}"

    def chat_local_llm(self, message: str, context: str = "") -> str:
        """Chat with local LLM server"""
        payload = {
            "model": "local-model",
            "messages": [
                {"role": "system", "content": f"You are an AI browser automation assistant. Context: {context}"},
                {"role": "user", "content": message}
            ],
            "stream": False
        }

        response = requests.post(f"{self.local_llm_url}/chat/completions",
                               json=payload, timeout=30)
        if response.status_code == 200:
            return response.json()["choices"][0]["message"]["content"]
        else:
            return f"❌ Local LLM Error: {response.status_code}"

class OperatorBrowserAgent:
    """OpenAI Operator-like browser agent with takeover capabilities"""

    def __init__(self, chrome_manager, llm_connector):
        self.chrome_manager = chrome_manager
        self.llm_connector = llm_connector
        self.current_task = None
        self.task_steps = []
        self.step_index = 0
        self.takeover_mode = False

    def start_task(self, task_description: str) -> Dict[str, Any]:
        """Start a new task like OpenAI Operator"""
        self.current_task = task_description
        self.step_index = 0
        self.takeover_mode = False

        # Use LLM to break down the task
        context = "You are a browser automation agent. Break down this task into specific steps."
        llm_response = self.llm_connector.chat_with_llm(
            f"Break down this browser automation task into specific steps: {task_description}",
            context
        )

        # Parse steps (simplified - in real implementation would be more sophisticated)
        self.task_steps = self.parse_task_steps(llm_response)

        return {
            "task": task_description,
            "steps": self.task_steps,
            "status": "ready",
            "can_takeover": True
        }

    def parse_task_steps(self, llm_response: str) -> List[str]:
        """Parse LLM response into actionable steps"""
        # Simple parsing - could be enhanced with better NLP
        lines = llm_response.split('\n')
        steps = []
        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('1.') or line.startswith('2.')):
                steps.append(line.lstrip('- 123456789.').strip())

        if not steps:
            steps = [llm_response]  # Fallback to full response

        return steps

    def execute_next_step(self) -> Dict[str, Any]:
        """Execute the next step in the task"""
        if self.step_index >= len(self.task_steps):
            return {"status": "completed", "message": "✅ Task completed!"}

        current_step = self.task_steps[self.step_index]

        # Execute the step
        result = self.execute_browser_step(current_step)

        self.step_index += 1

        return {
            "step": current_step,
            "step_number": self.step_index,
            "total_steps": len(self.task_steps),
            "result": result,
            "status": "in_progress" if self.step_index < len(self.task_steps) else "completed",
            "can_takeover": True
        }

    def execute_browser_step(self, step: str) -> str:
        """Execute a specific browser step"""
        step_lower = step.lower()

        if "open" in step_lower and ("google" in step_lower or "search" in step_lower):
            return self.open_website("https://google.com")
        elif "open" in step_lower and "youtube" in step_lower:
            return self.open_website("https://youtube.com")
        elif "open" in step_lower and "gmail" in step_lower:
            return self.open_website("https://gmail.com")
        elif "search" in step_lower:
            return f"🔍 Searching for: {step}"
        elif "click" in step_lower:
            return f"🖱️ Clicking: {step}"
        elif "type" in step_lower or "enter" in step_lower:
            return f"⌨️ Typing: {step}"
        else:
            return f"🤖 Executing: {step}"

    def open_website(self, url: str) -> str:
        """Open website using Chrome manager"""
        try:
            subprocess.Popen([self.chrome_manager.chrome_path, url])
            return f"🌐 Opened {url}"
        except Exception as e:
            return f"❌ Error opening {url}: {str(e)}"

    def takeover_control(self) -> str:
        """Allow user to take over control"""
        self.takeover_mode = True
        return "🎮 **TAKEOVER MODE ACTIVATED**\n\nYou now have control! I'll continue assisting while you manually control the browser."

    def resume_automation(self) -> str:
        """Resume automated execution"""
        self.takeover_mode = False
        return "🤖 **AUTOMATION RESUMED**\n\nI'm back in control and will continue the task automatically."

class ConversationalBrowserAI:
    """Main conversational browser AI system like OpenAI Operator"""

    def __init__(self):
        self.memory = ConversationMemory()
        self.chrome_manager = ChromeDebugManager()
        self.llm_connector = LLMConnector()
        self.operator_agent = OperatorBrowserAgent(self.chrome_manager, self.llm_connector)
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """Load configuration"""
        config_path = project_root / "config" / "browser_automation_config.json"
        try:
            if config_path.exists():
                with open(config_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")

        return {
            "ui_tars": {
                "api_url": "http://localhost:8080/v1",
                "api_key": "hf_dummy_key"
            }
        }

    def analyze_user_intent(self, message: str) -> Dict[str, Any]:
        """Analyze user intent and plan response"""
        task_info = self.memory.extract_task_info(message)
        context = self.memory.get_context(3)

        # Determine if this is a follow-up or new task
        is_followup = any(word in message.lower() for word in ["also", "then", "next", "after", "continue"])

        return {
            "task_info": task_info,
            "is_followup": is_followup,
            "context": context,
            "requires_chrome": any(word in message.lower() for word in ["open", "browse", "navigate", "click", "search"])
        }

    def expand_complex_task(self, message: str, analysis: Dict[str, Any]) -> str:
        """Expand complex tasks into detailed steps"""
        if analysis["task_info"]["complexity"] == "simple":
            return ""

        expansion = f"""**🧠 Task Analysis & Expansion:**

**Your Request:** {message}

**I understand you want me to:**
"""

        # Generate expansion based on task type
        if "email" in analysis["task_info"]["domain"]:
            expansion += """
1. **Email Automation Task Detected**
   - Open Gmail in Chrome
   - Navigate to compose
   - Set up email content
   - Handle sending process

**Would you like me to:**
- Draft the email first for your review?
- Send it immediately?
- Set up a template for future use?
"""

        elif "search" in analysis["task_info"]["domain"]:
            expansion += """
1. **Search & Research Task Detected**
   - Open search engine
   - Execute search queries
   - Analyze results
   - Compile findings

**I can help you:**
- Find specific information
- Compare multiple sources
- Save results for later
- Create a summary report
"""

        else:
            expansion += """
1. **Complex Browser Task Detected**
   - Break down into manageable steps
   - Execute each step systematically
   - Provide progress updates
   - Handle any errors gracefully

**Let me know if you'd like me to:**
- Proceed with the full automation
- Show you the steps first
- Modify the approach
"""

        return expansion

    def execute_browser_action(self, action: str) -> str:
        """Execute browser actions"""
        try:
            # Check Chrome debugging
            if not self.chrome_manager.is_chrome_debug_available():
                result = self.chrome_manager.start_chrome_debug()
                if not result["success"]:
                    return result["message"]

            # Handle different actions
            if "open google" in action.lower():
                return self.open_website("https://google.com")
            elif "open youtube" in action.lower():
                return self.open_website("https://youtube.com")
            elif "open gmail" in action.lower():
                return self.open_website("https://gmail.com")
            elif "status" in action.lower():
                return self.get_detailed_status()
            else:
                return f"🤖 **Action Acknowledged:** {action}\n\n*Advanced browser automation for this action is being processed...*"

        except Exception as e:
            return f"❌ Error executing action: {str(e)}"

    def open_website(self, url: str) -> str:
        """Open website in Chrome"""
        try:
            subprocess.Popen([self.chrome_manager.chrome_path, url])
            return f"🌐 **Opened {url} in Chrome**"
        except Exception as e:
            return f"❌ Error opening website: {str(e)}"

    def get_detailed_status(self) -> str:
        """Get detailed system status"""
        chrome_available = self.chrome_manager.is_chrome_debug_available()
        tabs = self.chrome_manager.get_chrome_tabs()

        status = f"""**🔍 Detailed System Status**

**Chrome Debugging:**
- Status: {'✅ Available' if chrome_available else '❌ Not Available'}
- Port: {self.chrome_manager.debug_port}
- Active Tabs: {len(tabs)}

**Conversation Memory:**
- Messages in Memory: {len(self.memory.conversations)}
- Session Duration: {datetime.now() - self.memory.session_start}

**Capabilities:**
- ✅ Complex task expansion
- ✅ Conversation memory
- ✅ Multi-step automation
- ✅ Context awareness

**Ready for:** {'✅ All browser automation tasks' if chrome_available else '❌ Need to start Chrome debugging first'}
"""
        return status

    def process_message(self, message: str, history: List) -> tuple:
        """Process user message with OpenAI Operator-like capabilities"""
        if not message.strip():
            return history, ""

        # Handle special commands
        if message.lower() in ["takeover", "take control", "let me control"]:
            response = self.operator_agent.takeover_control()
            history.append([message, response])
            return history, ""

        if message.lower() in ["resume", "continue automation", "auto mode"]:
            response = self.operator_agent.resume_automation()
            history.append([message, response])
            return history, ""

        if message.lower() in ["next step", "continue", "next"]:
            if self.operator_agent.current_task:
                result = self.operator_agent.execute_next_step()
                response = self.format_step_response(result)
                history.append([message, response])
                return history, ""

        # Analyze user intent
        analysis = self.analyze_user_intent(message)

        # Check if this is a new automation task
        if self.is_automation_task(message):
            return self.handle_automation_task(message, history, analysis)

        # Handle LLM chat
        if any(word in message.lower() for word in ["chat", "ask", "tell me", "explain", "what", "how", "why"]):
            return self.handle_llm_chat(message, history, analysis)

        # Default browser automation
        return self.handle_browser_action(message, history, analysis)

    def is_automation_task(self, message: str) -> bool:
        """Check if message describes an automation task"""
        automation_keywords = [
            "automate", "task", "workflow", "process", "steps",
            "help me", "i need to", "can you", "please"
        ]
        return any(keyword in message.lower() for keyword in automation_keywords)

    def handle_automation_task(self, message: str, history: List, analysis: Dict) -> tuple:
        """Handle automation task like OpenAI Operator"""
        # Start new task with LLM planning
        task_result = self.operator_agent.start_task(message)

        response = f"""🤖 **OpenAI Operator-Style Task Started**

**Task:** {task_result['task']}

**🧠 LLM Analysis & Planning:**
{self.llm_connector.chat_with_llm(message, "Plan this browser automation task")}

**📋 Planned Steps:**
"""

        for i, step in enumerate(task_result['steps'], 1):
            response += f"{i}. {step}\n"

        response += f"""
**🎮 Control Options:**
- Say "**next step**" to execute the next step
- Say "**takeover**" to take manual control
- Say "**continue**" to run all steps automatically

**Ready to start?** Say "next step" or I can begin automatically!
"""

        history.append([message, response])
        return history, ""

    def handle_llm_chat(self, message: str, history: List, analysis: Dict) -> tuple:
        """Handle direct LLM chat"""
        context = self.memory.get_context(3)
        llm_response = self.llm_connector.chat_with_llm(message, context)

        response = f"""🧠 **LLM Response:**

{llm_response}

**💡 Available Models:**
"""
        for model in self.llm_connector.available_models:
            active = "✅" if model == self.llm_connector.active_model else "⚪"
            response += f"{active} {model['name']} ({model['type']})\n"

        if not self.llm_connector.available_models:
            response += "❌ No LLM models detected. Please start Ollama or local LLM server."

        self.memory.add_message(message, llm_response, "chat")
        history.append([message, response])
        return history, ""

    def handle_browser_action(self, message: str, history: List, analysis: Dict) -> tuple:
        """Handle browser actions"""
        # Build comprehensive response
        response_parts = []

        # Add task expansion for complex tasks
        expansion = self.expand_complex_task(message, analysis)
        if expansion:
            response_parts.append(expansion)

        # Execute browser actions if needed
        if analysis["requires_chrome"]:
            action_result = self.execute_browser_action(message)
            response_parts.append(action_result)

        # Add conversational response
        if analysis["is_followup"]:
            response_parts.append("**🔄 Continuing from our previous conversation...**")

        # Default response if no specific action
        if not response_parts:
            response_parts.append(f"""**🤖 Browser AI Response:**

I understand: "{message}"

**🚀 I can help with:**
- **Automation Tasks**: "Help me automate email workflow"
- **LLM Chat**: "Explain how browser automation works"
- **Direct Actions**: "Open Google and search for AI tools"
- **Task Control**: "Take over", "Next step", "Continue"

**What would you like to do?**
""")

        # Combine all response parts
        full_response = "\n\n".join(response_parts)

        # Add to memory
        self.memory.add_message(message, full_response, analysis["task_info"]["domain"])

        # Add to chat history
        history.append([message, full_response])
        return history, ""

    def format_step_response(self, result: Dict) -> str:
        """Format step execution response"""
        if result["status"] == "completed":
            return f"""✅ **Task Completed!**

{result['message']}

**🎉 All steps finished successfully!**
"""

        return f"""🤖 **Step {result['step_number']}/{result['total_steps']} Executed**

**Action:** {result['step']}
**Result:** {result['result']}

**Status:** {result['status'].replace('_', ' ').title()}

**🎮 What's next?**
- Say "**next step**" to continue
- Say "**takeover**" to take control
- Say "**status**" to see progress
"""

def create_conversational_interface():
    """Create the OpenAI Operator-like conversational interface"""
    ai = ConversationalBrowserAI()

    with gr.Blocks(title="OpenAI Operator-Style Browser AI", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🤖 OpenAI Operator-Style Browser AI")
        gr.Markdown("**Native chat with browser automation • Seamless takeover • Connected to local/cloud LLMs**")

        # Status and control section
        with gr.Row():
            status_btn = gr.Button("🔍 System Status", size="sm")
            chrome_btn = gr.Button("🌐 Start Chrome Debug", size="sm")
            llm_btn = gr.Button("🧠 LLM Status", size="sm")
            takeover_btn = gr.Button("🎮 Takeover Control", size="sm", variant="secondary")
            resume_btn = gr.Button("🤖 Resume Auto", size="sm", variant="primary")

        status_output = gr.Markdown()

        # Main chat interface
        chatbot = gr.Chatbot(
            value=[["System", """🚀 **OpenAI Operator-Style Browser AI Ready!**

**🎯 Like OpenAI's Operator, I can:**
- **🤖 Automate** complex browser tasks step-by-step
- **🎮 Hand over control** when you want to take over
- **🧠 Chat natively** with local/cloud LLMs
- **🔄 Continue tasks** seamlessly after takeover
- **📝 Remember** everything we discuss

**🚀 Try these Operator-style commands:**
- "Help me automate my email workflow" (I'll plan and execute)
- "Open Google and search for AI tools" (Direct automation)
- "Takeover" (You control, I assist)
- "Next step" (Continue automation)
- "Chat with LLM about browser automation" (Direct LLM chat)

**🧠 Connected LLMs:** Checking local models...
**🌐 Browser:** Ready for automation and takeover!"""]],
            height=500,
            show_label=False
        )

        with gr.Row():
            msg = gr.Textbox(
                placeholder="Describe what you want to accomplish... I'll understand and expand on it!",
                container=False,
                scale=7
            )
            send_btn = gr.Button("Send", variant="primary", scale=1)

        # Operator-style control buttons
        with gr.Row():
            next_btn = gr.Button("▶️ Next Step", size="sm", variant="primary")
            auto_btn = gr.Button("🚀 Auto Execute", size="sm")
            pause_btn = gr.Button("⏸️ Pause", size="sm")

        # Quick automation templates
        with gr.Row():
            email_btn = gr.Button("📧 Email Automation", size="sm")
            research_btn = gr.Button("🔍 Research Task", size="sm")
            social_btn = gr.Button("📱 Social Media", size="sm")
            custom_btn = gr.Button("🛠️ Custom Task", size="sm")

        # Event handlers
        def get_status():
            return ai.get_detailed_status()

        def start_chrome():
            result = ai.chrome_manager.start_chrome_debug()
            return result["message"]

        def get_llm_status():
            models = ai.llm_connector.available_models
            if not models:
                return "❌ No LLM models detected. Please start Ollama or local LLM server."

            status = "🧠 **Available LLM Models:**\n\n"
            for model in models:
                active = "✅ ACTIVE" if model == ai.llm_connector.active_model else "⚪"
                status += f"{active} **{model['name']}** ({model['type']})\n"
            return status

        def takeover_control():
            return ai.operator_agent.takeover_control()

        def resume_automation():
            return ai.operator_agent.resume_automation()

        def execute_next_step():
            if ai.operator_agent.current_task:
                result = ai.operator_agent.execute_next_step()
                return ai.format_step_response(result)
            else:
                return "❌ No active task. Start a task first by describing what you want to automate."

        # Button event handlers
        status_btn.click(get_status, outputs=status_output)
        chrome_btn.click(start_chrome, outputs=status_output)
        llm_btn.click(get_llm_status, outputs=status_output)
        takeover_btn.click(takeover_control, outputs=status_output)
        resume_btn.click(resume_automation, outputs=status_output)

        # Main chat handlers
        send_btn.click(ai.process_message, [msg, chatbot], [chatbot, msg])
        msg.submit(ai.process_message, [msg, chatbot], [chatbot, msg])

        # Operator control handlers
        next_btn.click(lambda: execute_next_step(), outputs=status_output)

        # Quick automation templates
        def start_email_automation():
            history, _ = ai.process_message("Help me automate my email workflow - check inbox, respond to important emails, and organize by priority", [])
            return history

        def start_research_task():
            history, _ = ai.process_message("I need to research competitors, gather information, and compile a comprehensive report", [])
            return history

        def start_social_automation():
            history, _ = ai.process_message("Help me automate social media posting, engagement tracking, and lead generation", [])
            return history

        def start_custom_task():
            history, _ = ai.process_message("I have a complex multi-step automation task that needs intelligent planning and execution", [])
            return history

        email_btn.click(start_email_automation, outputs=chatbot)
        research_btn.click(start_research_task, outputs=chatbot)
        social_btn.click(start_social_automation, outputs=chatbot)
        custom_btn.click(start_custom_task, outputs=chatbot)

    return demo

def main():
    """Main function"""
    print("🧠 Starting Conversational Browser AI with Memory...")
    print("📍 Will run on http://localhost:7788")

    try:
        demo = create_conversational_interface()
        print("✅ Conversational AI interface created successfully")

        demo.launch(
            server_name="0.0.0.0",
            server_port=7788,
            share=False,
            inbrowser=True,
            show_error=True,
            quiet=False
        )

    except Exception as e:
        print(f"❌ Error: {e}")
        print("🔧 Trying port 7789...")
        try:
            demo.launch(
                server_name="0.0.0.0",
                server_port=7789,
                share=False,
                inbrowser=True,
                show_error=True
            )
        except Exception as e2:
            print(f"❌ Failed: {e2}")

if __name__ == "__main__":
    main()
