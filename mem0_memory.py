
import os
from mem0.memory.main import Memory

# Set Gemini API key from environment variable
os.environ["GEMINI_API_KEY"] = os.environ.get("GEMINI_API_KEY", "")

# Initialize Mem0 client with Gemini
config = {
    "llm": {
        "provider": "gemini",
        "config": {
            "model": "gemini-1.5-flash"
        }
    },
    "embedder": {
        "provider": "gemini",
        "config": {
            "model": "text-embedding-004"
        }
    },
    "vector_store": {
        "provider": "qdrant",
        "config": {
            "embedding_model_dims": 768
        }
    }
}
mem0 = Memory.from_config(config)
print(mem0.config)

def add_memory(user_input, agent_response, user_id="user"):
    """
    Adds a memory to Mem0.
    """
    mem0.add(f"User: {user_input}\nAgent: {agent_response}", user_id=user_id)
    print("Memory added.")

def get_all_memories():
    """
    Retrieves all memories from Mem0.
    """
    return mem0.get_all()

def search_memories(query, user_id="user"):
    """
    Searches for memories in Mem0.
    """
    return mem0.search(query, user_id=user_id)

if __name__ == '__main__':
    # Example usage
    add_memory("My favorite color is blue.", "Okay, I will remember that.")
    print(search_memories("What is my favorite color?"))
