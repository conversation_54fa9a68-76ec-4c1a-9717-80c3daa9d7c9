import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useRef } from "react";

export function ResponseDisplay({
  messages,
}: {
  messages: { content: string }[];
}) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      const container =
        messagesEndRef.current.parentElement?.parentElement?.parentElement;
      container?.scrollTo({
        top: container.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages]);

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8 px-4">
      <AnimatePresence mode="popLayout">
        {messages.map((message, index) => (
          <motion.div
            key={`message-${index}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div
              className="max-w-[90%] md:max-w-[75%] break-words backdrop-blur-sm border
                        px-4 py-2 rounded-xl rounded-tl-sm"
            >
              <p>{message.content}</p>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
      <div ref={messagesEndRef} className="h-px" />
    </div>
  );
}
