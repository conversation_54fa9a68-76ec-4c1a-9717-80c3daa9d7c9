{"apps/android-playground": {"root": ["apps/android-playground/package.json", "nx/core/package-json"], "name": ["apps/android-playground/package.json", "nx/core/package-json"], "tags": ["apps/android-playground/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.js": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/android-playground/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/android-playground/package.json", "nx/core/package-json"], "targets": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.executor": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.options": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.metadata": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.options.script": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev.executor": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev.options": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev.metadata": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev.options.script": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview.executor": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview.options": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview.metadata": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview.options.script": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview.metadata.scriptContent": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.preview.metadata.runCommand": ["apps/android-playground/package.json", "nx/core/package-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/chrome-extension": {"root": ["apps/chrome-extension/package.json", "nx/core/package-json"], "name": ["apps/chrome-extension/package.json", "nx/core/package-json"], "tags": ["apps/chrome-extension/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.js": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/chrome-extension/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.executor": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.options": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.metadata": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.options.script": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev.executor": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev.options": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev.metadata": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev.options.script": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview.executor": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview.options": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview.metadata": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview.options.script": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview.metadata.scriptContent": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.preview.metadata.runCommand": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension.executor": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension.options": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension.metadata": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension.options.script": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension.metadata.scriptContent": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.pack-extension.metadata.runCommand": ["apps/chrome-extension/package.json", "nx/core/package-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/recorder-form": {"root": ["apps/recorder-form/package.json", "nx/core/package-json"], "name": ["apps/recorder-form/package.json", "nx/core/package-json"], "tags": ["apps/recorder-form/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.js": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/recorder-form/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.executor": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.options": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.metadata": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.options.script": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev.executor": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev.options": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev.metadata": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev.options.script": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview.executor": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview.options": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview.metadata": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview.options.script": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview.metadata.scriptContent": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.preview.metadata.runCommand": ["apps/recorder-form/package.json", "nx/core/package-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/report": {"root": ["apps/report/package.json", "nx/core/package-json"], "name": ["apps/report/package.json", "nx/core/package-json"], "tags": ["apps/report/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["apps/report/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["apps/report/package.json", "nx/core/package-json"], "metadata.js": ["apps/report/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/report/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/report/package.json", "nx/core/package-json"], "targets": ["apps/report/package.json", "nx/core/package-json"], "targets.dev": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.executor": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.options": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.metadata": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.options.script": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["apps/report/package.json", "nx/core/package-json"], "targets.build": ["apps/report/package.json", "nx/core/package-json"], "targets.build.executor": ["apps/report/package.json", "nx/core/package-json"], "targets.build.options": ["apps/report/package.json", "nx/core/package-json"], "targets.build.metadata": ["apps/report/package.json", "nx/core/package-json"], "targets.build.options.script": ["apps/report/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["apps/report/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor.executor": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor.options": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor.metadata": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor.options.script": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor.metadata.scriptContent": ["apps/report/package.json", "nx/core/package-json"], "targets.dev:rsdoctor.metadata.runCommand": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor.executor": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor.options": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor.metadata": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor.options.script": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor.metadata.scriptContent": ["apps/report/package.json", "nx/core/package-json"], "targets.build:rsdoctor.metadata.runCommand": ["apps/report/package.json", "nx/core/package-json"], "targets.preview": ["apps/report/package.json", "nx/core/package-json"], "targets.preview.executor": ["apps/report/package.json", "nx/core/package-json"], "targets.preview.options": ["apps/report/package.json", "nx/core/package-json"], "targets.preview.metadata": ["apps/report/package.json", "nx/core/package-json"], "targets.preview.options.script": ["apps/report/package.json", "nx/core/package-json"], "targets.preview.metadata.scriptContent": ["apps/report/package.json", "nx/core/package-json"], "targets.preview.metadata.runCommand": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e.executor": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e.options": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e.metadata": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e.options.script": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e.metadata.scriptContent": ["apps/report/package.json", "nx/core/package-json"], "targets.e2e.metadata.runCommand": ["apps/report/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e.dependsOn": ["nx.json", "nx/target-defaults"], "targets.e2e.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/site": {"root": ["apps/site/package.json", "nx/core/package-json"], "name": ["apps/site/package.json", "nx/core/package-json"], "tags": ["apps/site/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/site/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/site/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/site/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/site/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/site/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/site/package.json", "nx/core/package-json"], "metadata.js": ["apps/site/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/site/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/site/package.json", "nx/core/package-json"], "targets": ["apps/site/package.json", "nx/core/package-json"], "targets.dev": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.executor": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.options": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.metadata": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.options.script": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["apps/site/package.json", "nx/core/package-json"], "targets.build": ["apps/site/package.json", "nx/core/package-json"], "targets.build.executor": ["apps/site/package.json", "nx/core/package-json"], "targets.build.options": ["apps/site/package.json", "nx/core/package-json"], "targets.build.metadata": ["apps/site/package.json", "nx/core/package-json"], "targets.build.options.script": ["apps/site/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["apps/site/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["apps/site/package.json", "nx/core/package-json"], "targets.preview": ["apps/site/package.json", "nx/core/package-json"], "targets.preview.executor": ["apps/site/package.json", "nx/core/package-json"], "targets.preview.options": ["apps/site/package.json", "nx/core/package-json"], "targets.preview.metadata": ["apps/site/package.json", "nx/core/package-json"], "targets.preview.options.script": ["apps/site/package.json", "nx/core/package-json"], "targets.preview.metadata.scriptContent": ["apps/site/package.json", "nx/core/package-json"], "targets.preview.metadata.runCommand": ["apps/site/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/android": {"root": ["packages/android/package.json", "nx/core/package-json"], "name": ["packages/android/package.json", "nx/core/package-json"], "tags": ["packages/android/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/android/package.json", "nx/core/package-json"], "tags.npm:Android UI automation": ["packages/android/package.json", "nx/core/package-json"], "tags.npm:Android AI testing": ["packages/android/package.json", "nx/core/package-json"], "tags.npm:Android automation library": ["packages/android/package.json", "nx/core/package-json"], "tags.npm:Android automation tool": ["packages/android/package.json", "nx/core/package-json"], "tags.npm:Android use": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/android/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/android/package.json", "nx/core/package-json"], "metadata.description": ["packages/android/package.json", "nx/core/package-json"], "metadata.js": ["packages/android/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/android/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/android/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/android/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/android/package.json", "nx/core/package-json"], "targets": ["packages/android/package.json", "nx/core/package-json"], "targets.dev": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/android/package.json", "nx/core/package-json"], "targets.build": ["packages/android/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/android/package.json", "nx/core/package-json"], "targets.build.options": ["packages/android/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/android/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/android/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/android/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/android/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/android/package.json", "nx/core/package-json"], "targets.test": ["packages/android/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/android/package.json", "nx/core/package-json"], "targets.test.options": ["packages/android/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/android/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/android/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/android/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u.executor": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u.options": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u.metadata": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u.options.script": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u.metadata.scriptContent": ["packages/android/package.json", "nx/core/package-json"], "targets.test:u.metadata.runCommand": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai.executor": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai.options": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai.metadata": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai.options.script": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai.metadata.scriptContent": ["packages/android/package.json", "nx/core/package-json"], "targets.test:ai.metadata.runCommand": ["packages/android/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/android-playground": {"root": ["packages/android-playground/package.json", "nx/core/package-json"], "name": ["packages/android-playground/package.json", "nx/core/package-json"], "tags": ["packages/android-playground/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.description": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.js": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/android-playground/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/android-playground/package.json", "nx/core/package-json"], "targets": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server.executor": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server.options": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server.metadata": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server.options.script": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server.metadata.scriptContent": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev:server.metadata.runCommand": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build.options": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/android-playground/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/cli": {"root": ["packages/cli/package.json", "nx/core/package-json"], "name": ["packages/cli/package.json", "nx/core/package-json"], "tags": ["packages/cli/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/cli/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/cli/package.json", "nx/core/package-json"], "metadata.description": ["packages/cli/package.json", "nx/core/package-json"], "metadata.js": ["packages/cli/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/cli/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/cli/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/cli/package.json", "nx/core/package-json"], "targets": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.build": ["packages/cli/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.build.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.new": ["packages/cli/package.json", "nx/core/package-json"], "targets.new.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.new.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.new.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.new.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.new.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.new.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.upgrade.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.test": ["packages/cli/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.test.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp.executor": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp.options": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp.metadata": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp.options.script": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp.metadata.scriptContent": ["packages/cli/package.json", "nx/core/package-json"], "targets.test:ai:temp.metadata.runCommand": ["packages/cli/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/core": {"root": ["packages/core/package.json", "nx/core/package-json"], "name": ["packages/core/package.json", "nx/core/package-json"], "tags": ["packages/core/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/core/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.9": ["packages/core/package.json", "nx/core/package-json"], "metadata.description": ["packages/core/package.json", "nx/core/package-json"], "metadata.js": ["packages/core/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/core/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/core/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/core/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/core/package.json", "nx/core/package-json"], "targets": ["packages/core/package.json", "nx/core/package-json"], "targets.dev": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.build": ["packages/core/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.build.options": ["packages/core/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.new": ["packages/core/package.json", "nx/core/package-json"], "targets.new.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.new.options": ["packages/core/package.json", "nx/core/package-json"], "targets.new.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.new.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.new.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.new.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade.options": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.upgrade.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.test": ["packages/core/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.test.options": ["packages/core/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u.options": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.test:u.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai.options": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.test:ai.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.computer": ["packages/core/package.json", "nx/core/package-json"], "targets.computer.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.computer.options": ["packages/core/package.json", "nx/core/package-json"], "targets.computer.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.computer.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.computer.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.computer.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action.executor": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action.options": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action.metadata": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action.options.script": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action.metadata.scriptContent": ["packages/core/package.json", "nx/core/package-json"], "targets.test:parse-action.metadata.runCommand": ["packages/core/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/evaluation": {"root": ["packages/evaluation/package.json", "nx/core/package-json"], "name": ["packages/evaluation/package.json", "nx/core/package-json"], "tags": ["packages/evaluation/package.json", "nx/core/package-json"], "tags.npm:private": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.9": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.10": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.11": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.12": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.13": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.14": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.15": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.js": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/evaluation/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/evaluation/package.json", "nx/core/package-json"], "targets": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headless.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-page-data:headed.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:coord.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:locator:element.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:coord.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:planning:element.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.download-screenspot-v2.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:assertion.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.update-answer-data:section-locator.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:locator:screenspot-v2.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:planning.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:assertion.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.evaluate:section-locator.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format.executor": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format.options": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format.metadata": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format.options.script": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format.metadata.scriptContent": ["packages/evaluation/package.json", "nx/core/package-json"], "targets.format.metadata.runCommand": ["packages/evaluation/package.json", "nx/core/package-json"]}, "packages/mcp": {"root": ["packages/mcp/package.json", "nx/core/package-json"], "name": ["packages/mcp/package.json", "nx/core/package-json"], "tags": ["packages/mcp/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.js": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/mcp/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/mcp/package.json", "nx/core/package-json"], "targets": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.options": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/mcp/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test.options": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/mcp/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect.executor": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect.options": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect.metadata": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect.options.script": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect.metadata.scriptContent": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect.metadata.runCommand": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2.executor": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2.options": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2.metadata": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2.options.script": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2.metadata.scriptContent": ["packages/mcp/package.json", "nx/core/package-json"], "targets.inspect2.metadata.runCommand": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start.executor": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start.options": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start.metadata": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start.options.script": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["packages/mcp/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["packages/mcp/package.json", "nx/core/package-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/recorder": {"root": ["packages/recorder/package.json", "nx/core/package-json"], "name": ["packages/recorder/package.json", "nx/core/package-json"], "tags": ["packages/recorder/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.js": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/recorder/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/recorder/package.json", "nx/core/package-json"], "targets": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.options": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/recorder/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/recorder/package.json", "nx/core/package-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/shared": {"root": ["packages/shared/package.json", "nx/core/package-json"], "name": ["packages/shared/package.json", "nx/core/package-json"], "tags": ["packages/shared/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.9": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.10": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.11": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.12": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.13": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.14": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.15": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/shared/package.json", "nx/core/package-json"], "targets": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.build": ["packages/shared/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.build.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:pkg.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:script.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.reset.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.bump.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.pre.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.change-status.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.gen-release-note.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.release": ["packages/shared/package.json", "nx/core/package-json"], "targets.release.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.release.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.release.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.release.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.release.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.release.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.new": ["packages/shared/package.json", "nx/core/package-json"], "targets.new.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.new.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.new.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.new.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.new.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.new.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.upgrade.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.test": ["packages/shared/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.test.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.test:u.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/visualizer": {"root": ["packages/visualizer/package.json", "nx/core/package-json"], "name": ["packages/visualizer/package.json", "nx/core/package-json"], "tags": ["packages/visualizer/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.js": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/visualizer/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/visualizer/package.json", "nx/core/package-json"], "targets": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build.options": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve.executor": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve.options": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve.metadata": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve.options.script": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve.metadata.scriptContent": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.serve.metadata.runCommand": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new.executor": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new.options": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new.metadata": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new.options.script": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new.metadata.scriptContent": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.new.metadata.runCommand": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade.executor": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade.options": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade.metadata": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade.options.script": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade.metadata.scriptContent": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.upgrade.metadata.runCommand": ["packages/visualizer/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/web-integration": {"root": ["packages/web-integration/package.json", "nx/core/package-json"], "name": ["packages/web-integration/package.json", "nx/core/package-json"], "tags": ["packages/web-integration/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/web-integration/package.json", "nx/core/package-json"], "tags.npm:AI UI automation": ["packages/web-integration/package.json", "nx/core/package-json"], "tags.npm:AI testing": ["packages/web-integration/package.json", "nx/core/package-json"], "tags.npm:Computer use": ["packages/web-integration/package.json", "nx/core/package-json"], "tags.npm:Browser use": ["packages/web-integration/package.json", "nx/core/package-json"], "tags.npm:Android use": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.5": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.6": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.7": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.8": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.9": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.10": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.11": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.12": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.13": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.14": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.15": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.description": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.js": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/web-integration/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/web-integration/package.json", "nx/core/package-json"], "targets": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev:server.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.postbuild.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.build:watch.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:u.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:bridge.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.test:ai:cache.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.upgrade.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:report.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:cache.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache.executor": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache.options": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache.metadata": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache.options.script": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache.metadata.scriptContent": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.e2e:ui:cache.metadata.runCommand": ["packages/web-integration/package.json", "nx/core/package-json"], "targets.dev.dependsOn": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.outputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.build:watch.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build:watch.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.cache": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e.dependsOn": ["nx.json", "nx/target-defaults"], "targets.e2e.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e:ui.dependsOn": ["nx.json", "nx/target-defaults"], "targets.e2e:ui.parallelism": ["nx.json", "nx/target-defaults"]}}