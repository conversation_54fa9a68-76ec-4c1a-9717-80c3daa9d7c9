#!/usr/bin/env python3
"""
Browser Automation Web UI
Uses the existing browser automation system with proper integration
"""

import gradio as gr
import asyncio
import json
import sys
import os
from pathlib import Path
import subprocess
import requests
import time
import threading
from typing import Dict, Any, Optional

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class BrowserAutomationWebUI:
    def __init__(self):
        self.config_path = project_root / "config" / "browser_automation_config.json"
        self.config = self.load_config()
        self.browser_manager = None
        self.service_status = {
            "browser_automation": False,
            "ui_tars": False,
            "chrome": False
        }
        
    def load_config(self) -> Dict[str, Any]:
        """Load browser automation configuration"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
        
        # Default configuration
        return {
            "ui_tars": {
                "api_url": "http://localhost:8080/v1",
                "api_key": "hf_dummy_key",
                "browser_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "remote_debugging_port": 9222
            }
        }
    
    def check_services(self) -> Dict[str, bool]:
        """Check status of all services"""
        status = {}
        
        # Check UI-TARS
        try:
            response = requests.get("http://localhost:8080/health", timeout=3)
            status["ui_tars"] = response.status_code == 200
        except:
            status["ui_tars"] = False
            
        # Check Chrome debugging
        try:
            response = requests.get("http://localhost:9222/json", timeout=3)
            status["chrome"] = response.status_code == 200
            status["chrome_tabs"] = len(response.json()) if status["chrome"] else 0
        except:
            status["chrome"] = False
            status["chrome_tabs"] = 0
            
        return status
    
    def start_chrome_debug(self) -> str:
        """Start Chrome with remote debugging"""
        try:
            chrome_path = self.config["ui_tars"]["browser_path"]
            cmd = [
                chrome_path,
                "--remote-debugging-port=9222",
                "--user-data-dir=C:\\temp\\chrome_debug",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
            
            subprocess.Popen(cmd)
            time.sleep(3)  # Wait for Chrome to start
            
            # Check if it started
            status = self.check_services()
            if status["chrome"]:
                return "✅ Chrome started with remote debugging on port 9222"
            else:
                return "⚠️ Chrome may have started but debugging port not accessible"
                
        except Exception as e:
            return f"❌ Error starting Chrome: {str(e)}"
    
    def execute_browser_command(self, command: str) -> str:
        """Execute a browser automation command"""
        try:
            # Check services first
            status = self.check_services()
            
            if not status["chrome"]:
                return "❌ Chrome debugging not available. Please start Chrome first."
            
            # Handle basic commands
            if "open google" in command.lower():
                return self.open_website("https://google.com")
            elif "open youtube" in command.lower():
                return self.open_website("https://youtube.com")
            elif "open github" in command.lower():
                return self.open_website("https://github.com")
            elif "status" in command.lower():
                return self.get_status_report()
            elif "start chrome" in command.lower():
                return self.start_chrome_debug()
            else:
                return f"""**Command received:** {command}

**Available commands:**
- "open google" - Opens Google
- "open youtube" - Opens YouTube
- "open github" - Opens GitHub
- "start chrome" - Starts Chrome with debugging
- "status" - Shows system status

**Current Status:**
- Chrome Debug: {'✅' if status['chrome'] else '❌'}
- Chrome Tabs: {status.get('chrome_tabs', 0)}

*More advanced browser automation features coming soon!*
"""
                
        except Exception as e:
            return f"❌ Error executing command: {str(e)}"
    
    def open_website(self, url: str) -> str:
        """Open a website in Chrome"""
        try:
            # Use Chrome to open URL
            chrome_path = self.config["ui_tars"]["browser_path"]
            subprocess.Popen([chrome_path, url])
            return f"🌐 **Opened {url} in Chrome**"
        except Exception as e:
            return f"❌ Error opening website: {str(e)}"
    
    def get_status_report(self) -> str:
        """Get detailed status report"""
        status = self.check_services()
        
        report = f"""**🔍 System Status Report**

**Services:**
- 🌐 Chrome Debug: {'✅ Online' if status['chrome'] else '❌ Offline'}
- 📊 Chrome Tabs: {status.get('chrome_tabs', 0)}
- 🤖 UI-TARS: {'✅ Online' if status['ui_tars'] else '❌ Offline'}

**Endpoints:**
- Chrome Debug: http://localhost:9222
- UI-TARS API: http://localhost:8080

**Browser Path:** {self.config['ui_tars']['browser_path']}

**Ready for Commands:** {'✅ Yes' if status['chrome'] else '❌ Start Chrome first'}
"""
        return report
    
    def chat_interface(self, message: str, history: list) -> tuple:
        """Handle chat messages"""
        if not message.strip():
            return history, ""
        
        # Execute the command
        response = self.execute_browser_command(message)
        
        # Add to history
        history.append([message, response])
        return history, ""

def create_web_interface():
    """Create the Gradio web interface"""
    ui = BrowserAutomationWebUI()
    
    with gr.Blocks(title="Browser Automation System", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🤖 Browser Automation System")
        gr.Markdown("**Native browser control with AI assistance**")
        
        # Status section
        with gr.Row():
            status_btn = gr.Button("🔍 Check Status", size="sm")
            chrome_btn = gr.Button("🌐 Start Chrome Debug", size="sm")
            
        status_output = gr.Markdown()
        
        # Chat interface
        chatbot = gr.Chatbot(
            value=[["System", "🚀 **Browser Automation Ready!**\n\nCommands:\n- 'open google'\n- 'open youtube'\n- 'start chrome'\n- 'status'"]],
            height=400,
            show_label=False
        )
        
        with gr.Row():
            msg = gr.Textbox(
                placeholder="Type your browser command...",
                container=False,
                scale=7
            )
            send_btn = gr.Button("Send", variant="primary", scale=1)
        
        # Quick action buttons
        with gr.Row():
            google_btn = gr.Button("🔍 Google", size="sm")
            youtube_btn = gr.Button("📺 YouTube", size="sm")
            github_btn = gr.Button("💻 GitHub", size="sm")
        
        # Event handlers
        def update_status():
            return ui.get_status_report()
        
        def start_chrome():
            return ui.start_chrome_debug()
        
        status_btn.click(update_status, outputs=status_output)
        chrome_btn.click(start_chrome, outputs=status_output)
        
        send_btn.click(ui.chat_interface, [msg, chatbot], [chatbot, msg])
        msg.submit(ui.chat_interface, [msg, chatbot], [chatbot, msg])
        
        # Quick buttons
        google_btn.click(lambda: ui.chat_interface("open google", chatbot.value)[0], outputs=chatbot)
        youtube_btn.click(lambda: ui.chat_interface("open youtube", chatbot.value)[0], outputs=chatbot)
        github_btn.click(lambda: ui.chat_interface("open github", chatbot.value)[0], outputs=chatbot)
        
    return demo

def main():
    """Main function"""
    print("🚀 Starting Browser Automation Web UI...")
    print("📍 Will run on http://localhost:7788")
    
    try:
        demo = create_web_interface()
        print("✅ Interface created successfully")
        
        demo.launch(
            server_name="0.0.0.0",
            server_port=7788,
            share=False,
            inbrowser=True,
            show_error=True,
            quiet=False
        )
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("🔧 Trying port 7789...")
        try:
            demo.launch(
                server_name="0.0.0.0",
                server_port=7789,
                share=False,
                inbrowser=True,
                show_error=True
            )
        except Exception as e2:
            print(f"❌ Failed: {e2}")

if __name__ == "__main__":
    main()
