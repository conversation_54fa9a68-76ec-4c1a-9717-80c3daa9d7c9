.scrcpy-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .ant-card {
    background: transparent;
    border: none;
    flex: 1;
    display: flex;
    flex-direction: column;

    .ant-card-head {
      display: none;
    }

    .ant-card-body {
      padding: 0;
      flex: 1;
      display: flex;
      flex-direction: column;

      .ant-row {
        flex: 1;
        margin: 0 !important;

        .ant-col {
          padding: 0 !important;

          &:first-child {
            flex: 1;
            display: flex;
            flex-direction: column;
          }
        }
      }
    }
  }

  .header-bar {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border: 1px solid #F5F5F5;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .ant-typography {
        color: #333;
        font-size: 14px;
        margin: 0;
      }

      .anticon {
        color: #999;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          color: #666;
        }
      }
    }

    .screen-info {
      text-align: center;

      .ant-typography {
        color: #666;
        font-size: 14px;
        margin: 0;
      }
    }

    .header-right {
      justify-self: end;
      display: flex;
      gap: 8px;
      align-items: center;

      .ant-btn {
        width: 16px;
        height: 16px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;

        .link-icon {
          font-size: 16px;
          line-height: 1;
        }
      }
    }
  }

  .video-section {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;

    .video-container {
      width: 100%;
      flex: 1;
      background-color: #F5F5F5;
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
      overflow: hidden;
      position: relative;

      .canvas-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      canvas {
        border: none !important;
        box-shadow: none !important;
      }

      .empty-state {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        padding: 24px;
        text-align: center;

        .empty-state-icon {
          font-size: 48px;
          line-height: 1;
        }

        .empty-state-text {
          color: #666;
          font-size: 16px;
          margin-bottom: 8px;
        }

        .loading-spinner {
          margin-top: 16px;

          .ant-spin {
            color: #1677ff;
          }
        }

        .ant-btn {
          min-width: 120px;
          height: 40px;
          font-size: 16px;
          border-radius: 20px;
        }
      }
    }

    .screen-info {
      position: absolute;
      bottom: 8px;
      left: 8px;
      background: rgba(0, 0, 0, 0.5);
      padding: 4px 8px;
      border-radius: 4px;

      .ant-typography {
        color: white;
        margin: 0;
      }
    }
  }
}

/* 确保响应式布局 */
@media (max-width: 576px) {
  .scrcpy-container {
    .header-bar {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto;
      padding: 8px;
      gap: 8px;

      .header-left {
        text-align: center;
      }

      .screen-info {
        text-align: center;
      }

      .header-right {
        justify-self: center;
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}