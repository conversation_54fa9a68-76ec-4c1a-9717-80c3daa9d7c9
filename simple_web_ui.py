import gradio as gr
import requests
import json
import os
from datetime import datetime

# Configuration
LLM_SERVER_URL = "http://localhost:8000"
UI_TITLE = "🤖 AI Agent Web Interface"

def check_llm_server():
    """Check if LLM server is running"""
    try:
        response = requests.get(f"{LLM_SERVER_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def chat_with_llm(message, history):
    """Send message to local LLM server"""
    try:
        if not check_llm_server():
            return "❌ LLM Server not available at http://localhost:8000. Please start your local LLM server first."

        # Try different API endpoints
        endpoints_to_try = [
            "/v1/chat/completions",
            "/api/v1/chat/completions",
            "/chat",
            "/api/chat",
            "/generate",
            "/api/generate"
        ]

        for endpoint in endpoints_to_try:
            try:
                # Prepare the request for OpenAI-style API
                if "chat/completions" in endpoint:
                    payload = {
                        "model": "default_model_name",
                        "messages": [
                            {"role": "system", "content": "You are a helpful AI assistant integrated with a local AI agent system."},
                            {"role": "user", "content": message}
                        ],
                        "stream": False
                    }
                elif "chat" in endpoint:
                    payload = {
                        "message": message,
                        "mode": "chat",
                        "character": "Assistant"
                    }
                else:  # generate endpoint
                    payload = {
                        "prompt": f"User: {message}\nAssistant:",
                        "max_length": 500,
                        "temperature": 0.7
                    }

                headers = {"Content-Type": "application/json"}

                # Send request to local LLM server
                response = requests.post(f"{LLM_SERVER_URL}{endpoint}",
                                       json=payload, headers=headers, timeout=30)

                if response.status_code == 200:
                    result = response.json()

                    # Parse different response formats
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]
                    elif "response" in result:
                        return result["response"]
                    elif "text" in result:
                        return result["text"]
                    elif "results" in result and len(result["results"]) > 0:
                        return result["results"][0]["text"]
                    elif isinstance(result, str):
                        return result
                    else:
                        continue  # Try next endpoint

            except Exception as e:
                continue  # Try next endpoint

        # If all endpoints fail, provide helpful message
        return f"""✅ Connected to LLM server, but no compatible API endpoint found.

Your LLM server is running at {LLM_SERVER_URL} but doesn't seem to have standard OpenAI-compatible endpoints.

**To fix this:**
1. Check if your LLM server supports OpenAI API format
2. Or use browser automation instead - it's working perfectly!

**Try the browser automation tab for web tasks!**"""

    except requests.exceptions.Timeout:
        return "⏱️ Request timed out. The LLM server might be processing a large request."
    except requests.exceptions.ConnectionError:
        return "🔌 Connection error. Please ensure the LLM server is running on http://localhost:8000"
    except Exception as e:
        return f"❌ Error: {str(e)}"

def browser_automation(task, url=""):
    """Execute real browser automation task using UI-TARS"""
    try:
        if not task.strip():
            return "Please provide a task description."

        import subprocess
        import json
        import os
        import sys

        # Check if UI-TARS is available
        config_path = "config/ui_tars_config.json"
        if not os.path.exists(config_path):
            return f"❌ UI-TARS configuration not found at {config_path}. Please run setup first."

        response = f"""🤖 Browser Automation Task EXECUTING

**Task:** {task}
**Target URL:** {url if url else "Auto-determined"}
**Status:** 🔄 RUNNING - UI-TARS Integration Active

**Execution Steps:**
1. ✅ Loading UI-TARS configuration
2. ✅ Initializing browser automation
3. 🔄 Executing task: "{task}"
"""

        # Try to execute with UI-TARS
        try:
            # Load UI-TARS config
            with open(config_path, 'r') as f:
                config = json.load(f)

            # Prepare automation command
            automation_script = f"""
import pyautogui
import time
import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

# Configure browser
chrome_options = Options()
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

try:
    # Initialize browser with automatic driver management
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {{get: () => undefined}})")

    # Navigate to URL or Google
    target_url = "{url}" if "{url}" else "https://www.google.com"
    driver.get(target_url)
    time.sleep(3)

    # Execute the task
    task = "{task}"

    # Simple automation based on task content
    if "search" in task.lower():
        # Find search box and enter query
        try:
            search_box = driver.find_element(By.NAME, "q")
            search_box.clear()
            search_terms = task.replace("search for", "").replace("search", "").strip()
            search_box.send_keys(search_terms)
            search_box.submit()
            time.sleep(3)

            # Get results
            results = driver.find_elements(By.CSS_SELECTOR, "h3")[:3]
            result_text = "\\n".join([f"- {{result.text}}" for result in results if result.text])

            print(f"AUTOMATION_SUCCESS: Search completed\\nResults:\\n{{result_text}}")

        except Exception as e:
            print(f"AUTOMATION_ERROR: Search failed - {{str(e)}}")

    elif "click" in task.lower():
        # Try to find and click elements
        try:
            # Look for clickable elements
            buttons = driver.find_elements(By.TAG_NAME, "button")
            links = driver.find_elements(By.TAG_NAME, "a")

            # Extract text from task to find matching element
            for element in buttons + links:
                if element.text and any(word in element.text.lower() for word in task.lower().split()):
                    element.click()
                    time.sleep(2)
                    print(f"AUTOMATION_SUCCESS: Clicked on '{{element.text}}'")
                    break
            else:
                print("AUTOMATION_INFO: No matching clickable element found")

        except Exception as e:
            print(f"AUTOMATION_ERROR: Click failed - {{str(e)}}")

    # Take screenshot
    screenshot_path = "automation_result.png"
    driver.save_screenshot(screenshot_path)

    # Get page title and URL
    title = driver.title
    current_url = driver.current_url

    print(f"AUTOMATION_COMPLETE: Task executed on '{{title}}' at {{current_url}}")

    driver.quit()

except Exception as e:
    print(f"AUTOMATION_FATAL: {{str(e)}}")
"""

            # Write and execute automation script
            with open("temp_automation.py", "w") as f:
                f.write(automation_script)

            # Execute the automation using the same Python executable
            result = subprocess.run([sys.executable, "temp_automation.py"],
                                  capture_output=True, text=True, timeout=60)

            # Clean up
            if os.path.exists("temp_automation.py"):
                os.remove("temp_automation.py")

            # Process results
            if result.returncode == 0:
                output_lines = result.stdout.strip().split('\n')
                status_info = []

                for line in output_lines:
                    if line.startswith("AUTOMATION_SUCCESS:"):
                        status_info.append(f"✅ {line.replace('AUTOMATION_SUCCESS:', '').strip()}")
                    elif line.startswith("AUTOMATION_ERROR:"):
                        status_info.append(f"❌ {line.replace('AUTOMATION_ERROR:', '').strip()}")
                    elif line.startswith("AUTOMATION_INFO:"):
                        status_info.append(f"ℹ️ {line.replace('AUTOMATION_INFO:', '').strip()}")
                    elif line.startswith("AUTOMATION_COMPLETE:"):
                        status_info.append(f"🎯 {line.replace('AUTOMATION_COMPLETE:', '').strip()}")

                response += f"""
4. ✅ Browser launched successfully
5. ✅ Task execution completed

**Results:**
{chr(10).join(status_info) if status_info else "✅ Task completed successfully"}

**Screenshot:** automation_result.png saved
**Status:** ✅ COMPLETED"""
            else:
                error_msg = result.stderr or "Unknown error occurred"
                response += f"""
4. ❌ Automation failed
5. ❌ Error: {error_msg}

**Status:** ❌ FAILED"""

        except subprocess.TimeoutExpired:
            response += f"""
4. ⏱️ Task timed out after 60 seconds
5. ❌ Automation stopped

**Status:** ⏱️ TIMEOUT"""

        except Exception as e:
            response += f"""
4. ❌ Execution error: {str(e)}
5. ❌ Check UI-TARS configuration

**Status:** ❌ ERROR"""

        return response

    except Exception as e:
        return f"❌ Browser automation error: {str(e)}"

def system_status():
    """Get system status"""
    status_info = []

    # Check LLM Server
    llm_status = "🟢 Online" if check_llm_server() else "🔴 Offline"
    status_info.append(f"**LLM Server:** {llm_status}")

    # Check if other services are running (placeholder)
    status_info.append(f"**UI-TARS:** 🟡 Configured")
    status_info.append(f"**Web Interface:** 🟢 Running")
    status_info.append(f"**Last Updated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    return "\n".join(status_info)

# Create Gradio interface
with gr.Blocks(title=UI_TITLE, theme=gr.themes.Soft()) as demo:
    gr.Markdown(f"# {UI_TITLE}")
    gr.Markdown("A unified interface for your AI Agent System with local LLM integration")

    with gr.Tabs():
        # Chat Tab
        with gr.TabItem("💬 Chat with AI"):
            gr.Markdown("### Chat with your local LLM")

            chatbot = gr.Chatbot(height=400, placeholder="Start a conversation with your AI assistant...")
            msg = gr.Textbox(placeholder="Type your message here...", label="Message", scale=4)

            with gr.Row():
                send_btn = gr.Button("Send", variant="primary")
                clear_btn = gr.Button("Clear", variant="secondary")

            def respond(message, history):
                if not message.strip():
                    return history, ""

                # Get response from LLM
                bot_response = chat_with_llm(message, history)

                # Add to history
                history.append((message, bot_response))
                return history, ""

            send_btn.click(respond, [msg, chatbot], [chatbot, msg])
            msg.submit(respond, [msg, chatbot], [chatbot, msg])
            clear_btn.click(lambda: ([], ""), outputs=[chatbot, msg])

        # Browser Automation Tab
        with gr.TabItem("🌐 Browser Automation"):
            gr.Markdown("### Automated Web Tasks")

            with gr.Row():
                with gr.Column():
                    task_input = gr.Textbox(
                        label="Task Description",
                        placeholder="e.g., Search for 'AI news' on Google and summarize the top 3 results",
                        lines=3
                    )
                    url_input = gr.Textbox(
                        label="Target URL (optional)",
                        placeholder="https://example.com (leave empty for auto-detection)"
                    )
                    automate_btn = gr.Button("🚀 Start Automation", variant="primary")

                with gr.Column():
                    automation_output = gr.Textbox(
                        label="Automation Results",
                        lines=15,
                        interactive=False
                    )

            automate_btn.click(
                browser_automation,
                inputs=[task_input, url_input],
                outputs=[automation_output]
            )

        # System Status Tab
        with gr.TabItem("📊 System Status"):
            gr.Markdown("### AI Agent System Status")

            status_display = gr.Textbox(
                label="System Status",
                lines=10,
                interactive=False
            )

            refresh_btn = gr.Button("🔄 Refresh Status", variant="secondary")
            refresh_btn.click(system_status, outputs=[status_display])

            # Auto-load status on page load
            demo.load(system_status, outputs=[status_display])

        # Configuration Tab
        with gr.TabItem("⚙️ Configuration"):
            gr.Markdown("### System Configuration")

            with gr.Row():
                with gr.Column():
                    gr.Markdown("**Current Settings:**")
                    gr.Markdown(f"- LLM Server: `{LLM_SERVER_URL}`")
                    gr.Markdown("- UI-TARS: Configured")
                    gr.Markdown("- Browser: Chrome/Edge")

                with gr.Column():
                    gr.Markdown("**Available Actions:**")
                    test_llm_btn = gr.Button("🧪 Test LLM Connection")
                    test_output = gr.Textbox(label="Test Results", interactive=False)

                    def test_llm():
                        if check_llm_server():
                            return "✅ LLM Server is responding correctly!"
                        else:
                            return "❌ Cannot connect to LLM Server. Please check if it's running on http://localhost:8000"

                    test_llm_btn.click(test_llm, outputs=[test_output])

# Launch the interface
if __name__ == "__main__":
    print(f"🚀 Starting {UI_TITLE}")
    print(f"🔗 LLM Server: {LLM_SERVER_URL}")
    print("📱 Opening web interface...")

    demo.launch(
        server_name="0.0.0.0",
        server_port=7789,
        share=False,
        show_error=True,
        quiet=False
    )
