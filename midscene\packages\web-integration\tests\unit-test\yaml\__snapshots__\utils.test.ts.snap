// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`utils > build yaml 1`] = `
"target:
  url: https://www.example.com
tasks: []
"
`;

exports[`utils > parseYamlScript > aiRightClick 1`] = `
{
  "target": {
    "url": "sample_url",
  },
  "tasks": [
    {
      "sleep": 1000,
    },
    {
      "aiTap": "sample_button",
    },
    {
      "aiRightClick": "context menu trigger",
    },
    {
      "aiInput": {
        "aiInput": "<EMAIL>",
        "locate": "email input",
      },
    },
  ],
}
`;

exports[`utils > parseYamlScript > interpolates environment variables 1`] = `
{
  "target": {
    "url": "sample_url",
  },
  "tasks": [
    {
      "sleep": 1000,
    },
    {
      "aiTap": "sample_button",
    },
    {
      "aiInput": "sample_input",
      "locate": "input description",
    },
    {
      "aiInput": null,
      "locate": "input description",
    },
  ],
}
`;
