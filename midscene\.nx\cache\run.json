{"run": {"command": "nx run-many --target=build --exclude=doc --verbose", "startTime": "2025-07-01T06:31:49.777Z", "endTime": "2025-07-01T06:32:28.777Z", "inner": false}, "tasks": [{"taskId": "@midscene/shared:build", "target": "build", "projectName": "@midscene/shared", "hash": "14836644655962660180", "startTime": "2025-07-01T06:31:49.861Z", "endTime": "2025-07-01T06:31:50.111Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/recorder:build", "target": "build", "projectName": "@midscene/recorder", "hash": "10982747808515421470", "startTime": "2025-07-01T06:31:50.139Z", "endTime": "2025-07-01T06:31:50.209Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/core:build", "target": "build", "projectName": "@midscene/core", "hash": "14084570116937047948", "startTime": "2025-07-01T06:31:50.228Z", "endTime": "2025-07-01T06:31:50.398Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "recorder-form:build", "target": "build", "projectName": "recorder-form", "hash": "14494673059453964598", "startTime": "2025-07-01T06:31:50.228Z", "endTime": "2025-07-01T06:31:50.456Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/web:build", "target": "build", "projectName": "@midscene/web", "hash": "15534688145438870169", "startTime": "2025-07-01T06:31:50.477Z", "endTime": "2025-07-01T06:31:51.201Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/visualizer:build", "target": "build", "projectName": "@midscene/visualizer", "hash": "16029003187442401497", "startTime": "2025-07-01T06:31:51.262Z", "endTime": "2025-07-01T06:31:52.414Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/android:build", "target": "build", "projectName": "@midscene/android", "hash": "17474892781539370199", "startTime": "2025-07-01T06:31:51.262Z", "endTime": "2025-07-01T06:31:52.488Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/report:build", "target": "build", "projectName": "@midscene/report", "hash": "13178982952536718631", "startTime": "2025-07-01T06:31:52.652Z", "endTime": "2025-07-01T06:31:52.894Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/android-playground:build", "target": "build", "projectName": "@midscene/android-playground", "hash": "9094590661307643062", "startTime": "2025-07-01T06:31:52.732Z", "endTime": "2025-07-01T06:31:52.971Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "android-playground:build", "target": "build", "projectName": "android-playground", "hash": "17661735116965048114", "startTime": "2025-07-01T06:31:52.732Z", "endTime": "2025-07-01T06:31:53.244Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/mcp:build", "target": "build", "projectName": "@midscene/mcp", "hash": "13573595319394516285", "startTime": "2025-07-01T06:31:53.380Z", "endTime": "2025-07-01T06:31:53.812Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "@midscene/cli:build", "target": "build", "projectName": "@midscene/cli", "hash": "1498164784708699680", "startTime": "2025-07-01T06:31:53.444Z", "endTime": "2025-07-01T06:31:53.975Z", "params": "", "cacheStatus": "local-cache-hit", "status": 0}, {"taskId": "chrome-extension:build", "target": "build", "projectName": "chrome-extension", "hash": "7980820313252809227", "startTime": "2025-07-01T06:31:53.277Z", "endTime": "2025-07-01T06:32:28.541Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}