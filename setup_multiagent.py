"""
Setup script for the Multi-Agent AI System.
Installs all required dependencies and configures the environment.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="Set up the Multi-Agent AI System.")
    parser.add_argument("--dev", action="store_true", help="Install development dependencies")
    parser.add_argument("--skip-gpu-check", action="store_true", help="Skip GPU support check")
    parser.add_argument("--skip-playwright", action="store_true", help="Skip Playwright browser installation")
    args = parser.parse_args()

    print("====== Multi-Agent AI System Setup ======")
    print("=======================================")

    # Check Python version
    check_python_version()

    # Check GPU support
    if not args.skip_gpu_check:
        check_gpu_support()

    # Create virtual environment
    venv_path = create_virtual_env()

    # Install requirements
    install_requirements(venv_path, args.dev)

    # Setup Playwright if not skipped
    if not args.skip_playwright:
        setup_playwright(venv_path)

    # Create activation scripts
    create_activation_scripts(venv_path)

    print("=======================================")
    print("✅ Setup completed successfully!")
    print(f"To activate the environment:")
    if os.name == 'nt':
        print(f"   - Windows CMD: scripts\\activate.bat")
        print(f"   - PowerShell: .\\scripts\\activate.ps1")
    else:
        print(f"   - Bash: source scripts/activate.sh")
    print(f"Then run: python dashboard_launcher.py")
    print("=======================================")

def check_python_version():
    """Check if Python version is compatible."""
    required_version = (3, 9)
    current_version = sys.version_info[:2]

    if current_version < required_version:
        print(f"Error: Python {required_version[0]}.{required_version[1]} or higher is required.")
        print(f"Current version: Python {current_version[0]}.{current_version[1]}")
        sys.exit(1)

    print(f"✅ Python version {current_version[0]}.{current_version[1]} is compatible.")

def create_virtual_env():
    """Create a virtual environment for the project."""
    venv_path = Path(".venvAI")

    if venv_path.exists():
        print(f"ℹ️ Virtual environment already exists at {venv_path}")
        return venv_path

    print("Creating virtual environment...")

    try:
        subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
        print(f"✅ Virtual environment created at {venv_path}")
        return venv_path
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        sys.exit(1)

def install_requirements(venv_path, dev_mode=False):
    """Install all required packages."""
    # Get the pip executable for the virtual environment
    if os.name == 'nt':  # Windows
        pip_path = venv_path / "Scripts" / "pip"
    else:  # Unix/Linux/MacOS
        pip_path = venv_path / "bin" / "pip"

    # Upgrade pip
    print("Upgrading pip...")
    subprocess.run([str(pip_path), "install", "--upgrade", "pip"], check=True)

    # Install core requirements
    print("Installing core requirements...")
    subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)

    # Install integration requirements
    print("Installing integration requirements...")
    subprocess.run([str(pip_path), "install", "-r", "integration_requirements.txt"], check=True)

    # Install development requirements if in dev mode
    if dev_mode:
        print("Installing development requirements...")
        try:
            subprocess.run([str(pip_path), "install", "-r", "requirements-dev.txt"], check=True)
        except subprocess.CalledProcessError:
            print("⚠️ Could not find requirements-dev.txt. Skipping development dependencies.")

    print("✅ All requirements installed successfully.")

def setup_playwright(venv_path):
    """Install Playwright browsers."""
    print("Setting up Playwright...")

    if os.name == 'nt':  # Windows
        playwright_path = venv_path / "Scripts" / "playwright"
    else:  # Unix/Linux/MacOS
        playwright_path = venv_path / "bin" / "playwright"

    try:
        subprocess.run([str(playwright_path), "install", "--with-deps", "chromium"], check=True)
        print("✅ Playwright browsers installed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Playwright browsers: {e}")
        print("ℹ️ You can manually install them later with: playwright install --with-deps chromium")

def check_gpu_support():
    """Check for GPU support."""
    print("Checking for GPU support...")

    try:
        # Try to import torch without installing it
        import importlib.util
        torch_spec = importlib.util.find_spec("torch")

        if torch_spec is not None:
            import torch

            if torch.cuda.is_available():
                print(f"✅ CUDA is available. Found {torch.cuda.device_count()} device(s).")
                for i in range(torch.cuda.device_count()):
                    print(f"   - GPU {i}: {torch.cuda.get_device_name(i)}")
            else:
                print("ℹ️ CUDA is not available. System will use CPU for processing.")
        else:
            print("ℹ️ PyTorch not installed yet. GPU support will be checked after installation.")
    except Exception as e:
        print(f"ℹ️ Could not check GPU support: {e}")
        print("ℹ️ GPU support will be checked after installation.")

def create_activation_scripts(venv_path):
    """Create activation scripts for different platforms."""
    scripts_dir = Path("scripts")
    scripts_dir.mkdir(exist_ok=True)

    # Windows batch file
    with open(scripts_dir / "activate.bat", "w") as f:
        f.write(f'@echo off\n')
        f.write(f'call "{venv_path / "Scripts" / "activate.bat"}"\n')
        f.write(f'echo Environment activated. Run "python dashboard_launcher.py" to start the dashboard.\n')

    # PowerShell script
    with open(scripts_dir / "activate.ps1", "w") as f:
        f.write(f'& "{venv_path / "Scripts" / "Activate.ps1"}"\n')
        f.write(f'Write-Host "Environment activated. Run python dashboard_launcher.py to start the dashboard."\n')

    # Bash script for Unix/Linux/MacOS
    with open(scripts_dir / "activate.sh", "w") as f:
        f.write(f'#!/bin/bash\n')
        f.write(f'source "{venv_path / "bin" / "activate"}"\n')
        f.write(f'echo "Environment activated. Run python dashboard_launcher.py to start the dashboard."\n')

    # Make the bash script executable
    if os.name != 'nt':
        os.chmod(scripts_dir / "activate.sh", 0o755)

    print("✅ Activation scripts created in 'scripts' directory.")

if __name__ == "__main__":
    main()
