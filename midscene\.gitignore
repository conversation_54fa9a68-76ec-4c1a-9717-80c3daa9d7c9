# Logs
*.log
*.log.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.*

# next.js build output
.next

# OS X temporary files
.DS_Store

# Rush temporary files
common/deploy/
common/temp/
common/autoinstallers/*/.npmrc
**/.rush/temp/

# Heft
.heft

# pnpm store
.pnpm-store

.eden-mono

dist
dist-script
doc_build
build_cn

## for scm
output
output_resource

## ts build
tsconfig.tsbuildinfo

.temp
.output

## local debug scripts
coze

# emo infraDir: ''

node_modules/
test-results/
playwright-report/
blob-report/
playwright/.cache/
iife-script/

# Midscene.js dump files
__ai_responses__/

.nx/cache
.nx/workspace-data
# Midscene.js dump files
midscene_run
midscene_run/report
midscene_run/dump

extension_output
.cursor
android-playground/static/
.cursor
CLAUDE.md
.claude
**/.claude
**/CLAUDE.md
.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
