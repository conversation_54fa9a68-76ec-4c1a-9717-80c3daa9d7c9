{"name": "jest-each", "version": "27.5.1", "description": "Parameterised tests for Jest", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-each"}, "keywords": ["jest", "parameterised", "test", "each"], "author": "<PERSON> (mattphillips)", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "chalk": "^4.0.0", "jest-get-type": "^27.5.1", "jest-util": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}