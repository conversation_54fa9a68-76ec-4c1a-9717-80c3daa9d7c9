@import './common.less';

@player-spacing: 12px;
@player-vertical-spacing: 15px; // ~=12 * 1.2
@border-color: #979797;
@radius: 8px;
@player-control-bg: #666;
@timeline-height: 4px;
@tools-height: 42px;
@tools-margin: 15px;

.player-container {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  padding: 8px;
  background: #fff;
  box-sizing: border-box;
  border: 1px solid #0000000F;
  border-radius: @radius;
  line-height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  overflow: visible;
  min-height: 300px;
  position: relative;

  .canvas-container {
    flex: 1;
    min-height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 8px;
    margin-bottom: @player-spacing;
    position: relative;
    background-color: #F5F5F5;
    border-radius: inherit;

    canvas {
      max-width: 100%;
      max-height: 100%;
      box-sizing: border-box;
      display: block;
      margin: 0 auto;
      object-fit: contain;
    }
  }

  .player-timeline-wrapper {
    width: 100%;
    height: @timeline-height;
    flex: none;
    margin-bottom: 2px;
    position: relative;
  }

  .player-timeline {
    width: 100%;
    height: @timeline-height;
    background: @player-control-bg;
    position: relative;
    flex-shrink: 0;

    .player-timeline-progress {
      transition-timing-function: linear;
      position: absolute;
      top: 0;
      left: 0;
      height: @timeline-height;
      background: @primary-color;
    }
  }

  .player-tools-wrapper {
    width: 100%;
    height: @tools-height + @player-vertical-spacing * 2;
    flex: none;
    position: relative;
    padding-top: @player-vertical-spacing;
    padding-bottom: @player-vertical-spacing;
    box-sizing: border-box;
  }

  .player-tools {
    width: 100%;
    height: @tools-height;
    max-width: 100%;
    overflow: hidden;
    color: #000;
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-shrink: 0;

    .ant-spin {
      color: #333;
    }

    .player-control {
      flex-grow: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      overflow: hidden;
    }

    .status-icon {
      transition: .2s;
      width: 32px;
      height: 32px;
      border-radius: @radius;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-left: 10px;

      &:hover {
        cursor: pointer;
        background: #F0f0f0;
      }
    }

    .status-text {
      flex-grow: 1;
      flex-shrink: 1;
      min-width: 0;
      overflow: hidden;
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 0;
    }

    .title {
      font-weight: 600;
    }

    .title,
    .subtitle {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 100%;
    }

    .player-tools-item {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}