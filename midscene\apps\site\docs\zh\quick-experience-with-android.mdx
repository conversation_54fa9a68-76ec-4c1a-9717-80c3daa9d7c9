import StartExperience from './common/start-experience.mdx';
import PrepareAndroid from './common/prepare-android.mdx';

# 使用 Android Playground 快速体验

通过使用 Midscene.js Android 设备，你可以快速在 Android 设备上体验 Midscene 的主要功能，而无需编写任何代码。

该 Playground 和 `@midscene/android` 包共享一份代码，因此你可以将其视为 Midscene Android SDK 的一个 Playground 或调试工具。

![](/android-playground.png)

<PrepareAndroid />

## 启动 Playground

```bash
npx --yes @midscene/android-playground
```

## 配置 API Key

点击齿轮按钮，进入配置页面：

![](/android-set-env.png)

参考 [配置模型和服务商](./model-provider) 文档，配置 API Key。

<StartExperience />

* [与 Android(adb) 集成](./integrate-with-android)
