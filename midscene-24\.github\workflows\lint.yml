name: Lint

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
      
permissions:
  contents: read

jobs:
  main:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.19.0]

    env:
      MIDSCENE_OPENAI_INIT_CONFIG_JSON: ${{ secrets.MIDSCENE_OPENAI_INIT_CONFIG_JSON }}
      MIDSCENE_OPENAI_MODEL: ${{ secrets.MIDSCENE_OPENAI_MODEL }}

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Fetch all branches
      run: git fetch --all

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9.3.0
  
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'

    - name: Install Dependencies
      run: pnpm install --ignore-scripts

    - name: Check Dependency Version
      run: pnpm run check-dependency-version

    - name: Biome lint
      run: npx biome check . --diagnostic-level=warn --no-errors-on-unmatched

