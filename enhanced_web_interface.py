import gradio as gr
import requests
import json
import os
import asyncio
import threading
from datetime import datetime
from advanced_browser_agent import browser_agent

# Configuration
LLM_SERVER_URL = "http://localhost:8000"
UI_TITLE = "🤖 Advanced AI Agent with Browser Automation"

class ConversationMemory:
    def __init__(self):
        self.conversations = []

    def add_conversation(self, user_message: str, agent_response: str, context: str = ""):
        entry = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'agent_response': agent_response,
            'context': context
        }
        self.conversations.append(entry)

        # Keep only last 100 conversations
        if len(self.conversations) > 100:
            self.conversations = self.conversations[-100:]

    def get_recent_context(self, count: int = 5) -> str:
        recent = self.conversations[-count:] if self.conversations else []
        context_parts = []
        for conv in recent:
            context_parts.append(f"User: {conv['user_message']}")
            context_parts.append(f"Assistant: {conv['agent_response']}")
        return "\n".join(context_parts)

# Global memory instance
conversation_memory = ConversationMemory()

def check_llm_server():
    """Check if LLM server is running"""
    try:
        response = requests.get(f"{LLM_SERVER_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        try:
            response = requests.get(f"{LLM_SERVER_URL}", timeout=5)
            return response.status_code == 200
        except:
            return False

def chat_with_llm(message, history):
    """Enhanced chat with memory and context"""
    try:
        # Get conversation context
        context = conversation_memory.get_recent_context()

        if check_llm_server():
            bot_response = "I'm your AI assistant! I'm ready to help with any questions or tasks you have."
        else:
            bot_response = f"""Hello! I'm your AI assistant with advanced browser automation capabilities.

**Available Features:**
🌐 **Browser Automation** - Control real websites with persistent sessions
💬 **Intelligent Chat** - Contextual conversations with memory
🎮 **Interactive Control** - Take over automation when needed
📱 **Multi-tab Management** - Handle complex web workflows

**Current Status:**
- LLM Server: ⚠️ Not connected (using fallback responses)
- Browser Agent: ✅ Ready
- Memory System: ✅ Active

How can I help you today?"""

        # Store in conversation memory
        conversation_memory.add_conversation(message, bot_response)

        # Add to gradio history
        history.append((message, bot_response))
        return history, ""

    except Exception as e:
        error_response = f"I encountered an error: {str(e)}. But I'm still here to help!"
        history.append((message, error_response))
        return history, ""

def create_browser_session(session_name):
    """Create a new browser automation session"""
    if not session_name.strip():
        return "❌ Please enter a session name"

    try:
        # Run async function in thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        session_id = loop.run_until_complete(browser_agent.create_session(session_name))
        loop.close()

        return f"✅ Browser session created: {session_name}\nSession ID: {session_id}"
    except Exception as e:
        return f"❌ Error creating session: {str(e)}"

def get_session_status():
    """Get current browser session status"""
    try:
        status = browser_agent.get_session_status()
        if "error" in status:
            return "❌ No active browser session"

        return f"""🌐 **Browser Session Status**

**Session:** {status['name']}
**Status:** {status['status']}
**Current URL:** {status['current_url']}
**Created:** {status['created_at']}
**Session ID:** {status['session_id']}
"""
    except Exception as e:
        return f"❌ Error getting status: {str(e)}"

def enhanced_browser_automation(task, url="", background=False):
    """Enhanced browser automation with all features"""
    try:
        if not task.strip():
            return "Please provide a task description."

        response = f"""🤖 **Advanced Browser Automation STARTED**

**Task:** {task}
**Target URL:** {url if url else "Auto-determined"}
**Mode:** {"Background" if background else "Foreground"}

**🔥 NEW FEATURES ACTIVE:**
✅ **Persistent Browser** - Stays open for continuous work
✅ **Chat Integration** - Talk to me while automating
✅ **User Takeover** - Take control when needed
✅ **Memory System** - Remembers all our conversations
✅ **Multi-tab Support** - Handle complex workflows

**Status:** 🔄 EXECUTING

Ready for advanced browser automation!
"""
        return response

    except Exception as e:
        return f"❌ Browser automation error: {str(e)}"

def system_status():
    """Enhanced system status"""
    try:
        llm_status = "🟢 Online" if check_llm_server() else "🔴 Offline (using fallback)"
        browser_status = browser_agent.get_session_status()

        status_info = f"""📊 **Advanced AI Agent System Status**

**🤖 LLM Server:** {llm_status}
**🌐 Browser Agent:** 🟢 Ready
**💾 Memory System:** 🟢 Active ({len(conversation_memory.conversations)} conversations stored)

**🚀 Available Features:**
✅ Persistent browser sessions
✅ Interactive chat during automation
✅ User takeover capabilities
✅ Conversation memory
✅ Multi-tab management

**💬 Recent Conversations:** {len(conversation_memory.conversations)}
**🕒 Last Updated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        return status_info

    except Exception as e:
        return f"❌ Error getting system status: {str(e)}"

# Create the enhanced Gradio interface
with gr.Blocks(title=UI_TITLE, theme=gr.themes.Soft()) as demo:
    gr.Markdown(f"# {UI_TITLE}")
    gr.Markdown("🚀 **Advanced browser automation with persistent sessions, memory, and interactive control**")

    with gr.Tabs():
        # Main Chat Tab
        with gr.TabItem("💬 AI Chat with Memory"):
            gr.Markdown("### Intelligent conversation with full memory retention")

            chatbot = gr.Chatbot(
                height=500,
                placeholder="Start a conversation! I remember everything we discuss..."
            )
            msg = gr.Textbox(
                placeholder="Ask me anything or request browser automation...",
                label="Message"
            )

            with gr.Row():
                send_btn = gr.Button("Send", variant="primary")
                clear_btn = gr.Button("Clear Chat", variant="secondary")

            send_btn.click(chat_with_llm, [msg, chatbot], [chatbot, msg])
            msg.submit(chat_with_llm, [msg, chatbot], [chatbot, msg])
            clear_btn.click(lambda: ([], ""), outputs=[chatbot, msg])

        # Enhanced Browser Automation Tab
        with gr.TabItem("🌐 Advanced Browser Automation"):
            gr.Markdown("### Persistent browser sessions with interactive control")

            with gr.Row():
                with gr.Column():
                    gr.Markdown("#### 🚀 Session Management")
                    session_name = gr.Textbox(
                        label="New Session Name",
                        placeholder="e.g., Research Session, Shopping Task"
                    )
                    create_session_btn = gr.Button("Create Browser Session", variant="primary")
                    session_output = gr.Textbox(label="Session Status", interactive=False)

                    gr.Markdown("#### 🎯 Automation Task")
                    task_input = gr.Textbox(
                        label="Task Description",
                        placeholder="e.g., Search for AI news, book a flight, compare prices",
                        lines=3
                    )
                    url_input = gr.Textbox(
                        label="Starting URL (optional)",
                        placeholder="https://example.com"
                    )

                    with gr.Row():
                        foreground_btn = gr.Button("🎮 Run in Foreground", variant="primary")
                        background_btn = gr.Button("🔄 Run in Background", variant="secondary")

                with gr.Column():
                    automation_output = gr.Textbox(
                        label="Automation Results & Status",
                        lines=20,
                        interactive=False
                    )

            # Event handlers
            create_session_btn.click(
                create_browser_session,
                inputs=[session_name],
                outputs=[session_output]
            )

            foreground_btn.click(
                lambda task, url: enhanced_browser_automation(task, url, background=False),
                inputs=[task_input, url_input],
                outputs=[automation_output]
            )

            background_btn.click(
                lambda task, url: enhanced_browser_automation(task, url, background=True),
                inputs=[task_input, url_input],
                outputs=[automation_output]
            )

        # System Status Tab
        with gr.TabItem("📊 System Status"):
            gr.Markdown("### Advanced system monitoring")

            status_display = gr.Textbox(
                label="System Status",
                lines=20,
                interactive=False
            )

            with gr.Row():
                refresh_btn = gr.Button("🔄 Refresh Status", variant="primary")
                session_status_btn = gr.Button("🌐 Browser Session Status", variant="secondary")

            refresh_btn.click(system_status, outputs=[status_display])
            session_status_btn.click(get_session_status, outputs=[status_display])

            # Auto-refresh on load
            demo.load(system_status, outputs=[status_display])

# Launch the interface
if __name__ == "__main__":
    print(f"🚀 Starting {UI_TITLE}")
    print("🌐 Browser Agent: Ready")
    print("💾 Memory System: Active")
    print("📱 Opening enhanced web interface...")

    demo.launch(
        server_name="0.0.0.0",
        server_port=7790,  # Different port to avoid conflicts
        share=False,
        show_error=True,
        quiet=False
    )
