"""
Browser actions for Playwright automation.

This module provides a collection of browser actions that can be used with
<PERSON><PERSON> for browser automation.
"""
import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
import json
import re

from .browser import PlaywrightBrowser

logger = logging.getLogger(__name__)

class BrowserActions:
    """
    High-level browser actions for Playwright automation.

    This class provides high-level browser actions that can be used by
    AI agents to interact with web pages.
    """

    def __init__(self, browser: PlaywrightBrowser):
        """
        Initialize browser actions.

        Args:
            browser: PlaywrightBrowser instance
        """
        self.browser = browser

    async def navigate_to_url(self, url: str) -> Dict:
        """
        Navigate to a URL.

        Args:
            url: URL to navigate to

        Returns:
            Dictionary with result information
        """
        try:
            success = await self.browser.goto(url)

            result = {
                "success": success,
                "url": await self.browser.get_current_url()
            }

            if success:
                result["title"] = await self.browser._page.title()

            return result
        except Exception as e:
            logger.error(f"Error navigating to {url}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def search_on_page(self, text: str) -> Dict:
        """
        Search for text on the current page.

        Args:
            text: Text to search for

        Returns:
            Dictionary with search results
        """
        try:
            # Get the page content
            content = await self.browser.get_page_text()

            # Check if the text exists on the page
            if text.lower() in content.lower():
                # Find all occurrences of the text
                pattern = re.compile(re.escape(text), re.IGNORECASE)
                matches = pattern.finditer(content)

                # Get the context for each match
                contexts = []
                for match in matches:
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    contexts.append(content[start:end].strip())

                return {
                    "success": True,
                    "found": True,
                    "count": len(contexts),
                    "contexts": contexts
                }
            else:
                return {
                    "success": True,
                    "found": False,
                    "count": 0,
                    "contexts": []
                }
        except Exception as e:
            logger.error(f"Error searching for '{text}' on page: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def click_on(self, selector_or_text: str, by_text: bool = False) -> Dict:
        """
        Click on an element.

        Args:
            selector_or_text: CSS selector or text content
            by_text: Whether to find the element by text content

        Returns:
            Dictionary with result information
        """
        try:
            if by_text:
                # Use a JavaScript function to find an element containing the specified text
                await self.browser._page.evaluate(f"""() => {{
                    const elements = Array.from(document.querySelectorAll('a, button, [role="button"], input[type="submit"], input[type="button"]'));
                    const element = elements.find(el => el.innerText && el.innerText.includes("{selector_or_text}"));
                    if (element) {{
                        element.click();
                    }}
                }}""")

                return {
                    "success": True,
                    "method": "by_text",
                    "text": selector_or_text
                }
            else:
                # Click using the CSS selector
                success = await self.browser.click(selector_or_text)

                return {
                    "success": success,
                    "method": "by_selector",
                    "selector": selector_or_text
                }
        except Exception as e:
            logger.error(f"Error clicking on {selector_or_text}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def fill_form(self, form_data: Dict[str, str]) -> Dict:
        """
        Fill a form with data.

        Args:
            form_data: Dictionary mapping selectors to values

        Returns:
            Dictionary with result information
        """
        try:
            results = {}

            for selector, value in form_data.items():
                success = await self.browser.fill(selector, value)
                results[selector] = success

            return {
                "success": all(results.values()),
                "fields": results
            }
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def extract_links(self, filter_text: Optional[str] = None) -> Dict:
        """
        Extract links from the current page.

        Args:
            filter_text: Optional text to filter links by

        Returns:
            Dictionary with link information
        """
        try:
            # Extract all links from the page
            links = await self.browser._page.evaluate("""() => {
                const links = Array.from(document.querySelectorAll('a[href]'));
                return links.map(link => {
                    return {
                        text: link.innerText.trim(),
                        href: link.href,
                        target: link.target,
                        rel: link.rel
                    };
                });
            }""")

            # Filter links if filter_text is provided
            if filter_text:
                links = [
                    link for link in links
                    if filter_text.lower() in link["text"].lower() or
                       filter_text.lower() in link["href"].lower()
                ]

            return {
                "success": True,
                "count": len(links),
                "links": links
            }
        except Exception as e:
            logger.error(f"Error extracting links: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def extract_tables(self) -> Dict:
        """
        Extract tables from the current page.

        Returns:
            Dictionary with table information
        """
        try:
            # Extract all tables from the page
            tables = await self.browser._page.evaluate("""() => {
                const tables = Array.from(document.querySelectorAll('table'));
                return tables.map((table, tableIndex) => {
                    const rows = Array.from(table.rows);
                    const headers = Array.from(rows[0]?.cells || []).map(cell => cell.innerText.trim());

                    const data = rows.slice(1).map(row => {
                        const cells = Array.from(row.cells);
                        return cells.map(cell => cell.innerText.trim());
                    });

                    return {
                        index: tableIndex,
                        headers: headers,
                        data: data
                    };
                });
            }""")

            return {
                "success": True,
                "count": len(tables),
                "tables": tables
            }
        except Exception as e:
            logger.error(f"Error extracting tables: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def wait_and_click(self, selector: str, timeout: int = 30000) -> Dict:
        """
        Wait for an element to be visible and then click on it.

        Args:
            selector: CSS selector for the element
            timeout: Timeout in milliseconds

        Returns:
            Dictionary with result information
        """
        try:
            # Wait for the element to be visible
            await self.browser.wait_for_selector(selector, timeout=timeout)

            # Click on the element
            success = await self.browser.click(selector)

            return {
                "success": success,
                "selector": selector
            }
        except Exception as e:
            logger.error(f"Error waiting for and clicking on {selector}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def scroll(self, direction: str = "down", pixels: int = 300) -> Dict:
        """
        Scroll the page.

        Args:
            direction: Direction to scroll ("down", "up", "left", "right")
            pixels: Number of pixels to scroll

        Returns:
            Dictionary with result information
        """
        try:
            scroll_x = 0
            scroll_y = 0

            if direction == "down":
                scroll_y = pixels
            elif direction == "up":
                scroll_y = -pixels
            elif direction == "right":
                scroll_x = pixels
            elif direction == "left":
                scroll_x = -pixels

            await self.browser._page.evaluate(f"window.scrollBy({scroll_x}, {scroll_y})")

            return {
                "success": True,
                "direction": direction,
                "pixels": pixels
            }
        except Exception as e:
            logger.error(f"Error scrolling {direction}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def take_screenshot(self, full_page: bool = True) -> Dict:
        """
        Take a screenshot of the current page.

        Args:
            full_page: Whether to take a screenshot of the full page or just the viewport

        Returns:
            Dictionary with screenshot information
        """
        try:
            screenshot_base64 = await self.browser.get_screenshot_base64(full_page=full_page)

            return {
                "success": True,
                "screenshot": screenshot_base64,
                "full_page": full_page
            }
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_current_page_info(self) -> Dict:
        """
        Get comprehensive information about the current page.

        Returns:
            Dictionary with page information
        """
        try:
            # Get page info
            page_info = await self.browser.get_page_info()

            # Get structured data
            structured_data = await self.browser.extract_structured_data()

            return {
                "success": True,
                "url": page_info["url"],
                "title": page_info["title"],
                "structured_data": structured_data
            }
        except Exception as e:
            logger.error(f"Error getting page info: {e}")
            return {
                "success": False,
                "error": str(e)
            }
