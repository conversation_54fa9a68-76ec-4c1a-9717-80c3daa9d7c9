# 编写提示词（指令）的技巧

你在 Midscene 编写的自然语言参数，最终都会变成提示词（Prompt）发送给大语言模型。以下是一些可以帮助提升效果的提示词工程（Prompt Engineering）技巧。

## 目标是获得更稳定的响应

由于 AI 常常会“幻想”，调优的目标是在多次运行中获得模型的稳定响应。大多数情况下，通过使用良好的提示，AI 模型的响应效果可以变得更好。

## 提供更详细的描述并提供样例

提供详细描述和示例一直是非常有用的提示词技巧。

例如：
❌ 错误示例
```log
搜'耳机'
```

✅ 正确示例
```log
找到搜索框（搜索框的上方应该有区域切换按钮，如 '国内'， '国际')，输入'耳机'，敲回车
```

❌ 错误示例
```log
断言：外卖服务正在正常运行
```

✅ 正确示例
```log
断言：界面上有个“外卖服务”的板块，并且标识着“正常”
```

### 在确定交互类型时，使用即时操作接口（Instant Action）

例如：

`agent.ai('点击登录按钮')` 是自动规划模式，Midscene 会规划步骤并执行。它可能会花费更多时间和 token.

使用 `agent.aiTap('登录按钮')` 你可以直接使用 AI 模型定位结果并执行点击操作。它比自动规划模式更快且更准确。

更多细节请参考 [API](./API).

### 理解 `.ai` 交互出错的原因

**理解报告文件**

通过查看 Midscene 的运行报告，你可以看到每个 `.ai` 调用中的两个主要步骤：

1. 规划（Planning）
2. 定位（Locating）

首先，你应该找出 AI 在规划步骤还是定位步骤中出错。

当看到步骤不符预期（多步骤或少步骤），说明 AI 在规划步骤中出错。此时，你可以尝试在任务流中提供更多细节。

例如：

❌ 错误示例
```log
选择 "include" 选项
```

你可以尝试：

✅ 正确示例
```log
点击 "range" 下拉菜单，并选择 "include" 选项
```

当看到定位结果不符预期（元素错误或坐标偏移），说明 AI 在定位步骤中出错。此时，你可以尝试在定位参数中提供更多细节。

例如：

❌ 错误示例
```log
点击 "Add" 按钮
```

你可以尝试：

✅ 正确示例
```log
点击页面右上角的 "Add" 按钮，它是一个带有 "+" 图标的按钮，位于 "range" 下拉菜单的右侧
```

**其他优化方法**

* 使用更大尺寸、更强的 AI 模型
* 使用即时操作接口（Instant Action，如 `agent.aiTap()`）代替 `.ai`


## 一个 Prompt (指令)只做一件事

使用 `.ai` 每次只做一件事。尽管 Midscene 有自动重规划能力，但仍应保持指令简洁。否则，LLM 的输出可能会变得混乱。指令的长度对 token 消耗的影响几乎可以忽略不计。

❌ 错误示例
```log
点击登录按钮，然后点击注册按钮，在表单中输入'<EMAIL>'作为邮箱，'test'作为密码，然后点击注册按钮
```

✅ 正确示例：将任务分解为多个步骤的 `.ai` 调用
```log
"点击登录按钮"
"点击注册按钮"
"在表单中[邮箱]输入'<EMAIL>'"
"在表单中[密码]输入'test'"
"点击注册按钮"
```

### LLM 可能无法准确辨别数值（比如坐标或十六进制颜色值），不妨提供一些选项

例如：

❌ 错误示例
```log
string，文本颜色的十六进制值
```

❌ 错误示例
```log
[number, number]，主按钮的 [x, y] 坐标
```

✅ 正确示例
```log
string，文本的颜色，返回：蓝色 / 红色 / 黄色 / 绿色 / 白色 / 黑色 / 其他
```

### 使用可视化报告和 Playground 进行调试

测试报告里有每个步骤的详细信息。如果你想结合报告里的 UI 状态重新运行 Prompt，你可以启动本地 Playground Server，然后点击“Send to Playground”。

启动本地 Playground Server:
```
npx --yes @midscene/web
```

![Playground](/midescene-playground-entry.jpg)


### 从界面做推断，而不是 DOM 属性或者浏览器状态

所有传递给 LLM 的数据都是截图和元素坐标。DOM和浏览器 对 LLM 来说几乎是不可见的。因此，务必确保你想提取的信息都在截图中有所体现且能被 LLM “看到”。

❌ 错误示例
```log
标题有个 `test-id-size` 属性
```

❌ 错误示例
```log
浏览器有两个 tab 开着
```

❌ 错误示例
```log
异步请求已经结束了
```

✅ 正确示例
```log
标题是蓝色的
```


### 通过断言交叉检查结果

LLM 可能会表现出错误的行为。更好的做法是运行操作后检查其结果。

例如，你可以在插入记录后检查待办应用的列表内容。

```typescript
await ai('在任务框中输入“后天学习 AI”，然后按 Enter 键创建');

// 检查结果
const taskList = await aiQuery<string[]>('string[], 列表中的任务');
expect(taskList.length).toBe(1);
expect(taskList[0]).toBe('后天学习 AI');
```

### 中、英文提示词都是可行的

由于大多数 AI 模型可以理解多种语言，所以请随意用你喜欢的语言撰写提示指令。即使提示语言与页面语言不同，通常也是可行的。
