#!/usr/bin/env python3
"""
Quick System Check and Dashboard Launch
Final verification and launch script
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 AI Agent System - Quick Check & Launch")
    print("=" * 50)

    # Quick file check
    files_to_check = [
        "terminal_connected_dashboard.py",
        "quick_start_dashboard.py",
        "LAUNCH_AI_SYSTEM.bat"
    ]

    print("📁 Checking files...")
    all_files_exist = True
    for file in files_to_check:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            all_files_exist = False

    if all_files_exist:
        print("\n🎉 All core files are ready!")

        print("\n🚀 Launching Terminal Connected Dashboard...")
        print("   The GUI should appear on your screen momentarily...")

        try:
            # Launch the dashboard
            subprocess.Popen([sys.executable, "terminal_connected_dashboard.py"])
            print("✅ Dashboard launched successfully!")

            print("\n🎯 SYSTEM STATUS: FULLY OPERATIONAL")
            print("\nWhat you should see:")
            print("  🖥️  Dashboard GUI window with multiple tabs")
            print("  💻  Terminal Output tab showing live logs")
            print("  📊  System Monitor tab with resource usage")
            print("  ☁️  Cloud Integration tab with GitHub/HuggingFace/Reddit")
            print("  🎮  Component management with start/stop controls")

            print("\n🔧 Next Steps:")
            print("  1. Look for the Dashboard window on your screen")
            print("  2. Click 'Start All Components' to launch services")
            print("  3. Use the Terminal Output tab to see live logs")
            print("  4. Check Cloud Integration tab for external services")
            print("  5. Use component buttons to manage individual services")

            print("\n📋 Available Files:")
            print("  • LAUNCH_AI_SYSTEM.bat - Master launcher")
            print("  • terminal_connected_dashboard.py - Main dashboard")
            print("  • quick_start_dashboard.py - Simple dashboard")
            print("  • comprehensive_launcher.py - Full system launcher")

        except Exception as e:
            print(f"❌ Error launching dashboard: {e}")
            print("   Try running: python terminal_connected_dashboard.py")

    else:
        print("\n⚠️ Some files are missing. Running setup...")
        try:
            subprocess.run([sys.executable, "final_integration_setup.py"], check=True)
            print("✅ Setup completed. Try running this script again.")
        except:
            print("❌ Setup failed. Check your Python environment.")

    print(f"\n📍 Working Directory: {os.getcwd()}")
    print(f"🐍 Python: {sys.executable}")
    print("\n🎉 System Check Complete!")

if __name__ == "__main__":
    main()
