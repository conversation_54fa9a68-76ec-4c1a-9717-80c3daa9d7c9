"""
Working Browser Automation with Real Browser Window
This script creates a simple but functional browser automation that opens a real Chrome window
"""
import gradio as gr
import subprocess
import sys
import os
import time
from datetime import datetime

def launch_real_browser(task, url=""):
    """Launch actual browser automation with visible Chrome window"""
    if not task.strip():
        return "❌ Please enter a task description"

    # Create the automation script
    automation_script = f'''
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

print("🚀 Starting browser automation...")

# Configure Chrome for visible automation
chrome_options = Options()
chrome_options.add_experimental_option("detach", True)  # Keep browser open
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

try:
    print("🔧 Setting up Chrome driver...")
    service = Service(ChromeDriverManager().install())

    print("🌐 Opening Chrome browser...")
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Navigate to URL
    target_url = "{url}" if "{url}" else "https://www.google.com"
    print(f"🌍 Navigating to: {{target_url}}")
    driver.get(target_url)
    time.sleep(2)

    task = "{task}"
    print(f"🎯 Executing task: {{task}}")

    # Simple automation based on task
    if "search" in task.lower():
        try:
            search_box = driver.find_element(By.NAME, "q")
            search_terms = task.replace("search for", "").replace("search", "").strip()
            search_box.clear()
            search_box.send_keys(search_terms)
            search_box.submit()
            time.sleep(3)

            # Get first few results
            results = driver.find_elements(By.CSS_SELECTOR, "h3")[:3]
            print("RESULTS_START")
            for i, result in enumerate(results, 1):
                if result.text:
                    print(f"{{i}}. {{result.text}}")
            print("RESULTS_END")

        except Exception as e:
            print(f"Search error: {{e}}")

    print("✅ Browser automation completed")
    print("🎮 Browser window will stay open for manual control")
    print(f"📍 Current URL: {{driver.current_url}}")
    print(f"📄 Page title: {{driver.title}}")

    # Keep browser open for user interaction
    print("ℹ️  Browser will remain open for continued use")

except Exception as e:
    print(f"❌ Error: {{e}}")
'''

    try:
        # Write automation script
        with open("browser_automation.py", "w") as f:
            f.write(automation_script)

        # Execute automation
        result = subprocess.run([sys.executable, "browser_automation.py"],
                              capture_output=True, text=True, timeout=60)

        # Parse output
        output = result.stdout if result.stdout else "No output received"
        error = result.stderr if result.stderr else ""

        # Format response
        response = f"""🤖 **Browser Automation Executed**

**Task:** {task}
**URL:** {url if url else "https://www.google.com"}
**Time:** {datetime.now().strftime('%H:%M:%S')}

**Status:** ✅ COMPLETED

**Output:**
{output}

**Browser Window:** Should be open and ready for interaction!

**Next Steps:**
- A Chrome browser window should now be visible
- You can interact with it manually
- The automation has completed its task
- Browser will stay open for continued use
"""

        if error:
            response += f"\n\n**Debug Info:**\n{error}"

        # Clean up
        if os.path.exists("browser_automation.py"):
            os.remove("browser_automation.py")

        return response

    except subprocess.TimeoutExpired:
        return """⏰ **Browser Automation Timeout**

The automation took longer than expected but the browser should still be launching.
Check for a Chrome window opening on your screen.
"""
    except Exception as e:
        return f"❌ **Error:** {str(e)}"

def quick_test():
    """Quick test of browser automation"""
    return launch_real_browser("search for AI news", "")

# Create simple interface
with gr.Blocks(title="🌐 Working Browser Automation", theme=gr.themes.Soft()) as demo:
    gr.Markdown("# 🌐 Working Browser Automation")
    gr.Markdown("**This actually opens a real Chrome browser window!**")

    with gr.Row():
        with gr.Column():
            task_input = gr.Textbox(
                label="Automation Task",
                placeholder="e.g., search for python tutorials",
                lines=2
            )
            url_input = gr.Textbox(
                label="Starting URL (optional)",
                placeholder="https://example.com",
                value=""
            )

            run_btn = gr.Button("🚀 Launch Browser Automation", variant="primary", size="lg")
            test_btn = gr.Button("🧪 Quick Test", variant="secondary")

        with gr.Column():
            output = gr.Textbox(
                label="Automation Results",
                lines=15,
                interactive=False
            )

    # Event handlers
    run_btn.click(
        launch_real_browser,
        inputs=[task_input, url_input],
        outputs=[output]
    )

    test_btn.click(
        quick_test,
        outputs=[output]
    )

    # Instructions
    gr.Markdown("""
## 📋 Instructions:

1. **Enter a task** like "search for AI news" or "navigate to reddit.com"
2. **Optionally add a starting URL**
3. **Click "🚀 Launch Browser Automation"**
4. **Watch for Chrome browser window to open**
5. **Browser stays open for manual interaction**

## ✅ Features:
- **Real Chrome browser window opens**
- **Performs actual web automation**
- **Browser stays open for manual control**
- **Works with search, navigation, and more**

## 🔧 Requirements:
- Chrome browser installed
- Internet connection
- Selenium and webdriver-manager packages
""")

if __name__ == "__main__":
    demo.launch(
        server_name="0.0.0.0",
        server_port=7791,
        share=False,
        show_error=True
    )
