name: unified-ai-repository
services:
  - name: midscene-bridge
    project: midscene
    language: python
    host: containerapp
    environmentVariables:
      - OPENAI_BASE_URL
      - OPENAI_API_KEY
      - MIDSCENE_MODEL_NAME
      - MIDSCENE_USE_GEMINI
    dependencies:
      - name: chrome-extension
        type: azurestorageaccount
        connection: http
        environmentVariables:
          - CHROME_EXTENSION_URL
  - name: gemini-integration
    project: gemini
    language: python
    host: containerapp
    environmentVariables:
      - GEMINI_API_KEY
      - GEMINI_MODEL_NAME
  - name: unified-ai-repository
    project: venai
    language: python
    host: containerapp
    environmentVariables:
      - VENAI_ENV
    dependencies:
      - name: midscene-bridge
        type: containerapp
        connection: http
        environmentVariables:
          - MIDSCENE_BRIDGE_URL
      - name: gemini-integration
        type: containerapp
        connection: http
        environmentVariables:
          - GEMINI_API_URL
