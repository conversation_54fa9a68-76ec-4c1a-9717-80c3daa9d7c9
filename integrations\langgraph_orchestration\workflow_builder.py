"""
Workflow building utilities for LangGraph-based agent workflows.
"""
import logging
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass

from langgraph.graph import StateGraph, END
from langgraph.checkpoint import Checkpoint

logger = logging.getLogger(__name__)

@dataclass
class WorkflowNode:
    """Definition of a node in a workflow graph."""
    id: str
    fn: Callable
    description: Optional[str] = None
    agent_id: Optional[str] = None
    requires_tools: bool = False
    tools: Optional[List[str]] = None

    def __post_init__(self):
        """Validate node configuration after initialization."""
        if self.requires_tools and not self.tools:
            logger.warning(f"Node '{self.id}' requires tools but none were specified")

@dataclass
class WorkflowEdge:
    """Definition of an edge in a workflow graph."""
    source: str
    target: Union[str, object]  # Can be a node ID or END
    condition: Optional[Callable] = None

class WorkflowBuilder:
    """
    Builder for creating LangGraph workflows.

    This class provides a high-level interface to create StateGraph
    workflows, making it easier to define nodes, edges, and conditions.
    """

    def __init__(self, name: str, description: Optional[str] = None):
        """
        Initialize a workflow builder.

        Args:
            name: The name of the workflow
            description: Optional description
        """
        self.name = name
        self.description = description
        self.nodes: Dict[str, WorkflowNode] = {}
        self.edges: List[WorkflowEdge] = []
        self.conditional_edges: Dict[str, List[WorkflowEdge]] = {}
        self.entry_point: Optional[str] = None

    def add_node(self, node: WorkflowNode) -> 'WorkflowBuilder':
        """
        Add a node to the workflow.

        Args:
            node: The node to add

        Returns:
            Self for chaining
        """
        if node.id in self.nodes:
            logger.warning(f"Node with ID '{node.id}' already exists. Overwriting.")

        self.nodes[node.id] = node
        return self

    def add_edge(self, source: str, target: Union[str, object]) -> 'WorkflowBuilder':
        """
        Add a simple edge between nodes.

        Args:
            source: Source node ID
            target: Target node ID or END

        Returns:
            Self for chaining
        """
        if source not in self.nodes:
            raise ValueError(f"Source node '{source}' does not exist")

        if target != END and target not in self.nodes:
            raise ValueError(f"Target node '{target}' does not exist")

        self.edges.append(WorkflowEdge(source=source, target=target))
        return self

    def add_conditional_edge(
        self,
        source: str,
        condition_fn: Callable,
        targets: Dict[str, str]
    ) -> 'WorkflowBuilder':
        """
        Add conditional edges from a source node.

        Args:
            source: Source node ID
            condition_fn: Function that determines which edge to take
            targets: Mapping of condition result to target node

        Returns:
            Self for chaining
        """
        if source not in self.nodes:
            raise ValueError(f"Source node '{source}' does not exist")

        for condition, target in targets.items():
            if target != END and target not in self.nodes:
                raise ValueError(f"Target node '{target}' does not exist")

        if source not in self.conditional_edges:
            self.conditional_edges[source] = []

        self.conditional_edges[source].append((condition_fn, targets))
        return self

    def set_entry_point(self, node_id: str) -> 'WorkflowBuilder':
        """
        Set the entry point for the workflow.

        Args:
            node_id: The ID of the node to use as entry point

        Returns:
            Self for chaining
        """
        if node_id not in self.nodes:
            raise ValueError(f"Node '{node_id}' does not exist")

        self.entry_point = node_id
        return self

    def build(self, checkpointer: Optional[Checkpoint] = None) -> StateGraph:
        """
        Build the workflow into a LangGraph StateGraph.

        Args:
            checkpointer: Optional checkpoint saver for persistence

        Returns:
            Compiled StateGraph
        """
        if not self.entry_point:
            raise ValueError("Entry point not set. Call set_entry_point() first.")

        # Create a new graph
        builder = StateGraph()

        # Add all nodes
        for node_id, node in self.nodes.items():
            builder.add_node(node_id, node.fn)

        # Add normal edges
        for edge in self.edges:
            builder.add_edge(edge.source, edge.target)

        # Add conditional edges
        for source, conditions_list in self.conditional_edges.items():
            for condition_fn, targets in conditions_list:
                builder.add_conditional_edges(source, condition_fn, targets)

        # Set entry point
        builder.set_entry_point(self.entry_point)

        # Compile the graph
        return builder.compile(checkpointer=checkpointer)
