"""
Document loading and processing utilities using LangChain.
"""
from typing import List, Dict, Any, Union, Optional
import os
from pathlib import Path

from langchain_community.document_loaders import (
    TextLoader,
    PyPDFLoader,
    CSVLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredExcelLoader
)
from langchain_core.documents import Document
from langchain.text_splitter import (
    RecursiveCharacterTextSplitter,
    CharacterTextSplitter,
    TokenTextSplitter
)
from langchain_community.vectorstores import Chroma, FAISS
from langchain_core.embeddings import Embeddings
from langchain_openai import OpenAIEmbeddings


def load_documents(
    source_path: Union[str, Path],
    file_extension: Optional[str] = None
) -> List[Document]:
    """
    Load documents from various file formats.

    Args:
        source_path: Path to file or directory to load
        file_extension: Override file extension detection

    Returns:
        List of loaded documents
    """
    source_path = Path(source_path)
    if not source_path.exists():
        raise FileNotFoundError(f"Path does not exist: {source_path}")

    # If directory, process all files
    if source_path.is_dir():
        documents = []
        for file_path in source_path.glob("**/*"):
            if file_path.is_file():
                try:
                    docs = _load_single_file(file_path)
                    documents.extend(docs)
                except Exception as e:
                    print(f"Error loading {file_path}: {str(e)}")
        return documents

    # Process single file
    return _load_single_file(source_path, file_extension)


def _load_single_file(file_path: Path, file_extension: Optional[str] = None) -> List[Document]:
    """Load a single file based on its extension."""
    ext = file_extension.lower() if file_extension else file_path.suffix.lower()

    if ext in ['.txt', '.log', '.json', '.py', '.js', '.html', '.css']:
        return TextLoader(str(file_path)).load()
    elif ext == '.pdf':
        return PyPDFLoader(str(file_path)).load()
    elif ext in ['.csv', '.tsv']:
        return CSVLoader(str(file_path)).load()
    elif ext in ['.md', '.markdown']:
        return UnstructuredMarkdownLoader(str(file_path)).load()
    elif ext in ['.doc', '.docx']:
        return UnstructuredWordDocumentLoader(str(file_path)).load()
    elif ext in ['.xls', '.xlsx']:
        return UnstructuredExcelLoader(str(file_path)).load()
    elif ext == '.html':
        return UnstructuredHTMLLoader(str(file_path)).load()
    else:
        # Default to text loader for unknown types
        return TextLoader(str(file_path)).load()


def process_documents(
    documents: List[Document],
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    use_tokens: bool = False,
    embeddings: Optional[Embeddings] = None,
    vector_store_type: str = "chroma",
    persist_directory: Optional[str] = None
):
    """
    Process documents by chunking and optionally storing in a vector database.

    Args:
        documents: List of documents to process
        chunk_size: Size of document chunks
        chunk_overlap: Overlap between chunks
        use_tokens: Whether to split by tokens instead of characters
        embeddings: Embeddings to use (defaults to OpenAI)
        vector_store_type: Type of vector store ('chroma' or 'faiss')
        persist_directory: Directory to persist vector store (if any)

    Returns:
        Vector store if embeddings provided, otherwise list of chunked documents
    """
    # Create text splitter
    if use_tokens:
        text_splitter = TokenTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    else:
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )

    # Split documents
    split_docs = text_splitter.split_documents(documents)

    # If no embeddings, just return the split documents
    if embeddings is None:
        return split_docs

    # Initialize embeddings if not provided
    if embeddings is None:
        embeddings = OpenAIEmbeddings()

    # Create vector store
    if vector_store_type.lower() == "faiss":
        vector_store = FAISS.from_documents(split_docs, embeddings)
        if persist_directory:
            vector_store.save_local(persist_directory)
    else:  # Default to Chroma
        if persist_directory:
            vector_store = Chroma.from_documents(
                split_docs,
                embeddings,
                persist_directory=persist_directory
            )
            vector_store.persist()
        else:
            vector_store = Chroma.from_documents(split_docs, embeddings)

    return vector_store
