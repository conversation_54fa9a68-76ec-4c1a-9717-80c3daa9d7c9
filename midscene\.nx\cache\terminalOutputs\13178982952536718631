
> @midscene/report@0.13.1 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\apps\report
> rsbuild build

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;181;251;237mR[39m[38;2;173;246;230ms[39m[38;2;164;242;224mb[39m[38;2;156;238;218mu[39m[38;2;148;233;211mi[39m[38;2;140;229;205ml[39m[38;2;131;224;198md[39m[38;2;131;224;198m [39m[38;2;123;220;192mv[39m[38;2;115;216;186m1[39m[38;2;107;211;179m.[39m[38;2;99;207;173m3[39m[38;2;90;203;167m.[39m[38;2;82;198;160m2[39m[38;2;74;194;154m2[39m[38;2;74;194;154m
[39m[22m
[1m[32mready  [39m[22m built in [1m25.5[22m s

[34mFile (web)        Size        Gzip   [39m
[2mdist\[22m[32mindex.html[39m   7639.6 kB   [31m2094.1 kB[39m

Template injected into C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\core\dist\es\chunk-2OLUMFSC.js
Template injected into C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\core\dist\lib\chunk-2OLUMFSC.js
