"""
<PERSON>rip<PERSON> to start the FastAPI dashboard with Cloudflare tunneling
"""
import os
import asyncio
import subprocess
import sys
import signal
import time
from pathlib import Path
import config
from core.logger import setup_logger

# Set up logging
logger = setup_logger("cloudflare_tunnel")

# Tunnel configuration
TUNNEL_NAME = "ai-agent-dashboard"
APP_PORT = int(os.getenv("WEB_PORT", "8000"))
APP_HOST = os.getenv("WEB_HOST", "127.0.0.1")

async def create_and_run_tunnel():
    """Create and run a Cloudflare tunnel to expose the FastAPI dashboard"""

    logger.info("Setting up Cloudflare tunnel...")
      # Check if cloudflared is available in the path or current directory
    cloudflared_cmd = "cloudflared"
    where_result = subprocess.run(["where", "cloudflared"], capture_output=True, text=True)

    if where_result.returncode != 0:
        # Check for cloudflared in the current directory
        current_dir_cloudflared = str(Path(__file__).resolve().parent / "cloudflared.exe")
        if os.path.exists(current_dir_cloudflared):
            cloudflared_cmd = current_dir_cloudflared
        else:
            # Use the downloaded MSI executable directly
            cloudflared_cmd = str(Path(__file__).resolve().parent / "cloudflared.exe")
            if not os.path.exists(cloudflared_cmd):
                logger.error("cloudflared not found. Please ensure cloudflared.exe is in the current directory or install it first.")
                sys.exit(1)

    # Check if tunnel already exists
    logger.info(f"Checking if tunnel '{TUNNEL_NAME}' already exists...")
    tunnel_check = subprocess.run(
        [cloudflared_cmd, "tunnel", "list"],
        capture_output=True,
        text=True
    )

    if TUNNEL_NAME in tunnel_check.stdout:
        logger.info(f"Tunnel '{TUNNEL_NAME}' already exists. Using existing tunnel.")
    else:
        # Create a new tunnel
        logger.info(f"Creating new tunnel '{TUNNEL_NAME}'...")
        create_result = subprocess.run(
            [cloudflared_cmd, "tunnel", "create", TUNNEL_NAME],
            capture_output=True,
            text=True
        )

        if create_result.returncode != 0:
            logger.error(f"Failed to create tunnel: {create_result.stderr}")
            sys.exit(1)

        logger.info("Tunnel created successfully!")

        # Get tunnel ID from the output
        import re
        tunnel_id_match = re.search(r"Created tunnel ([\w-]+)", create_result.stdout)
        if tunnel_id_match:
            tunnel_id = tunnel_id_match.group(1)
            logger.info(f"Tunnel ID: {tunnel_id}")
        else:
            logger.warning("Could not extract tunnel ID from output")

    # Run tunnel to route traffic to the local FastAPI server
    logger.info(f"Starting tunnel to route traffic to {APP_HOST}:{APP_PORT}...")

    # Start the tunnel process
    tunnel_process = subprocess.Popen(
        [
            cloudflared_cmd, "tunnel", "run", "--url", f"http://{APP_HOST}:{APP_PORT}", TUNNEL_NAME
        ],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    # Wait a moment and check if the process is still running
    time.sleep(2)
    if tunnel_process.poll() is not None:
        stderr_output = tunnel_process.stderr.read()
        logger.error(f"Tunnel process exited unexpectedly: {stderr_output}")
        sys.exit(1)

    # Get the tunnel URL
    time.sleep(3)  # Give the tunnel a moment to establish

    logger.info("Tunnel is running! Getting tunnel details...")
    tunnel_info = subprocess.run(
        [cloudflared_cmd, "tunnel", "info", TUNNEL_NAME],
        capture_output=True,
        text=True
    )

    logger.info(f"Tunnel info: {tunnel_info.stdout}")

    # Pull out public URL from the tunnel info if possible
    import re
    url_match = re.search(r"https://[^\s]+", tunnel_info.stdout)
    if url_match:
        public_url = url_match.group(0)
        logger.info(f"\n\nYour dashboard is available at: {public_url}\n\n")
    else:
        # Get hostname for the tunnel
        hostname_info = subprocess.run(
            [cloudflared_cmd, "tunnel", "route", "dns", TUNNEL_NAME, f"{TUNNEL_NAME}.trycloudflare.com"],
            capture_output=True,
            text=True
        )
        logger.info(f"Your dashboard should be available at: https://{TUNNEL_NAME}.trycloudflare.com")

    # Keep the tunnel running until interrupted
    try:
        while True:
            if tunnel_process.poll() is not None:
                stderr_output = tunnel_process.stderr.read()
                logger.error(f"Tunnel process exited: {stderr_output}")
                break
            await asyncio.sleep(5)
    except KeyboardInterrupt:
        logger.info("Shutting down tunnel...")
        tunnel_process.send_signal(signal.SIGINT)
        tunnel_process.wait()

if __name__ == "__main__":
    asyncio.run(create_and_run_tunnel())
