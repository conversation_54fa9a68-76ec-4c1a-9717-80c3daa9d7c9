# Additional requirements for integration components
# This file adds packages needed for multi-agent integrations

# Abacus DeepAgent integration
requests>=2.31.0
aiohttp>=3.8.6
websockets>=12.0
asyncio>=3.4.3
abacusai>=0.9.8  # Abacus Deep Agent client

# LangChain and components
langchain>=0.1.0
langchain-community>=0.0.10
langchain-core>=0.1.10
langchain-text-splitters>=0.0.1
langchain-openai>=0.0.2
langchain-anthropic>=0.0.1
langchain-google-genai>=0.0.1
langgraph>=0.0.20

# Embeddings and vector stores
faiss-cpu>=1.7.4
chromadb>=0.4.18
sentence-transformers>=2.2.2

# Playwright for browser automation
playwright>=1.39.0
playwright-stealth>=1.0.5

# Document processing
pypdf>=3.17.1
unstructured>=0.10.30
unstructured-inference>=0.7.12
markdown>=3.5.1
tabulate>=0.9.0
pdf2image>=1.16.3
pytesseract>=0.3.10

# API and dashboard
fastapi>=0.104.1
uvicorn>=0.24.0
jinja2>=3.1.2
aiofiles>=23.2.1
python-multipart>=0.0.6
