
> chrome-extension@0.12.4 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\apps\chrome-extension
> rsbuild build && npm run pack-extension

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;181;251;237mR[39m[38;2;173;246;230ms[39m[38;2;164;242;224mb[39m[38;2;156;238;218mu[39m[38;2;148;233;211mi[39m[38;2;140;229;205ml[39m[38;2;131;224;198md[39m[38;2;131;224;198m [39m[38;2;123;220;192mv[39m[38;2;115;216;186m1[39m[38;2;107;211;179m.[39m[38;2;99;207;173m3[39m[38;2;90;203;167m.[39m[38;2;82;198;160m2[39m[38;2;74;194;154m2[39m[38;2;74;194;154m
[39m[22m
[1m[32mready  [39m[22m built in [1m21.0[22m s[90m (web)[39m
[1m[32mready  [39m[22m built in [1m19.9[22m s[90m (iife)[39m

[34mFile (iife)                                                        Size        Gzip   [39m
[2mdist\[22m[35mmanifest.json[39m                                                 0.63 kB     [32m0.34 kB[39m
[2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.png[39m       4.7 kB   
[2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.png[39m     4.9 kB   
[2mdist\scripts\[22m[36mrecorder-iife.js[39m                                      5.6 kB      [32m1.9 kB[39m
[2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.png[39m     5.8 kB   
[2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.png[39m       5.8 kB   
[2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.png[39m     6.8 kB   
[2mdist\fonts\open-sans\[22m[35mApache License.txt[39m                            11.5 kB     [32m3.9 kB[39m
[2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.png[39m     12.2 kB  
[2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.fnt[39m     14.1 kB  
[2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.png[39m     14.8 kB  
[2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.fnt[39m     15.4 kB  
[2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.fnt[39m     17.1 kB  
[2mdist\scripts\[22m[36mhtmlElementDebug.js[39m                                   19.3 kB     [32m7.1 kB[39m
[2mdist\scripts\[22m[36mhtmlElement.js[39m                                        22.7 kB     [32m8.4 kB[39m
[2mdist\[22m[35micon128.png[39m                                                   25.9 kB  
[2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.fnt[39m       26.7 kB  
[2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.fnt[39m       26.7 kB  
[2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.fnt[39m     27.1 kB  
[2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.fnt[39m     27.1 kB  
[2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.fnt[39m     27.5 kB  
[2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.fnt[39m     27.5 kB  
[2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.fnt[39m     27.8 kB  
[2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.fnt[39m     27.8 kB  
[2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.fnt[39m   28.1 kB  
[2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.fnt[39m   28.1 kB  
[2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.png[39m     29.5 kB  
[2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.png[39m     36.0 kB  
[2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.png[39m     72.0 kB  
[2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.png[39m     85.7 kB  
[2mdist\scripts\[22m[36mworker.js[39m                                             153.1 kB    [32m48.7 kB[39m
[2mdist\scripts\[22m[36mwater-flow.js[39m                                         155.8 kB    [32m49.0 kB[39m
[2mdist\scripts\[22m[36mstop-water-flow.js[39m                                    157.9 kB    [32m48.2 kB[39m
[2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.png[39m   164.0 kB 
[2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.png[39m   185.8 kB 
[2mdist\scripts\[22m[36mevent-recorder-bridge.js[39m                              768.6 kB    [33m240.4 kB[39m

                                                          [35mTotal:[39m   2270.3 kB   [32m1383.0 kB[39m


[34mFile (web)                                                         Size         Gzip   [39m
[2mdist\[22m[32mpopup.html[39m                                                    0.49 kB      [32m0.26 kB[39m
[2mdist\[22m[32mindex.html[39m                                                    0.55 kB      [32m0.27 kB[39m
[2mdist\[22m[35mmanifest.json[39m                                                 0.63 kB      [32m0.34 kB[39m
[2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.png[39m       4.7 kB    
[2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.png[39m     4.9 kB    
[2mdist\static\js\async\[22m[36m450.41f8b458.js[39m                               5.1 kB       [32m1.5 kB[39m
[2mdist\scripts\[22m[36mrecorder-iife.js[39m                                      5.6 kB       [32m1.9 kB[39m
[2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.png[39m     5.8 kB    
[2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.png[39m       5.8 kB    
[2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.png[39m     6.8 kB    
[2mdist\fonts\open-sans\[22m[35mApache License.txt[39m                            11.5 kB      [32m3.9 kB[39m
[2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.png[39m     12.2 kB   
[2mdist\fonts\open-sans\open-sans-10-black\[22m[35mopen-sans-10-black.fnt[39m     14.1 kB   
[2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.png[39m     14.8 kB   
[2mdist\fonts\open-sans\open-sans-12-black\[22m[35mopen-sans-12-black.fnt[39m     15.4 kB   
[2mdist\fonts\open-sans\open-sans-14-black\[22m[35mopen-sans-14-black.fnt[39m     17.1 kB   
[2mdist\scripts\[22m[36mhtmlElementDebug.js[39m                                   19.3 kB      [32m7.1 kB[39m
[2mdist\scripts\[22m[36mhtmlElement.js[39m                                        22.7 kB      [32m8.4 kB[39m
[2mdist\[22m[35micon128.png[39m                                                   25.9 kB   
[2mdist\fonts\open-sans\open-sans-8-black\[22m[35mopen-sans-8-black.fnt[39m       26.7 kB   
[2mdist\fonts\open-sans\open-sans-8-white\[22m[35mopen-sans-8-white.fnt[39m       26.7 kB   
[2mdist\fonts\open-sans\open-sans-16-black\[22m[35mopen-sans-16-black.fnt[39m     27.1 kB   
[2mdist\fonts\open-sans\open-sans-16-white\[22m[35mopen-sans-16-white.fnt[39m     27.1 kB   
[2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.fnt[39m     27.5 kB   
[2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.fnt[39m     27.5 kB   
[2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.fnt[39m     27.8 kB   
[2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.fnt[39m     27.8 kB   
[2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.fnt[39m   28.1 kB   
[2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.fnt[39m   28.1 kB   
[2mdist\fonts\open-sans\open-sans-32-black\[22m[35mopen-sans-32-black.png[39m     29.5 kB   
[2mdist\fonts\open-sans\open-sans-32-white\[22m[35mopen-sans-32-white.png[39m     36.0 kB   
[2mdist\fonts\open-sans\open-sans-64-black\[22m[35mopen-sans-64-black.png[39m     72.0 kB   
[2mdist\fonts\open-sans\open-sans-64-white\[22m[35mopen-sans-64-white.png[39m     85.7 kB   
[2mdist\static\js\[22m[36mpopup.b540a4fc.js[39m                                   87.4 kB      [32m26.5 kB[39m
[2mdist\static\js\[22m[36mlib-react.260f2feb.js[39m                               140.0 kB     [32m45.1 kB[39m
[2mdist\static\js\[22m[36mlib-polyfill.793bb00e.js[39m                            149.2 kB     [32m47.0 kB[39m
[2mdist\fonts\open-sans\open-sans-128-black\[22m[35mopen-sans-128-black.png[39m   164.0 kB  
[2mdist\static\js\[22m[36m244.9765e3f0.js[39m                                     175.5 kB     [32m49.5 kB[39m
[2mdist\fonts\open-sans\open-sans-128-white\[22m[35mopen-sans-128-white.png[39m   185.8 kB  
[2mdist\static\js\async\[22m[36m18.18abdd20.js[39m                                274.5 kB     [32m66.2 kB[39m
[2mdist\static\js\async\[22m[36m251.67360a89.js[39m                               584.6 kB     [33m180.9 kB[39m
[2mdist\static\js\[22m[36m268.d2da751f.js[39m                                     1570.4 kB    [31m398.7 kB[39m
[2mdist\static\js\[22m[36m470.a4a59682.js[39m                                     2284.7 kB    [31m624.7 kB[39m
[2mdist\static\js\[22m[36mindex.0f0073b4.js[39m                                   4691.6 kB    [31m1270.9 kB[39m

                                                          [35mTotal:[39m   10998.8 kB   [32m3708.1 kB[39m


> chrome-extension@0.12.4 pack-extension
> node scripts/pack-extension.js

Extension packed successfully: midscene-extension-v0.12.4.zip (12051360 total bytes saved in extension directory)
