{"selectedAuthType": "oauth-personal", "theme": "<PERSON><PERSON><PERSON>", "mcpServers": {"midscene-mcp": {"command": "node", "args": ["dist/index.js"], "cwd": "C:\\Users\\<USER>\\Documents\\augment-projects\\Ai Agent System\\midscene\\packages\\mcp", "timeout": 30000, "trust": true, "env": {"NODE_ENV": "production"}}, "mpc-standard-server": {"url": "http://localhost:8765/mcp", "timeout": 15000, "trust": true}, "mpc-simple-server": {"url": "http://localhost:8766/mcp", "timeout": 15000, "trust": true}, "mpc-advanced-server": {"url": "http://localhost:8767/mcp", "timeout": 15000, "trust": true}, "mpc-secure-server": {"url": "https://localhost:8768/mcp", "timeout": 15000, "trust": true}}}