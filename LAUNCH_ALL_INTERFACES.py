"""
Master Launcher for All AI Agent Interfaces
Launches all working interfaces with real browser automation
"""
import subprocess
import sys
import time
import webbrowser
from datetime import datetime

def launch_interface(script_name, port, name, delay=2):
    """Launch an interface script"""
    print(f"🚀 Starting {name}...")
    try:
        process = subprocess.Popen([sys.executable, script_name])
        time.sleep(delay)
        print(f"✅ {name} launched on port {port}")
        return process
    except Exception as e:
        print(f"❌ Failed to launch {name}: {e}")
        return None

def main():
    print("🎉 AI AGENT SYSTEM - MASTER LAUNCHER")
    print("=" * 50)
    print(f"🕒 Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    processes = []

    # Launch all interfaces
    interfaces = [
        ("working_browser_automation.py", 7791, "🌐 Working Browser Automation (REAL BROWSER)", 3),
        ("enhanced_web_interface.py", 7790, "💬 Enhanced AI Agent Interface", 3),
        ("simple_web_ui.py", 7789, "🤖 Original Web UI", 2),
    ]

    for script, port, name, delay in interfaces:
        process = launch_interface(script, port, name, delay)
        if process:
            processes.append((process, name, port))

    print("\n🎯 ALL INTERFACES LAUNCHED!")
    print("=" * 50)

    # Display access URLs
    print("\n📱 ACCESS YOUR AI AGENT SYSTEM:")
    print()
    print("🌐 WORKING BROWSER AUTOMATION:")
    print("   http://localhost:7791")
    print("   ✅ Opens REAL Chrome browser windows")
    print("   ✅ Persistent browser sessions")
    print("   ✅ Manual control takeover")
    print()

    print("💬 ENHANCED AI INTERFACE:")
    print("   http://localhost:7790")
    print("   ✅ Full conversation memory")
    print("   ✅ Advanced browser automation")
    print("   ✅ Session management")
    print()

    print("🤖 ORIGINAL WEB UI:")
    print("   http://localhost:7789")
    print("   ✅ Basic browser automation")
    print("   ✅ LLM integration")
    print("   ✅ System status")
    print()

    print("🔗 SUPPORTING SERVICES:")
    print("   🤖 LLM Server: http://localhost:8000")
    print("   🔍 WebRover Backend: http://localhost:8001")
    print("   🎮 WebRover Frontend: http://localhost:3000")
    print()

    # Auto-open browser to main interface
    try:
        print("🌐 Opening browser to Working Browser Automation...")
        webbrowser.open("http://localhost:7791")
        time.sleep(2)
    except:
        pass

    print("🎮 READY TO USE!")
    print("=" * 50)
    print()
    print("📋 QUICK TESTS:")
    print("1. Go to http://localhost:7791")
    print("2. Enter task: 'search for AI news'")
    print("3. Click '🚀 Launch Browser Automation'")
    print("4. Watch Chrome browser window open!")
    print()

    print("💡 FEATURES TO TRY:")
    print("✅ Real browser automation with visible Chrome windows")
    print("✅ Chat with AI while automation runs")
    print("✅ Pause/resume automation for manual control")
    print("✅ Handle logins and authorizations manually")
    print("✅ Full conversation memory across sessions")
    print("✅ Multi-tab browser management")
    print()

    # Keep running and monitor processes
    try:
        print("🔄 Monitoring interfaces... (Press Ctrl+C to stop all)")
        while True:
            time.sleep(10)
            # Check if processes are still running
            running_count = 0
            for process, name, port in processes:
                if process.poll() is None:
                    running_count += 1

            if running_count == 0:
                print("⚠️  All interfaces stopped")
                break

    except KeyboardInterrupt:
        print("\n🛑 Stopping all interfaces...")
        for process, name, port in processes:
            try:
                process.terminate()
                print(f"✅ Stopped {name}")
            except:
                pass
        print("🔚 All interfaces stopped")

if __name__ == "__main__":
    main()
