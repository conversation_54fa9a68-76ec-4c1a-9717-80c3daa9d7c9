{"compilerOptions": {"baseUrl": ".", "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["DOM", "ESNext"], "moduleResolution": "node", "paths": {"@/*": ["./src/*"]}, "target": "es6", "resolveJsonModule": true, "rootDir": "./src", "skipLibCheck": true, "strict": true, "module": "ESNext"}, "exclude": ["node_modules"], "include": ["src", "./vitest.config"]}