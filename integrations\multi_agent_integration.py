"""
Multi-agent system integration module.

This module integrates LangGraph, Lang<PERSON>hain, Abacus DeepAgent,
Playwright, and WebRover into a unified multi-agent system.
"""
import os
import logging
from typing import Dict, List, Any, Optional, Type, Union

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect

# Import LangGraph components
from langgraph.graph import StateGraph

# Import system components
from core.state_manager import StateManager
from core.agent_manager import AgentManager
from core.security import get_current_user

# Import integrations
from integrations.abacus_deep_agent import AbacusDeepAgentClient, AbacusDeepAgent
from integrations.langchain_utils import register_langchain_tools
from integrations.playwright_automation import PlaywrightBrowser, BrowserActions

logger = logging.getLogger(__name__)

class MultiAgentIntegration:
    """
    Integration manager for multi-agent capabilities.

    This class provides a unified interface to integrate LangGraph, LangChain,
    Abacus DeepAgent, Playwright, and WebRover into the multi-agent system.
    """

    def __init__(
        self,
        state_manager: <PERSON><PERSON><PERSON>ger,
        agent_manager: <PERSON><PERSON><PERSON><PERSON>,
        abacus_api_key: Optional[str] = None,
        abacus_base_url: Optional[str] = None
    ):
        """
        Initialize the multi-agent integration manager.

        Args:
            state_manager: System state manager
            agent_manager: Agent management system
            abacus_api_key: API key for Abacus DeepAgent (optional)
            abacus_base_url: Base URL for Abacus DeepAgent API (optional)
        """
        self.state_manager = state_manager
        self.agent_manager = agent_manager

        # Initialize browser automation
        self.playwright_browser = PlaywrightBrowser()
        self.browser_actions = BrowserActions(self.playwright_browser)

        # Initialize Abacus DeepAgent
        self.abacus_client = AbacusDeepAgentClient(
            api_key=abacus_api_key or os.environ.get("ABACUS_API_KEY"),
            base_url=abacus_base_url or os.environ.get("ABACUS_BASE_URL")
        )

        # Initialize LangChain tools registry
        self.tools_registry = self._initialize_tools_registry()

    def _initialize_tools_registry(self):
        """Initialize and register tools for the system."""
        from integrations.langchain_utils.tools import ToolRegistry

        registry = ToolRegistry()

        # Register browser automation tools
        browser_tools = [
            {
                "name": "navigate_to_url",
                "func": self.browser_actions.navigate_to_url,
                "description": "Navigate the browser to a specific URL"
            },
            {
                "name": "click_element",
                "func": self.browser_actions.click_element,
                "description": "Click on an element in the current page using a CSS selector"
            },
            {
                "name": "fill_form_field",
                "func": self.browser_actions.fill_form_field,
                "description": "Fill in a form field with text using a CSS selector"
            },
            {
                "name": "take_screenshot",
                "func": self.browser_actions.take_screenshot,
                "description": "Take a screenshot of the current page"
            }
        ]

        # Register the browser tools
        browser_tool_objects = register_langchain_tools(browser_tools)
        for tool in browser_tool_objects:
            registry.register_tool(tool, categories=["browser-automation"])

        return registry

    async def create_abacus_agent(
        self,
        agent_name: str,
        agent_config: Dict[str, Any],
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> AbacusDeepAgent:
        """
        Create a new Abacus DeepAgent with specified configuration.

        Args:
            agent_name: Name of the agent
            agent_config: Configuration for the agent
            tools: Optional list of tools to provide to the agent

        Returns:
            An initialized DeepAgent instance
        """
        # Create the agent on the Abacus platform
        agent_id = await self.abacus_client.create_agent(
            agent_name=agent_name,
            agent_config=agent_config
        )

        # Initialize the local agent representation
        agent = AbacusDeepAgent(
            agent_id=agent_id,
            client=self.abacus_client,
            name=agent_name,
            config=agent_config
        )

        # Register provided tools if any
        if tools:
            await agent.register_tools(tools)

        # Register the agent with the agent manager
        self.agent_manager.register_agent(agent)

        return agent

    async def initialize_browser(self, headless: bool = False):
        """
        Initialize the browser for automation.

        Args:
            headless: Whether to run the browser in headless mode
        """
        await self.playwright_browser.initialize(headless=headless)

    async def shutdown_browser(self):
        """Shutdown the browser."""
        await self.playwright_browser.shutdown()

    def create_api_router(self) -> APIRouter:
        """
        Create a FastAPI router with integration endpoints.

        Returns:
            APIRouter with integration endpoints
        """
        router = APIRouter(prefix="/integrations", tags=["integrations"])

        # Add endpoints for DeepAgent
        @router.post("/deep-agent/create")
        async def create_deep_agent(
            agent_name: str,
            agent_config: Dict[str, Any],
            tools: Optional[List[Dict[str, Any]]] = None,
            user=Depends(get_current_user)
        ):
            """Create a new DeepAgent."""
            try:
                agent = await self.create_abacus_agent(agent_name, agent_config, tools)
                return {"status": "success", "agent_id": agent.agent_id}
            except Exception as e:
                logger.exception(f"Error creating DeepAgent: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Add endpoints for browser automation
        @router.post("/browser/navigate")
        async def navigate_browser(
            url: str,
            user=Depends(get_current_user)
        ):
            """Navigate the browser to a URL."""
            try:
                await self.browser_actions.navigate_to_url(url)
                screenshot = await self.browser_actions.take_screenshot()
                return {"status": "success", "screenshot": screenshot}
            except Exception as e:
                logger.exception(f"Navigation error: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Add WebSocket for real-time browser control
        @router.websocket("/browser/control")
        async def browser_control(websocket: WebSocket):
            """WebSocket endpoint for real-time browser control."""
            await websocket.accept()
            try:
                while True:
                    data = await websocket.receive_json()
                    command = data.get("command")
                    params = data.get("params", {})

                    if command == "navigate":
                        await self.browser_actions.navigate_to_url(params.get("url"))
                    elif command == "click":
                        await self.browser_actions.click_element(params.get("selector"))
                    elif command == "fill":
                        await self.browser_actions.fill_form_field(
                            params.get("selector"),
                            params.get("text")
                        )
                    elif command == "screenshot":
                        screenshot = await self.browser_actions.take_screenshot()
                        await websocket.send_json({"type": "screenshot", "data": screenshot})

                    # Return current page info
                    page_info = await self.playwright_browser.get_page_info()
                    await websocket.send_json({"type": "page_info", "data": page_info})
            except WebSocketDisconnect:
                logger.info("WebSocket disconnected")

        return router
