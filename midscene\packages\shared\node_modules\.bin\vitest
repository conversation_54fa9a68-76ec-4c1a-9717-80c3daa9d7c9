#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/vitest@3.0.5_@types+debug@4.1.12_@types+node@18.19.62_jsdom@26.1.0_less@4.3.0_sass-embedded@1.86.3_terser@5.39.0/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
