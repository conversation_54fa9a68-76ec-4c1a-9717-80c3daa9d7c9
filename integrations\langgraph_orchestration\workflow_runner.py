"""
Workflow execution for LangGraph workflows.

This module provides functionality to run and monitor LangGraph workflows.
"""
import asyncio
from typing import Dict, Any, List, Optional, Union, Callable, Type
import uuid
import json
from datetime import datetime
from pathlib import Path

from langchain.schema.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig, RunnableLambda, Runnable
from langgraph.graph import StateGraph
import langgraph.checkpoint as checkpoint

from integrations.langgraph_orchestration.state_management import StateManagement
from core.logger import setup_logger

# Set up logger
logger = setup_logger("langgraph.workflow_runner")

class WorkflowRunner:
    """
    Runner for executing LangGraph workflows with state management.

    This class provides functionality for:
    - Running workflows with proper state management
    - Monitoring workflow execution
    - Handling workflow events (completion, error, etc.)
    - Interactive debugging and inspection
    """

    def __init__(self, state_management: Optional[StateManagement] = None):
        """
        Initialize the workflow runner.

        Args:
            state_management: State management system for workflows
        """
        self.state_management = state_management or StateManagement()

        # Active workflow runs
        self.active_runs: Dict[str, Dict[str, Any]] = {}

        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {
            "workflow_start": [],
            "workflow_step": [],
            "workflow_complete": [],
            "workflow_error": []
        }

    async def run_workflow(
        self,
        workflow: StateGraph,
        workflow_id: Optional[str] = None,
        inputs: Optional[Dict[str, Any]] = None,
        checkpoint_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Run a workflow with the given inputs.

        Args:
            workflow: StateGraph workflow to run
            workflow_id: Optional ID for the workflow (generated if not provided)
            inputs: Inputs to the workflow
            checkpoint_id: Optional checkpoint ID to resume from

        Returns:
            Final workflow state
        """
        # Generate workflow ID if not provided
        workflow_id = workflow_id or f"workflow_{uuid.uuid4()}"

        try:
            # Set up initial state
            initial_state = {}

            # Load from checkpoint if provided
            if checkpoint_id:
                initial_state = await self.state_management.load_checkpoint(checkpoint_id)

            # Create runnable config with checkpointing
            config = self.state_management.create_config(workflow_id)

            # Register the run
            run_info = {
                "workflow_id": workflow_id,
                "start_time": datetime.now().isoformat(),
                "status": "running",
                "steps": 0
            }
            self.active_runs[workflow_id] = run_info

            # Trigger workflow_start event
            await self._trigger_event("workflow_start", workflow_id=workflow_id, inputs=inputs)

            # Compile the workflow
            compiled = workflow.compile()

            # Execute the workflow
            state = inputs or {}
            result = await compiled.ainvoke(state, config=config)

            # Update run info
            run_info["status"] = "completed"
            run_info["end_time"] = datetime.now().isoformat()
            run_info["result"] = result

            # Persist final state
            await self.state_management.persist_state(workflow_id, result)

            # Trigger workflow_complete event
            await self._trigger_event("workflow_complete", workflow_id=workflow_id, result=result)

            return result

        except Exception as e:
            logger.error(f"Error running workflow {workflow_id}: {str(e)}")

            # Update run info
            if workflow_id in self.active_runs:
                self.active_runs[workflow_id]["status"] = "error"
                self.active_runs[workflow_id]["end_time"] = datetime.now().isoformat()
                self.active_runs[workflow_id]["error"] = str(e)

            # Trigger workflow_error event
            await self._trigger_event("workflow_error", workflow_id=workflow_id, error=str(e))

            # Re-raise the exception
            raise

    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Workflow status information or None if not found
        """
        return self.active_runs.get(workflow_id)

    async def list_active_workflows(self) -> List[Dict[str, Any]]:
        """
        List all active workflows.

        Returns:
            List of active workflow information
        """
        return [{**info, "workflow_id": wf_id} for wf_id, info in self.active_runs.items()]

    async def cancel_workflow(self, workflow_id: str) -> bool:
        """
        Cancel a running workflow.

        Args:
            workflow_id: ID of the workflow to cancel

        Returns:
            True if canceled successfully, False otherwise
        """
        if workflow_id not in self.active_runs:
            logger.warning(f"Workflow {workflow_id} not found, cannot cancel")
            return False

        run_info = self.active_runs[workflow_id]

        if run_info["status"] not in ["running", "paused"]:
            logger.warning(f"Workflow {workflow_id} is not running or paused, cannot cancel")
            return False

        # Update run info
        run_info["status"] = "cancelled"
        run_info["end_time"] = datetime.now().isoformat()

        # Trigger event
        await self._trigger_event("workflow_error", workflow_id=workflow_id, error="Workflow cancelled")

        logger.info(f"Cancelled workflow {workflow_id}")
        return True

    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Register a handler for workflow events.

        Args:
            event_type: Type of event to handle
            handler: Function to call when event occurs
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []

        self.event_handlers[event_type].append(handler)

    async def _trigger_event(self, event_type: str, **event_data) -> None:
        """
        Trigger an event.

        Args:
            event_type: Type of event to trigger
            event_data: Data associated with the event
        """
        if event_type not in self.event_handlers:
            return

        for handler in self.event_handlers[event_type]:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event_type=event_type, **event_data)
                else:
                    handler(event_type=event_type, **event_data)
            except Exception as e:
                logger.error(f"Error in event handler for {event_type}: {str(e)}")

    async def create_debugging_trace(self, workflow_id: str) -> Dict[str, Any]:
        """
        Create a debugging trace for a workflow.

        This collects all information about the workflow execution for debugging
        purposes.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Debugging trace information
        """
        # Get run info
        run_info = self.active_runs.get(workflow_id, {})

        # Get checkpoints
        checkpoints = await self.state_management.list_checkpoints(workflow_id)

        # Get latest state
        latest_state = await self.state_management.get_active_state(workflow_id)

        # Create the trace
        trace = {
            "workflow_id": workflow_id,
            "run_info": run_info,
            "checkpoints": checkpoints,
            "latest_state": latest_state,
        }

        return trace
