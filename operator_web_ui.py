#!/usr/bin/env python3
"""
OpenAI Operator-Style Browser AI - Web Interface
Integrates with local AI agent system and provides exact Operator functionality
"""

from flask import Flask, render_template_string, request, jsonify, session
import sys
import os
import subprocess
import requests
import time
import json
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

app = Flask(__name__)
app.secret_key = 'operator_browser_ai_secret_key'

class LocalSystemConnector:
    """Connects to the user's existing local AI agent system"""
    
    def __init__(self):
        self.connections = {}
        self.initialize_connections()
    
    def initialize_connections(self):
        """Initialize connections to local AI agent system"""
        print("🔗 Connecting to local AI agent system...")
        
        # Test WebRover connection
        try:
            response = requests.get("http://localhost:8000/health", timeout=3)
            if response.status_code == 200:
                self.connections["webrover"] = "http://localhost:8000"
                print("✅ Connected to WebRover")
        except:
            print("⚠️ WebRover not available")
        
        # Test UI-TARS connection
        try:
            response = requests.get("http://localhost:8080/health", timeout=3)
            if response.status_code == 200:
                self.connections["ui_tars"] = "http://localhost:8080"
                print("✅ Connected to UI-TARS")
        except:
            print("⚠️ UI-TARS not available")
        
        # Test Ollama connection
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if response.status_code == 200:
                self.connections["ollama"] = "http://localhost:11434"
                print("✅ Connected to Ollama")
        except:
            print("⚠️ Ollama not available")
        
        print(f"📊 Connected to {len(self.connections)} services")
    
    def chat_with_llm(self, message: str, context: str = "") -> str:
        """Chat with local LLM"""
        if "ollama" not in self.connections:
            return "❌ No local LLM available. Please start Ollama with a model like llama3.2"
        
        try:
            payload = {
                "model": "llama3.2",  # Default model
                "prompt": f"Context: {context}\n\nUser: {message}\n\nAssistant:",
                "stream": False
            }
            
            response = requests.post(
                f"{self.connections['ollama']}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "No response from LLM")
            else:
                return f"❌ LLM Error: {response.status_code}"
                
        except Exception as e:
            return f"❌ LLM Error: {str(e)}"
    
    def execute_browser_automation(self, task: str) -> Dict[str, Any]:
        """Execute browser automation through available services"""
        if "webrover" in self.connections:
            return self.execute_webrover_task(task)
        elif "ui_tars" in self.connections:
            return self.execute_ui_tars_task(task)
        else:
            return {"error": "No browser automation service available", "message": "Please start WebRover or UI-TARS"}
    
    def execute_webrover_task(self, task: str) -> Dict[str, Any]:
        """Execute task through WebRover"""
        try:
            payload = {
                "query": task,
                "agent_type": "task"
            }
            response = requests.post(f"{self.connections['webrover']}/query", json=payload, timeout=30)
            if response.status_code == 200:
                return {"success": True, "result": response.json()}
            else:
                return {"error": f"WebRover error: {response.status_code}"}
        except Exception as e:
            return {"error": f"WebRover execution failed: {str(e)}"}
    
    def execute_ui_tars_task(self, task: str) -> Dict[str, Any]:
        """Execute task through UI-TARS"""
        try:
            payload = {
                "action": task,
                "type": "browser_automation"
            }
            response = requests.post(f"{self.connections['ui_tars']}/execute", json=payload, timeout=30)
            if response.status_code == 200:
                return {"success": True, "result": response.json()}
            else:
                return {"error": f"UI-TARS error: {response.status_code}"}
        except Exception as e:
            return {"error": f"UI-TARS execution failed: {str(e)}"}

# Global connector instance
connector = LocalSystemConnector()

# Session storage for operator states
operator_sessions = {}

class OperatorSession:
    """Operator session for each user"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.current_task = None
        self.task_steps = []
        self.step_index = 0
        self.user_control = False
        self.paused = False
        self.conversation_history = []
    
    def start_task(self, user_input: str) -> Dict[str, Any]:
        """Start a task exactly like OpenAI Operator"""
        self.current_task = user_input
        self.step_index = 0
        self.user_control = False
        self.paused = False
        
        # Use local LLM to plan the task
        planning_prompt = f"""
        You are an AI browser automation agent like OpenAI's Operator. 
        Break down this user request into specific, actionable browser automation steps:
        
        User Request: {user_input}
        
        Provide a numbered step-by-step plan that can be executed through browser automation.
        Each step should be a single, clear action.
        """
        
        llm_response = connector.chat_with_llm(planning_prompt)
        
        # Parse the response into steps
        self.task_steps = self.parse_steps(llm_response)
        
        return {
            "task": user_input,
            "steps": self.task_steps,
            "total_steps": len(self.task_steps),
            "llm_planning": llm_response,
            "status": "ready"
        }
    
    def parse_steps(self, llm_response: str) -> List[str]:
        """Parse LLM response into actionable steps"""
        lines = llm_response.split('\n')
        steps = []
        
        for line in lines:
            line = line.strip()
            if (line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or 
                line.startswith('-') or line.startswith('•')):
                step = line.lstrip('123456789.-• ').strip()
                if step and len(step) > 5:
                    steps.append(step)
        
        if not steps:
            steps = [llm_response.strip()]
        
        return steps
    
    def execute_next_step(self) -> Dict[str, Any]:
        """Execute the next step like Operator"""
        if self.paused:
            return {"status": "paused", "message": "⏸️ Automation is paused."}
        
        if self.user_control:
            return {"status": "user_control", "message": "🎮 User has control."}
        
        if self.step_index >= len(self.task_steps):
            return {"status": "completed", "message": "✅ Task completed successfully!"}
        
        current_step = self.task_steps[self.step_index]
        
        # Execute the step through the agent system
        result = connector.execute_browser_automation(current_step)
        
        self.step_index += 1
        progress = (self.step_index / len(self.task_steps)) * 100
        
        return {
            "step": current_step,
            "step_number": self.step_index,
            "total_steps": len(self.task_steps),
            "result": result,
            "status": "in_progress" if self.step_index < len(self.task_steps) else "completed",
            "progress": progress
        }

def get_operator_session():
    """Get or create operator session"""
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    session_id = session['session_id']
    if session_id not in operator_sessions:
        operator_sessions[session_id] = OperatorSession(session_id)
    
    return operator_sessions[session_id]

@app.route('/')
def index():
    """Main page"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/start_task', methods=['POST'])
def start_task():
    """Start a new automation task"""
    data = request.json
    user_input = data.get('message', '')
    
    if not user_input:
        return jsonify({"error": "No message provided"})
    
    operator = get_operator_session()
    result = operator.start_task(user_input)
    
    return jsonify(result)

@app.route('/api/execute_step', methods=['POST'])
def execute_step():
    """Execute next step"""
    operator = get_operator_session()
    result = operator.execute_next_step()
    return jsonify(result)

@app.route('/api/takeover', methods=['POST'])
def takeover():
    """User takes control"""
    operator = get_operator_session()
    operator.user_control = True
    operator.paused = True
    return jsonify({"message": "🎮 User control activated"})

@app.route('/api/resume', methods=['POST'])
def resume():
    """Resume AI control"""
    operator = get_operator_session()
    operator.user_control = False
    operator.paused = False
    return jsonify({"message": "🤖 AI control resumed"})

@app.route('/api/status', methods=['GET'])
def status():
    """Get current status"""
    operator = get_operator_session()
    
    return jsonify({
        "current_task": operator.current_task,
        "total_steps": len(operator.task_steps),
        "completed_steps": operator.step_index,
        "progress": (operator.step_index / len(operator.task_steps)) * 100 if operator.task_steps else 0,
        "user_control": operator.user_control,
        "paused": operator.paused,
        "connections": connector.connections
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """Chat with local LLM"""
    data = request.json
    message = data.get('message', '')
    
    if not message:
        return jsonify({"error": "No message provided"})
    
    response = connector.chat_with_llm(message, "You are a helpful AI assistant for browser automation.")
    return jsonify({"response": response})

# HTML Template
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>OpenAI Operator Browser AI</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 2em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .controls { padding: 20px; border-bottom: 1px solid #eee; display: flex; gap: 10px; flex-wrap: wrap; }
        .btn { padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s; }
        .btn-primary { background: #667eea; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn:hover { transform: translateY(-1px); box-shadow: 0 2px 8px rgba(0,0,0,0.2); }
        .chat-area { height: 500px; overflow-y: auto; padding: 20px; border-bottom: 1px solid #eee; }
        .message { margin-bottom: 15px; padding: 12px; border-radius: 8px; }
        .user-message { background: #e3f2fd; margin-left: 20%; }
        .ai-message { background: #f1f8e9; margin-right: 20%; }
        .system-message { background: #fff3e0; text-align: center; font-style: italic; }
        .input-area { padding: 20px; display: flex; gap: 10px; }
        .input-area input { flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px; }
        .status-bar { padding: 15px; background: #f8f9fa; border-top: 1px solid #eee; font-family: monospace; font-size: 14px; }
        .quick-actions { padding: 20px; border-bottom: 1px solid #eee; }
        .quick-actions h3 { margin: 0 0 15px 0; color: #333; }
        .quick-btn { padding: 8px 16px; margin: 5px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 20px; cursor: pointer; font-size: 14px; }
        .quick-btn:hover { background: #e9ecef; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 OpenAI Operator Browser AI</h1>
            <p>Exact same features as OpenAI's Operator • Connected to your local AI agent system</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-secondary" onclick="takeover()">🎮 Take Control</button>
            <button class="btn btn-primary" onclick="resume()">🤖 Resume AI</button>
            <button class="btn btn-success" onclick="executeStep()">▶️ Next Step</button>
            <button class="btn btn-warning" onclick="getStatus()">📊 Status</button>
        </div>
        
        <div class="quick-actions">
            <h3>Quick Actions:</h3>
            <button class="quick-btn" onclick="quickAction('Help me check my email and respond to important messages')">📧 Email Tasks</button>
            <button class="quick-btn" onclick="quickAction('Research my competitors and compile a report')">🔍 Research</button>
            <button class="quick-btn" onclick="quickAction('Help me find and order office supplies online')">🛒 Shopping</button>
            <button class="quick-btn" onclick="quickAction('Schedule a meeting through my calendar')">📅 Booking</button>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message system-message">
                🚀 <strong>OpenAI Operator Browser AI Ready!</strong><br><br>
                <strong>🎯 Exact same capabilities as OpenAI's Operator:</strong><br><br>
                ✅ <strong>Natural Language Automation</strong><br>
                ✅ <strong>Seamless Control Handoff</strong><br>
                ✅ <strong>Connected to Your Local System</strong><br><br>
                <strong>Just describe what you want to accomplish - I'll handle the rest!</strong>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Describe what you want to accomplish (just like with OpenAI's Operator)..." onkeypress="handleKeyPress(event)">
            <button class="btn btn-primary" onclick="sendMessage()">Send</button>
        </div>
        
        <div class="status-bar" id="statusBar">
            🔄 Initializing connections to local AI agent system...
        </div>
    </div>

    <script>
        function addMessage(content, type) {
            const chatArea = document.getElementById('chatArea');
            const message = document.createElement('div');
            message.className = `message ${type}-message`;
            message.innerHTML = content;
            chatArea.appendChild(message);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;

            addMessage(message, 'user');
            input.value = '';

            fetch('/api/start_task', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({message: message})
            })
            .then(response => response.json())
            .then(data => {
                let response = `🤖 <strong>Task Started - OpenAI Operator Style</strong><br><br>`;
                response += `<strong>Your Request:</strong> ${data.task}<br><br>`;
                response += `<strong>📋 Execution Plan (${data.total_steps} steps):</strong><br>`;
                data.steps.forEach((step, i) => {
                    response += `${i + 1}. ${step}<br>`;
                });
                response += `<br><strong>🎮 Control Options:</strong><br>`;
                response += `- Click "Next Step" - Execute one step at a time<br>`;
                response += `- Click "Take Control" - You control, I assist<br>`;
                response += `- Click "Resume AI" - I take back control<br>`;
                addMessage(response, 'ai');
            });
        }

        function executeStep() {
            fetch('/api/execute_step', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                let message = '';
                if (data.status === 'completed') {
                    message = `✅ <strong>Task Completed!</strong><br>${data.message}`;
                } else if (data.status === 'in_progress') {
                    message = `▶️ <strong>Step ${data.step_number}/${data.total_steps}</strong><br><strong>Executed:</strong> ${data.step}<br><strong>Progress:</strong> ${data.progress.toFixed(1)}%`;
                } else {
                    message = `ℹ️ <strong>Status:</strong> ${data.message}`;
                }
                addMessage(message, 'ai');
            });
        }

        function takeover() {
            fetch('/api/takeover', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                addMessage(`🎮 <strong>USER CONTROL ACTIVATED</strong><br><br>You now have full control of the browser!<br><br><strong>What I'll do:</strong><br>- ✅ Monitor your actions<br>- ✅ Provide assistance when asked<br>- ✅ Remember context for when you hand back control<br><br><strong>To resume automation:</strong> Click "Resume AI"`, 'ai');
            });
        }

        function resume() {
            fetch('/api/resume', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                addMessage(`🤖 <strong>AI CONTROL RESUMED</strong><br><br>I'm back in control and will continue the automation!<br><br><strong>Current Status:</strong><br>- ✅ Resuming from where we left off<br>- ✅ All context preserved<br>- ✅ Ready to execute remaining steps`, 'ai');
            });
        }

        function getStatus() {
            fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                let status = `📊 <strong>System Status</strong><br><br>`;
                if (data.current_task) {
                    status += `<strong>Current Task:</strong> ${data.current_task}<br>`;
                    status += `<strong>Progress:</strong> ${data.completed_steps}/${data.total_steps} steps (${data.progress.toFixed(1)}%)<br>`;
                    status += `<strong>Control:</strong> ${data.user_control ? '🎮 User' : '🤖 AI'}<br>`;
                    status += `<strong>Status:</strong> ${data.paused ? '⏸️ Paused' : '▶️ Running'}<br><br>`;
                }
                status += `<strong>Connected Services:</strong><br>`;
                Object.entries(data.connections).forEach(([service, url]) => {
                    status += `✅ ${service.replace('_', ' ').toUpperCase()}: ${url}<br>`;
                });
                addMessage(status, 'ai');
            });
        }

        function quickAction(action) {
            document.getElementById('messageInput').value = action;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Initialize status
        getStatus();
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    print("🤖 Starting OpenAI Operator-Style Browser AI...")
    print("🔗 Connecting to local AI agent system...")
    print("📍 Will run on http://localhost:7892")
    
    try:
        app.run(host='0.0.0.0', port=7892, debug=False)
    except Exception as e:
        print(f"❌ Error: {e}")
        print("🔧 Trying port 7893...")
        try:
            app.run(host='0.0.0.0', port=7893, debug=False)
        except Exception as e2:
            print(f"❌ Failed: {e2}")
