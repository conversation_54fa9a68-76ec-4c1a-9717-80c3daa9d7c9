@echo off
title Browser Automation System Startup
color 0A

echo ========================================
echo    🤖 BROWSER AUTOMATION SYSTEM 🤖
echo ========================================
echo.
echo Starting all browser automation services...
echo.

REM Kill any existing processes on the ports
echo 🔄 Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":8001" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":8081" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":7788" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1

echo.
echo 🚀 Starting services in order...
echo.

REM Start WebRover Backend
echo [1/4] Starting WebRover Backend...
start "WebRover Backend" cmd /c start_webrover_backend.bat
timeout /t 3 /nobreak > nul

REM Start WebRover Frontend  
echo [2/4] Starting WebRover Frontend...
start "WebRover Frontend" cmd /c start_webrover_frontend.bat
timeout /t 3 /nobreak > nul

REM Start Midscene Integration
echo [3/4] Starting Midscene Integration...
start "Midscene Integration" cmd /c start_midscene_integration.bat
timeout /t 3 /nobreak > nul

REM Start Browser-Use Web UI
echo [4/4] Starting Browser-Use Web UI...
cd /d "C:\Users\<USER>\Documents\augment-projects\Ai Agent System\browser-use-webui"
start "Browser-Use Web UI" cmd /c "python webui.py --ip 127.0.0.1 --port 7788"

echo.
echo ✅ All services started!
echo.
echo 🌐 Available interfaces:
echo   • WebRover Backend:     http://localhost:8001
echo   • WebRover Frontend:    http://localhost:3000  
echo   • Browser-Use Web UI:   http://localhost:7788
echo   • Midscene MCP Server:  http://localhost:8081
echo.
echo Press any key to open all interfaces in browser...
pause >nul

REM Open all interfaces
start http://localhost:8001
start http://localhost:3000
start http://localhost:7788

echo.
echo 🎉 Browser Automation System is ready!
echo Press any key to exit...
pause >nul
