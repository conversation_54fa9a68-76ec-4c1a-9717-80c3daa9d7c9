#!/usr/bin/env python3
"""
Comprehensive Web-UI Browser Automation Launcher
Starts MCP servers, local LLMs, and web-UI browser automation with proper port management
"""

import os
import sys
import time
import subprocess
import json
import requests
from pathlib import Path
import threading
import signal
from datetime import datetime

class BrowserAutomationLauncher:
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        self.config = self.load_config()

    def load_config(self):
        """Load configuration from multiple sources"""
        config = {
            "ports": {
                "web_ui": 7788,
                "ui_tars": 8080,
                "local_llm": 8000,
                "mcp_standard": 8765,
                "mcp_simple": 8766,
                "mcp_advanced": 8767,
                "mcp_secure": 8768
            },
            "chrome_path": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "chrome_debug_port": 9222
        }

        # Load browser automation config if exists
        browser_config_path = self.base_dir / "config" / "browser_automation_config.json"
        if browser_config_path.exists():
            with open(browser_config_path, 'r') as f:
                browser_config = json.load(f)
                config.update(browser_config)

        return config

    def check_port(self, port):
        """Check if a port is available"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return True
            except OSError:
                return False

    def wait_for_service(self, url, timeout=30):
        """Wait for a service to become available"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(2)
        return False

    def start_local_llm_server(self):
        """Start local Hugging Face LLM server"""
        print("🤖 Starting Local LLM Server...")

        llm_port = self.config["ports"]["local_llm"]
        if not self.check_port(llm_port):
            print(f"⚠️  Port {llm_port} is already in use, skipping LLM server")
            return None

        # Check if we have a local LLM server script
        llm_script = self.base_dir / "local_hf_server.py"
        if not llm_script.exists():
            llm_script = self.base_dir / "simple_hf_server.py"

        if llm_script.exists():
            # Use simple_hf_server.py without port argument (it uses port 8000 by default)
            if "simple_hf_server.py" in str(llm_script):
                cmd = [sys.executable, str(llm_script)]
            else:
                cmd = [sys.executable, str(llm_script), "--port", str(llm_port)]

            process = subprocess.Popen(cmd, cwd=self.base_dir)
            self.processes.append(("Local LLM Server", process))

            # Wait for server to start
            if self.wait_for_service(f"http://localhost:{llm_port}/health"):
                print(f"✅ Local LLM Server started on port {llm_port}")
            else:
                print(f"⚠️  Local LLM Server may not be responding on port {llm_port}")
            return process
        else:
            print("⚠️  No local LLM server script found")
            return None

    def start_mcp_servers(self):
        """Start MCP servers"""
        print("🔧 Starting MCP Servers...")

        mcp_config_path = self.base_dir / "mcp_servers" / "config.json"
        if not mcp_config_path.exists():
            print("⚠️  MCP server config not found")
            return []

        mcp_start_script = self.base_dir / "mcp_servers" / "start_mcp_servers.py"
        if mcp_start_script.exists():
            cmd = [sys.executable, str(mcp_start_script)]
            process = subprocess.Popen(cmd, cwd=self.base_dir / "mcp_servers")
            self.processes.append(("MCP Servers", process))

            # Wait a bit for servers to start
            time.sleep(3)
            print("✅ MCP Servers started")
            return [process]
        else:
            print("⚠️  MCP server start script not found")
            return []

    def start_ui_tars(self):
        """Start UI-TARS service"""
        print("🎯 Starting UI-TARS...")

        ui_tars_port = self.config["ports"]["ui_tars"]
        if not self.check_port(ui_tars_port):
            print(f"⚠️  Port {ui_tars_port} is already in use, skipping UI-TARS")
            return None

        ui_tars_script = self.base_dir / "ui_tars" / "main.py"
        if ui_tars_script.exists():
            cmd = [sys.executable, str(ui_tars_script)]
            process = subprocess.Popen(cmd, cwd=self.base_dir / "ui_tars")
            self.processes.append(("UI-TARS", process))

            # Wait for UI-TARS to start
            if self.wait_for_service(f"http://localhost:{ui_tars_port}"):
                print(f"✅ UI-TARS started on port {ui_tars_port}")
            else:
                print(f"⚠️  UI-TARS may not be responding on port {ui_tars_port}")
            return process
        else:
            print("⚠️  UI-TARS main script not found")
            return None

    def start_web_ui(self):
        """Start Web-UI Browser Automation"""
        print("🌐 Starting Web-UI Browser Automation...")

        web_ui_port = self.config["ports"]["web_ui"]
        if not self.check_port(web_ui_port):
            print(f"⚠️  Port {web_ui_port} is already in use, skipping Web-UI")
            return None

        web_ui_script = self.base_dir / "web-ui" / "webui.py"
        if web_ui_script.exists():
            cmd = [sys.executable, str(web_ui_script), "--port", str(web_ui_port), "--ip", "0.0.0.0"]
            process = subprocess.Popen(cmd, cwd=self.base_dir / "web-ui")
            self.processes.append(("Web-UI Browser Automation", process))

            # Wait for Web-UI to start
            if self.wait_for_service(f"http://localhost:{web_ui_port}"):
                print(f"✅ Web-UI Browser Automation started on port {web_ui_port}")
                print(f"🌐 Access at: http://localhost:{web_ui_port}")
            else:
                print(f"⚠️  Web-UI may not be responding on port {web_ui_port}")
            return process
        else:
            print("⚠️  Web-UI script not found")
            return None

    def setup_chrome_debugging(self):
        """Setup Chrome with remote debugging"""
        print("🔍 Setting up Chrome debugging...")

        chrome_path = self.config.get("chrome_path", "chrome")
        debug_port = self.config.get("chrome_debug_port", 9222)

        # Check if Chrome is already running with debugging
        try:
            response = requests.get(f"http://localhost:{debug_port}/json", timeout=2)
            if response.status_code == 200:
                print(f"✅ Chrome debugging already available on port {debug_port}")
                return True
        except:
            pass

        # Start Chrome with debugging
        user_data_dir = self.base_dir / "chrome_debug_profile"
        user_data_dir.mkdir(exist_ok=True)

        chrome_cmd = [
            chrome_path,
            f"--remote-debugging-port={debug_port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check"
        ]

        try:
            process = subprocess.Popen(chrome_cmd)
            self.processes.append(("Chrome Debug", process))

            # Wait for Chrome to start
            time.sleep(3)
            if self.wait_for_service(f"http://localhost:{debug_port}/json"):
                print(f"✅ Chrome debugging started on port {debug_port}")
                return True
            else:
                print(f"⚠️  Chrome debugging may not be responding on port {debug_port}")
                return False
        except Exception as e:
            print(f"⚠️  Failed to start Chrome debugging: {e}")
            return False

    def launch_all(self):
        """Launch all components in correct order"""
        print("🚀 LAUNCHING WEB-UI BROWSER AUTOMATION SYSTEM")
        print("=" * 60)
        print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 1. Start MCP Servers
        self.start_mcp_servers()
        time.sleep(2)

        # 2. Start Local LLM Server
        self.start_local_llm_server()
        time.sleep(3)

        # 3. Setup Chrome debugging
        self.setup_chrome_debugging()
        time.sleep(2)

        # 4. Start UI-TARS
        self.start_ui_tars()
        time.sleep(3)

        # 5. Start Web-UI
        web_ui_process = self.start_web_ui()

        print()
        print("🎉 SYSTEM LAUNCH COMPLETE!")
        print("=" * 60)
        print("📊 Running Services:")
        for name, process in self.processes:
            status = "✅ Running" if process.poll() is None else "❌ Stopped"
            print(f"  {name}: {status}")

        print()
        print("🌐 Access Points:")
        print(f"  Web-UI Browser Automation: http://localhost:{self.config['ports']['web_ui']}")
        print(f"  UI-TARS API: http://localhost:{self.config['ports']['ui_tars']}")
        print(f"  Local LLM API: http://localhost:{self.config['ports']['local_llm']}/v1")
        print(f"  Chrome Debug: http://localhost:{self.config['chrome_debug_port']}/json")

        return web_ui_process

    def cleanup(self):
        """Clean up all processes"""
        print("\n🛑 Shutting down services...")
        for name, process in self.processes:
            if process.poll() is None:
                print(f"  Stopping {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        print("✅ All services stopped")

def main():
    launcher = BrowserAutomationLauncher()

    def signal_handler(signum, frame):
        launcher.cleanup()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        web_ui_process = launcher.launch_all()

        if web_ui_process:
            print("\n💬 READY FOR BROWSER AUTOMATION!")
            print("🎯 You can now chat with the browser automation system")
            print("📝 Try commands like:")
            print("   - 'Open Google and search for AI news'")
            print("   - 'Navigate to GitHub and find trending repositories'")
            print("   - 'Open Gmail and check for new emails'")
            print("\n⌨️  Press Ctrl+C to stop all services")

            # Keep the main process alive
            web_ui_process.wait()
        else:
            print("❌ Failed to start Web-UI Browser Automation")
            launcher.cleanup()
            sys.exit(1)

    except KeyboardInterrupt:
        launcher.cleanup()
    except Exception as e:
        print(f"❌ Error: {e}")
        launcher.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
