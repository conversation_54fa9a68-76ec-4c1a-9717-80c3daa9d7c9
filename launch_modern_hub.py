#!/usr/bin/env python3
"""
Quick launcher for the Modern AI Central Hub
"""
import os
import sys
import subprocess

def main():
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    print("🚀 Launching Modern AI Central Hub...")
    print(f"📂 Working directory: {os.getcwd()}")

    try:
        # Import and run the hub
        from modern_ai_central_hub import ModernAIAgentHub

        print("✅ Successfully imported ModernAIAgentHub")

        # Create and run the hub
        hub = ModernAIAgentHub()
        print("✅ Hub created successfully")

        hub.run()
        print("✅ Hub started successfully")

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("📋 Attempting direct execution...")

        # Try running the file directly
        subprocess.run([sys.executable, "modern_ai_central_hub.py"])

    except Exception as e:
        print(f"❌ Error launching hub: {e}")
        print(f"📝 Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
