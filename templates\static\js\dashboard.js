/**
 * Dashboard functionality for Multi-Agent AI System
 */

// Global variables
let browserFeedSocket;
let agentEventsSocket;
let refreshInterval = 1000; // ms

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  console.log("Dashboard initializing...");

  // Connect WebSockets
  connectWebSockets();

  // Set up UI event handlers
  setupEventListeners();

  // Add initial log entry
  addLogEntry("info", "Dashboard initialized");
});

/**
 * Connect to WebSocket endpoints
 */
function connectWebSockets() {
  // Browser feed WebSocket
  const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:";
  const wsBase = `${wsProtocol}//${window.location.host}`;

  // Browser feed WebSocket
  browserFeedSocket = new WebSocket(`${wsBase}/ws/browser-feed`);
  browserFeedSocket.onopen = () =>
    addLogEntry("info", "Browser feed connected");
  browserFeedSocket.onclose = () =>
    addLogEntry("warning", "Browser feed disconnected");
  browserFeedSocket.onerror = (error) =>
    addLogEntry("error", `Browser feed error: ${error}`);
  browserFeedSocket.onmessage = handleBrowserFeedMessage;

  // Agent events WebSocket
  agentEventsSocket = new WebSocket(`${wsBase}/ws/agent-events`);
  agentEventsSocket.onopen = () =>
    addLogEntry("info", "Agent events connected");
  agentEventsSocket.onclose = () =>
    addLogEntry("warning", "Agent events disconnected");
  agentEventsSocket.onerror = (error) =>
    addLogEntry("error", `Agent events error: ${error}`);
  agentEventsSocket.onmessage = handleAgentEventsMessage;
}

/**
 * Handle messages from browser feed WebSocket
 */
function handleBrowserFeedMessage(event) {
  try {
    const data = JSON.parse(event.data);
    if (data.type === "screenshot" && data.data) {
      document.getElementById(
        "browserScreenshot"
      ).src = `data:image/jpeg;base64,${data.data}`;
    }
  } catch (error) {
    console.error("Error handling browser feed message:", error);
  }
}

/**
 * Handle messages from agent events WebSocket
 */
function handleAgentEventsMessage(event) {
  try {
    const data = JSON.parse(event.data);

    // Handle different event types
    switch (data.type) {
      case "log":
        addLogEntry(data.level, data.message);
        break;

      case "agent_status":
        updateAgentStatus(data.agent_id, data.status);
        break;

      case "hitl_pause":
        showHumanInterventionPanel(data.message);
        break;

      case "heartbeat":
        // Just keep the connection alive
        break;

      default:
        console.log("Unknown event type:", data.type, data);
    }
  } catch (error) {
    console.error("Error handling agent events message:", error);
  }
}

/**
 * Set up UI event listeners
 */
function setupEventListeners() {
  // Refresh button
  document
    .getElementById("refreshBtn")
    ?.addEventListener("click", refreshBrowserView);

  // HITL submit button
  document
    .getElementById("hitlSubmitBtn")
    ?.addEventListener("click", submitHumanInput);

  // HITL cancel button
  document
    .getElementById("hitlCancelBtn")
    ?.addEventListener("click", cancelHumanInput);

  // Stop all agents button
  document.getElementById("stopBtn")?.addEventListener("click", stopAllAgents);

  // Submit task button
  document
    .getElementById("submitTaskBtn")
    ?.addEventListener("click", submitNewTask);

  // Save settings button
  document
    .getElementById("saveSettingsBtn")
    ?.addEventListener("click", saveSettings);
}

/**
 * Add a log entry to the log window
 */
function addLogEntry(level, message) {
  const logWindow = document.getElementById("logWindow");
  if (!logWindow) return;

  const now = new Date();
  const timeString = now.toLocaleTimeString();

  const logEntry = document.createElement("div");
  logEntry.className = "log-entry";
  logEntry.innerHTML = `<span class="log-time">[${timeString}]</span> <span class="log-level-${level}">[${level.toUpperCase()}]</span> ${message}`;

  logWindow.appendChild(logEntry);
  logWindow.scrollTop = logWindow.scrollHeight;

  // Limit log entries (keep last 100)
  const entries = logWindow.getElementsByClassName("log-entry");
  if (entries.length > 100) {
    logWindow.removeChild(entries[0]);
  }
}

/**
 * Update the status display for an agent
 */
function updateAgentStatus(agentId, status) {
  const agentsList = document.getElementById("agentsList");
  if (!agentsList) return;

  // Check if agent already exists in list
  let agentElement = document.getElementById(`agent-${agentId}`);

  if (!agentElement) {
    // Agent doesn't exist, fetch agent info
    fetch(`/api/agents/${agentId}`)
      .then((response) => response.json())
      .then((data) => {
        const agentDiv = document.createElement("div");
        agentDiv.id = `agent-${agentId}`;
        agentDiv.className = `agent-status ${status.toLowerCase()}`;
        agentDiv.innerHTML = `
                    <h6>${data.name || agentId}</h6>
                    <p class="mb-0 text-muted">${status}</p>
                `;
        agentsList.appendChild(agentDiv);
      })
      .catch((error) => {
        console.error("Error fetching agent info:", error);

        // Create a generic entry if fetch fails
        const agentDiv = document.createElement("div");
        agentDiv.id = `agent-${agentId}`;
        agentDiv.className = `agent-status ${status.toLowerCase()}`;
        agentDiv.innerHTML = `
                    <h6>${agentId}</h6>
                    <p class="mb-0 text-muted">${status}</p>
                `;
        agentsList.appendChild(agentDiv);
      });
  } else {
    // Update existing agent
    agentElement.className = `agent-status ${status.toLowerCase()}`;
    const statusElement = agentElement.querySelector("p");
    if (statusElement) {
      statusElement.textContent = status;
    }
  }
}

/**
 * Show the human intervention panel
 */
function showHumanInterventionPanel(message) {
  document.getElementById("hitlPanel")?.classList.remove("d-none");
  document.getElementById("noHitlPanel")?.classList.add("d-none");
  document.getElementById("hitlReason").textContent = message;
  addLogEntry("warning", `Human intervention required: ${message}`);
}

/**
 * Submit human input and continue agent execution
 */
function submitHumanInput() {
  const input = document.getElementById("hitlInput")?.value;

  fetch("/api/agent/hitl-continue", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ input }),
  })
    .then((response) => response.json())
    .then((data) => {
      // Hide HITL panel
      document.getElementById("hitlPanel")?.classList.add("d-none");
      document.getElementById("noHitlPanel")?.classList.remove("d-none");
      document.getElementById("hitlInput").value = "";

      addLogEntry("info", "Human input provided to agent");
    })
    .catch((error) => {
      addLogEntry("error", `Error submitting human input: ${error}`);
    });
}

/**
 * Cancel human intervention
 */
function cancelHumanInput() {
  document.getElementById("hitlPanel")?.classList.add("d-none");
  document.getElementById("noHitlPanel")?.classList.remove("d-none");
  document.getElementById("hitlInput").value = "";

  fetch("/api/agent/hitl-cancel", {
    method: "POST",
  })
    .then((response) => response.json())
    .then((data) => {
      addLogEntry("info", "Human intervention cancelled");
    })
    .catch((error) => {
      addLogEntry("error", `Error cancelling human intervention: ${error}`);
    });
}

/**
 * Refresh the browser view
 */
function refreshBrowserView() {
  fetch("/api/screenshot")
    .then((response) => response.json())
    .then((data) => {
      if (data.screenshot) {
        document.getElementById(
          "browserScreenshot"
        ).src = `data:image/jpeg;base64,${data.screenshot}`;
        addLogEntry("info", "Browser view refreshed");
      }
    })
    .catch((error) => {
      addLogEntry("error", `Error refreshing browser view: ${error}`);
    });
}

/**
 * Stop all agents
 */
function stopAllAgents() {
  if (confirm("Are you sure you want to stop all agents?")) {
    fetch("/api/agents/stop-all", {
      method: "POST",
    })
      .then((response) => response.json())
      .then((data) => {
        addLogEntry("warning", "All agents stopped");
      })
      .catch((error) => {
        addLogEntry("error", `Error stopping agents: ${error}`);
      });
  }
}

/**
 * Submit a new task
 */
function submitNewTask() {
  const taskDescription = document.getElementById("taskDescription")?.value;
  const agentId = document.getElementById("agentSelect")?.value;

  if (!taskDescription) {
    alert("Please enter a task description");
    return;
  }

  fetch(`/api/agents/${agentId}/message`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      type: "task",
      content: taskDescription,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      // Close modal
      const modal = bootstrap.Modal.getInstance(
        document.getElementById("newTaskModal")
      );
      if (modal) modal.hide();

      document.getElementById("taskDescription").value = "";
      addLogEntry("info", `New task sent to ${agentId}: ${taskDescription}`);
    })
    .catch((error) => {
      addLogEntry("error", `Error submitting task: ${error}`);
    });
}

/**
 * Save dashboard settings
 */
function saveSettings() {
  const apiKey = document.getElementById("apiKeyInput")?.value;
  const autoSave = document.getElementById("autoSaveCheckbox")?.checked;
  refreshInterval = parseInt(
    document.getElementById("refreshIntervalInput")?.value || "1000"
  );

  fetch("/api/settings", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      api_key: apiKey,
      auto_save: autoSave,
      refresh_interval: refreshInterval,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      // Close modal
      const modal = bootstrap.Modal.getInstance(
        document.getElementById("settingsModal")
      );
      if (modal) modal.hide();

      addLogEntry("info", "Settings saved");
    })
    .catch((error) => {
      addLogEntry("error", `Error saving settings: ${error}`);
    });
}

/**
 * Reconnect WebSockets when disconnected
 */
function reconnectWebSockets() {
  addLogEntry("info", "Attempting to reconnect WebSockets...");
  connectWebSockets();
}

// Attempt to reconnect if WebSockets disconnect
setInterval(() => {
  if (browserFeedSocket && browserFeedSocket.readyState === WebSocket.CLOSED) {
    reconnectWebSockets();
  }
}, 5000);

// Load agent list on page load
window.addEventListener("load", () => {
  fetch("/api/agents")
    .then((response) => response.json())
    .then((data) => {
      const agentsList = document.getElementById("agentsList");
      if (!agentsList) return;

      agentsList.innerHTML = "";
      const agentSelect = document.getElementById("agentSelect");

      for (const [agentId, agent] of Object.entries(data.agents)) {
        // Add to agents list
        const agentDiv = document.createElement("div");
        agentDiv.id = `agent-${agentId}`;
        agentDiv.className = `agent-status ${agent.status.toLowerCase()}`;
        agentDiv.innerHTML = `
                    <h6>${agent.name}</h6>
                    <p class="mb-0 text-muted">${agent.status}</p>
                `;
        agentsList.appendChild(agentDiv);

        // Add to agent select dropdown if not orchestrator
        if (agentId !== "orchestrator" && agentSelect) {
          const option = document.createElement("option");
          option.value = agentId;
          option.textContent = agent.name;
          agentSelect.appendChild(option);
        }
      }
    })
    .catch((error) => {
      console.error("Error loading agents:", error);
      addLogEntry("error", `Error loading agents: ${error}`);
    });
});
