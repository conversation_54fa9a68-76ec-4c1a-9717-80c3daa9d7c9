{"disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInFunctionDeclaration": {"beforeOpeningRoundBrace": true}, "disallowEmptyBlocks": true, "disallowSpacesInsideParentheses": true, "disallowQuotedKeysInObjects": true, "disallowSpaceAfterObjectKeys": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowSpaceBeforePostfixUnaryOperators": true, "disallowSpaceBeforeBinaryOperators": [","], "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowTrailingComma": true, "disallowYodaConditions": true, "disallowKeywords": ["with"], "disallowMultipleLineBreaks": true, "requireSpaceBeforeBlockStatements": true, "requireParenthesesAroundIIFE": true, "requireSpacesInConditionalExpression": true, "disallowMultipleVarDecl": true, "requireBlocksOnNewline": 1, "requireCommaBeforeLineBreak": true, "requireSpaceBeforeBinaryOperators": true, "requireSpaceAfterBinaryOperators": true, "requireLineFeedAtFileEnd": true, "requireCapitalizedConstructors": true, "requireDotNotation": true, "requireSpacesInForStatement": true, "requireCurlyBraces": ["do"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "case", "return", "try", "catch", "typeof"], "safeContextKeyword": "self", "validateLineBreaks": "LF", "validateIndentation": 2}