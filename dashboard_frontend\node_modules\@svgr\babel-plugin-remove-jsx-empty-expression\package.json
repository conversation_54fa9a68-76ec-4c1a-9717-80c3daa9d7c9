{"name": "@svgr/babel-plugin-remove-jsx-empty-expression", "description": "Remove JSX empty expression", "version": "5.0.1", "main": "lib/index.js", "repository": "https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-remove-jsx-empty-expression", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["babel-plugin"], "engines": {"node": ">=10"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "scripts": {"prebuild": "rm -rf lib/", "build": "babel --config-file ../../babel.config.js -d lib --ignore \"**/*.test.js\" src", "prepublishOnly": "yarn run build"}, "gitHead": "2c0863b6821ef6b86bd7ad1a0267ba7e07b163ff"}