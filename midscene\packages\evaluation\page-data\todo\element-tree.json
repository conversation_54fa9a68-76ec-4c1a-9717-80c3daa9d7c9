{"node": null, "children": [{"node": null, "children": [{"node": null, "children": [{"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "fpael", "indexId": 0, "nodeHashId": "fpael", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<h3>"}, "center": [51, 43], "content": "React", "rect": {"left": 18, "top": 28, "width": 65, "height": 29, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "bkdon", "indexId": 1, "nodeHashId": "bkdon", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<h5>"}, "center": [37, 76], "content": "React", "rect": {"left": 18, "top": 67, "width": 38, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "opmfa", "indexId": 2, "nodeHashId": "opmfa", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [40, 94], "content": "Source", "rect": {"left": 18, "top": 85, "width": 44, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "gepop", "indexId": 3, "nodeHashId": "gepop", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<h5>"}, "center": [80, 122], "content": "TypeScript + React", "rect": {"left": 18, "top": 113, "width": 123, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "fpfeg", "indexId": 4, "nodeHashId": "fpfeg", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [37, 140], "content": "Demo", "rect": {"left": 18, "top": 131, "width": 37, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": {"id": "<PERSON><PERSON>", "indexId": 5, "nodeHashId": "<PERSON><PERSON>", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<span>"}, "center": [59, 140], "content": ",", "rect": {"left": 55, "top": 131, "width": 8, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": {"id": "f<PERSON>b", "indexId": 6, "nodeHashId": "f<PERSON>b", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [85, 140], "content": "Source", "rect": {"left": 63, "top": 131, "width": 44, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": []}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "elf<PERSON>h", "indexId": 7, "nodeHashId": "elf<PERSON>h", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<p>"}, "center": [150, 271], "content": "React is a JavaScript library for creating user interfaces. Its core principles are declarative code, efficiency, and flexibility. Simply specify what your component looks like and React will keep it up-to-date when the underlying data changes.", "rect": {"left": 28, "top": 203, "width": 244, "height": 135, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "hkkka", "indexId": 8, "nodeHashId": "hkkka", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [272, 379], "content": "React", "rect": {"left": 253, "top": 370, "width": 37, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": []}, {"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "jkmjg", "indexId": 9, "nodeHashId": "jkmjg", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<h4>"}, "center": [94, 441], "content": "Official Resources", "rect": {"left": 18, "top": 430, "width": 151, "height": 22, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "coknk", "indexId": 10, "nodeHashId": "coknk", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [78, 472], "content": "Quick Start", "rect": {"left": 43, "top": 463, "width": 70, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "obnba", "indexId": 11, "nodeHashId": "obnba", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [88, 492], "content": "API Reference", "rect": {"left": 43, "top": 483, "width": 89, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "bkcjh", "indexId": 12, "nodeHashId": "bkcjh", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [78, 512], "content": "Philosophy", "rect": {"left": 43, "top": 503, "width": 69, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "eglfn", "indexId": 13, "nodeHashId": "eglfn", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [100, 532], "content": "React Community", "rect": {"left": 43, "top": 523, "width": 113, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "ceeap", "indexId": 14, "nodeHashId": "ceeap", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<h4>"}, "center": [66, 583], "content": "Community", "rect": {"left": 18, "top": 572, "width": 95, "height": 22, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "ddhae", "indexId": 15, "nodeHashId": "ddhae", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [129, 614], "content": "ReactJS on Stack Overflow", "rect": {"left": 43, "top": 605, "width": 172, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}, {"node": null, "children": [{"node": null, "children": []}, {"node": null, "children": []}, {"node": null, "children": []}, {"node": null, "children": [{"node": {"id": "nddcg", "indexId": 16, "nodeHashId": "nddcg", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<em>"}, "center": [149, 695], "content": "If you have other helpful links to share, or find any of the links above no longer work, please", "rect": {"left": 18, "top": 677, "width": 261, "height": 36, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": null, "children": []}]}, {"node": null, "children": []}]}, {"node": null, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": null, "children": [{"node": {"id": "lnceo", "indexId": 17, "nodeHashId": "lnceo", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<h1>"}, "center": [790, 67], "content": "todos", "rect": {"left": 696, "top": 19, "width": 188, "height": 95, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": [{"node": {"id": "okgbn", "nodeHashId": "okgbn", "locator": "[_midscene_retrieve_task_id='okgbn']", "nodeType": "FORM_ITEM Node", "indexId": 18, "attributes": {"class": ".new-todo", "id": "todo-input", "type": "text", "data-testid": "text-input", "placeholder": "What needs to be done?", "htmlTagName": "<input>", "nodeType": "FORM_ITEM Node"}, "content": "What needs to be done?", "rect": {"left": 515, "top": 130, "width": 550, "height": 65, "zoom": 1}, "center": [790, 163], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": {"id": "liecc", "indexId": 19, "nodeHashId": "liecc", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<label>"}, "center": [1110, 140], "content": "New Todo Input", "rect": {"left": 1064, "top": 131, "width": 91, "height": 17, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": {"id": "ejffn", "nodeHashId": "ejffn", "locator": "[_midscene_retrieve_task_id='ejffn']", "nodeType": "FORM_ITEM Node", "indexId": 20, "attributes": {"class": ".toggle-all", "type": "checkbox", "id": "toggle-all", "data-testid": "toggle-all", "htmlTagName": "<input>", "nodeType": "FORM_ITEM Node"}, "content": "on", "rect": {"left": 519, "top": 133, "width": 40, "height": 60, "zoom": 1}, "center": [539, 163], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": null, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": null, "children": [{"node": {"id": "hbdoo", "nodeHashId": "hbdoo", "locator": "[_midscene_retrieve_task_id='hbdoo']", "nodeType": "FORM_ITEM Node", "indexId": 21, "attributes": {"class": ".toggle", "type": "checkbox", "data-testid": "todo-item-toggle", "htmlTagName": "<input>", "nodeType": "FORM_ITEM Node"}, "content": "on", "rect": {"left": 515, "top": 205, "width": 40, "height": 40, "zoom": 1}, "center": [535, 225], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": {"id": "ofnmb", "indexId": 22, "nodeHashId": "ofnmb", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<label>"}, "center": [647, 225], "content": "Learn Python", "rect": {"left": 575, "top": 211, "width": 143, "height": 28, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": {"id": "cdllc", "nodeHashId": "cdllc", "locator": "[_midscene_retrieve_task_id='cdllc']", "nodeType": "FORM_ITEM Node", "indexId": 23, "attributes": {"class": ".toggle", "type": "checkbox", "data-testid": "todo-item-toggle", "htmlTagName": "<input>", "nodeType": "FORM_ITEM Node"}, "content": "on", "rect": {"left": 515, "top": 265, "width": 40, "height": 40, "zoom": 1}, "center": [535, 285], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": {"id": "idmhb", "indexId": 24, "nodeHashId": "idmhb", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<label>"}, "center": [634, 285], "content": "Learn Rust", "rect": {"left": 575, "top": 271, "width": 117, "height": 28, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": {"id": "jicbk", "indexId": 25, "nodeHashId": "jicbk", "nodeType": "BUTTON Node", "locator": "[_midscene_retrieve_task_id='jicbk']", "attributes": {"class": ".destroy", "data-testid": "todo-item-button", "htmlTagName": "<div>", "nodeType": "BUTTON Node"}, "content": "×", "rect": {"left": 1015, "top": 265, "width": 40, "height": 40, "zoom": 1}, "center": [1035, 285], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": {"id": "kjccf", "nodeHashId": "kjccf", "locator": "[_midscene_retrieve_task_id='kjccf']", "nodeType": "FORM_ITEM Node", "indexId": 26, "attributes": {"class": ".toggle", "type": "checkbox", "data-testid": "todo-item-toggle", "htmlTagName": "<input>", "nodeType": "FORM_ITEM Node"}, "content": "on", "rect": {"left": 515, "top": 325, "width": 40, "height": 40, "zoom": 1}, "center": [535, 345], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": {"id": "nlfin", "indexId": 27, "nodeHashId": "nlfin", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<label>"}, "center": [620, 345], "content": "Learn AI", "rect": {"left": 575, "top": 331, "width": 89, "height": 28, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": []}]}]}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": null, "children": []}]}, {"node": null, "children": [{"node": null, "children": [{"node": null, "children": [{"node": {"id": "fcnjd", "indexId": 28, "nodeHashId": "fcnjd", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [715, 395], "content": "All", "rect": {"left": 708, "top": 386, "width": 14, "height": 18, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": {"id": "ikfki", "indexId": 29, "nodeHashId": "ikfki", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [763, 395], "content": "Active", "rect": {"left": 744, "top": 386, "width": 38, "height": 18, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": {"id": "ddapc", "indexId": 30, "nodeHashId": "ddapc", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [838, 395], "content": "Completed", "rect": {"left": 803, "top": 386, "width": 69, "height": 18, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}]}, {"node": {"id": "iodmf", "indexId": 31, "nodeHashId": "iodmf", "nodeType": "BUTTON Node", "locator": "[_midscene_retrieve_task_id='iodmf']", "attributes": {"class": ".clear-completed", "htmlTagName": "<footer>", "nodeType": "BUTTON Node"}, "content": "Clear completed", "rect": {"left": 947, "top": 385, "width": 103, "height": 19, "zoom": 1}, "center": [999, 395], "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}, {"node": null, "children": [{"node": null, "children": [{"node": {"id": "i<PERSON><PERSON>", "indexId": 32, "nodeHashId": "i<PERSON><PERSON>", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<p>"}, "center": [790, 486], "content": "Double-click to edit a todo", "rect": {"left": 730, "top": 479, "width": 120, "height": 13, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": [{"node": {"id": "lbkin", "indexId": 33, "nodeHashId": "lbkin", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<p>"}, "center": [790, 508], "content": "Created by the TodoMVC Team", "rect": {"left": 718, "top": 501, "width": 144, "height": 13, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}, {"node": null, "children": [{"node": {"id": "odmdh", "indexId": 34, "nodeHashId": "odmdh", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<p>"}, "center": [766, 530], "content": "Part of", "rect": {"left": 749, "top": 523, "width": 33, "height": 13, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}, {"node": null, "children": [{"node": {"id": "ahhbb", "indexId": 35, "nodeHashId": "ahhbb", "nodeType": "TEXT Node", "locator": "", "attributes": {"nodeType": "TEXT Node", "htmlTagName": "<a>"}, "center": [806, 530], "content": "TodoMVC", "rect": {"left": 782, "top": 523, "width": 48, "height": 13, "zoom": 1}, "zoom": 1, "screenWidth": 1280, "screenHeight": 720}, "children": []}]}]}]}, {"node": null, "children": []}]}]}