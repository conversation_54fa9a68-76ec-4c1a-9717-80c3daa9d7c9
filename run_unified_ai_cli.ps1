#!/usr/bin/env pwsh
# PowerShell script to run the Unified AI CLI
# Supports Gemini, OpenAI, and Local LLMs (Ollama)

param(
    [string]$Prompt = "",
    [ValidateSet("auto", "gemini", "openai", "ollama")]
    [string]$Provider = "auto",
    [string]$Model = "",
    [switch]$ListModels,
    [switch]$Interactive,
    [ValidateSet("json", "text")]
    [string]$Format = "text",
    [switch]$Help
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Prompt = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Show-Help {
    Write-ColorOutput "🤖 Unified AI CLI - Multi-Model AI Assistant" $Colors.Info
    Write-ColorOutput ""
    Write-ColorOutput "USAGE:" $Colors.Info
    Write-ColorOutput "  .\run_unified_ai_cli.ps1 [OPTIONS] [PROMPT]"
    Write-ColorOutput ""
    Write-ColorOutput "OPTIONS:" $Colors.Info
    Write-ColorOutput "  -Prompt <text>     The prompt to send to the AI"
    Write-ColorOutput "  -Provider <name>   AI provider: auto, gemini, openai, ollama (default: auto)"
    Write-ColorOutput "  -Model <name>      Specific model to use"
    Write-ColorOutput "  -ListModels        List available models for all providers"
    Write-ColorOutput "  -Interactive       Start interactive chat mode"
    Write-ColorOutput "  -Format <type>     Output format: text, json (default: text)"
    Write-ColorOutput "  -Help              Show this help message"
    Write-ColorOutput ""
    Write-ColorOutput "EXAMPLES:" $Colors.Info
    Write-ColorOutput "  # Quick query with auto provider selection"
    Write-ColorOutput "  .\run_unified_ai_cli.ps1 'What is artificial intelligence?'"
    Write-ColorOutput ""
    Write-ColorOutput "  # Use specific provider"
    Write-ColorOutput "  .\run_unified_ai_cli.ps1 -Provider gemini -Prompt 'Explain quantum computing'"
    Write-ColorOutput ""
    Write-ColorOutput "  # Interactive mode"
    Write-ColorOutput "  .\run_unified_ai_cli.ps1 -Interactive"
    Write-ColorOutput ""
    Write-ColorOutput "  # List available models"
    Write-ColorOutput "  .\run_unified_ai_cli.ps1 -ListModels"
    Write-ColorOutput ""
    Write-ColorOutput "  # JSON output"
    Write-ColorOutput "  .\run_unified_ai_cli.ps1 -Format json -Prompt 'Hello AI'"
}

function Test-PythonEnvironment {
    try {
        # Check if we're in the correct directory
        if (-not (Test-Path ".env")) {
            Write-ColorOutput "❌ Error: .env file not found. Make sure you're in the project directory." $Colors.Error
            return $false
        }

        # Check if Python virtual environment is activated
        if (-not $env:VIRTUAL_ENV) {
            Write-ColorOutput "⚠️  Warning: No Python virtual environment detected. Activating .venvAI..." $Colors.Warning
            if (Test-Path ".venvAI\Scripts\Activate.ps1") {
                & ".venvAI\Scripts\Activate.ps1"
                Write-ColorOutput "✓ Activated .venvAI virtual environment" $Colors.Success
            } else {
                Write-ColorOutput "❌ Error: .venvAI virtual environment not found." $Colors.Error
                Write-ColorOutput "Please run: python -m venv .venvAI && .venvAI\Scripts\Activate.ps1 && pip install -r requirements.txt" $Colors.Info
                return $false
            }
        }

        # Check if required packages are installed
        $RequiredPackages = @("google-generativeai", "openai", "python-dotenv", "requests")
        foreach ($Package in $RequiredPackages) {
            try {
                python -c "import $($Package.Replace('-', '_'))" 2>$null
                if ($LASTEXITCODE -ne 0) {
                    Write-ColorOutput "❌ Missing package: $Package" $Colors.Error
                    Write-ColorOutput "Installing required packages..." $Colors.Info
                    python -m pip install google-generativeai openai python-dotenv requests
                    break
                }
            } catch {
                Write-ColorOutput "❌ Error checking package: $Package" $Colors.Error
                return $false
            }
        }

        return $true
    } catch {
        Write-ColorOutput "❌ Error setting up Python environment: $($_.Exception.Message)" $Colors.Error
        return $false
    }
}

function Test-APIKeys {
    Write-ColorOutput "🔍 Checking API keys..." $Colors.Info

    # Load .env file and check for API keys
    $EnvFile = Get-Content ".env" -ErrorAction SilentlyContinue
    $HasGemini = $false
    $HasOpenAI = $false

    foreach ($Line in $EnvFile) {
        if ($Line -match "^GEMINI_API_KEY=(.+)" -or $Line -match "^GOOGLE_AI_API_KEY=(.+)") {
            $HasGemini = $true
            Write-ColorOutput "✓ Gemini API key found" $Colors.Success
        }
        if ($Line -match "^OPENAI_API_KEY=(.+)") {
            $HasOpenAI = $true
            Write-ColorOutput "✓ OpenAI API key found" $Colors.Success
        }
    }

    if (-not $HasGemini -and -not $HasOpenAI) {
        Write-ColorOutput "⚠️  Warning: No API keys found in .env file" $Colors.Warning
        Write-ColorOutput "You can still use local Ollama models if available" $Colors.Info
    }

    # Check if Ollama is running
    try {
        $OllamaResponse = Invoke-RestMethod -Uri "http://localhost:11434/api/tags" -Method Get -TimeoutSec 5 -ErrorAction SilentlyContinue
        Write-ColorOutput "✓ Ollama is running and accessible" $Colors.Success
    } catch {
        Write-ColorOutput "⚠️  Ollama not accessible on localhost:11434" $Colors.Warning
        Write-ColorOutput "To use local LLMs, install and start Ollama from https://ollama.ai" $Colors.Info
    }
}

# Main execution
try {
    if ($Help) {
        Show-Help
        exit 0
    }

    Write-ColorOutput "🚀 Starting Unified AI CLI..." $Colors.Info
    Write-ColorOutput ""

    # Test environment
    if (-not (Test-PythonEnvironment)) {
        exit 1
    }

    # Test API keys
    Test-APIKeys
    Write-ColorOutput ""

    # Build command arguments
    $PythonArgs = @("unified_ai_cli.py")

    if ($ListModels) {
        $PythonArgs += "--list-models"
    } elseif ($Interactive) {
        $PythonArgs += "--interactive"
        if ($Provider -ne "auto") {
            $PythonArgs += "--provider", $Provider
        }
        if ($Model) {
            $PythonArgs += "--model", $Model
        }
        if ($Format -ne "text") {
            $PythonArgs += "--format", $Format
        }
    } else {
        if (-not $Prompt) {
            Write-ColorOutput "❌ Error: Prompt is required unless using -ListModels or -Interactive" $Colors.Error
            Show-Help
            exit 1
        }

        if ($Provider -ne "auto") {
            $PythonArgs += "--provider", $Provider
        }
        if ($Model) {
            $PythonArgs += "--model", $Model
        }
        if ($Format -ne "text") {
            $PythonArgs += "--format", $Format
        }
        $PythonArgs += $Prompt
    }

    # Execute the Python script
    Write-ColorOutput "🔄 Running: python $($PythonArgs -join ' ')" $Colors.Info
    Write-ColorOutput ""

    & python @PythonArgs

    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput ""
        Write-ColorOutput "✅ Command completed successfully!" $Colors.Success
    } else {
        Write-ColorOutput ""
        Write-ColorOutput "❌ Command failed with exit code: $LASTEXITCODE" $Colors.Error
        exit $LASTEXITCODE
    }

} catch {
    Write-ColorOutput "❌ Error: $($_.Exception.Message)" $Colors.Error
    Write-ColorOutput "Stack Trace: $($_.ScriptStackTrace)" $Colors.Error
    exit 1
}
