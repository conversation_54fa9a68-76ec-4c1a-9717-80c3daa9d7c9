
> @midscene/web@0.20.1 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\web-integration
> modern build -c ./modern.config.ts

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;184;252;239mM[39m[38;2;179;249;235mo[39m[38;2;173;247;231md[39m[38;2;168;244;227me[39m[38;2;163;241;223mr[39m[38;2;158;238;219mn[39m[38;2;152;236;215m.[39m[38;2;147;233;211mj[39m[38;2;142;230;207ms[39m[38;2;142;230;207m [39m[38;2;137;227;203mM[39m[38;2;132;224;199mo[39m[38;2;126;222;194md[39m[38;2;121;219;190mu[39m[38;2;116;216;186ml[39m[38;2;111;213;182me[39m[38;2;111;213;182m [39m[38;2;105;211;178mv[39m[38;2;100;208;174m2[39m[38;2;95;205;170m.[39m[38;2;90;202;166m6[39m[38;2;84;200;162m0[39m[38;2;79;197;158m.[39m[38;2;74;194;154m6[39m[38;2;74;194;154m
[39m[22m
"http" is imported by "src/playground/server.ts", but could not be resolved – treating it as an external dependency.
"node:http" is imported by "src/playground/server.ts", but could not be resolved – treating it as an external dependency.
Generated an empty chunk: "midscene-playground".
[1m[36minfo   [39m[22m Build succeed in [36m69.0s[39m
[1m[36minfo   [39m[22m Bundle generated 51 files

[1m[32mBundle Files                              Size[39m[22m
dist\types\index.d.ts                     748.0 B
dist\types\midscene-playground.d.ts       13.0 B
dist\types\ui-utils.d.ts                  1.5 KB
dist\types\puppeteer-agent-launcher.d.ts  1.4 KB
dist\types\puppeteer.d.ts                 727.0 B
dist\types\midscene-server.d.ts           1.1 KB
dist\types\playwright-report.d.ts         635.0 B
dist\types\chrome-extension.d.ts          675.0 B
dist\types\yaml.d.ts                      507.0 B
dist\types\agent.d.ts                     4.2 KB
dist\types\bridge-mode.d.ts               1.7 KB
dist\types\playwright.d.ts                4.1 KB
dist\types\playground.d.ts                513.0 B
dist\types\playwright-reporter.d.ts       86.0 B
dist\types\utils.d.ts                     579.0 B
dist\types\bridge-mode-browser.d.ts       314.0 B
dist\types\utils-399ab32c.d.ts            1.4 KB
dist\types\page-13f71254.d.ts             17.8 KB
dist\types\browser-f65b8e4e.d.ts          1.5 KB
dist\lib\index.js                         101.2 KB
dist\lib\bridge-mode.js                   92.8 KB
dist\lib\bridge-mode-browser.js           31.8 KB
dist\lib\utils.js                         9.8 KB
dist\lib\ui-utils.js                      4.2 KB
dist\lib\puppeteer.js                     93.4 KB
dist\lib\puppeteer-agent-launcher.js      99.0 KB
dist\lib\playwright.js                    99.5 KB
dist\lib\playground.js                    84.8 KB
dist\lib\midscene-playground.js           92.2 KB
dist\lib\midscene-server.js               10.4 KB
dist\lib\playwright-report.js             5.8 KB
dist\lib\playwright-reporter.js           5.8 KB
dist\lib\chrome-extension.js              105.5 KB
dist\lib\yaml.js                          16.4 KB
dist\lib\agent.js                         81.7 KB
dist\es\index.js                          97.5 KB
dist\es\bridge-mode.js                    89.1 KB
dist\es\bridge-mode-browser.js            29.9 KB
dist\es\utils.js                          7.5 KB
dist\es\ui-utils.js                       3.0 KB
dist\es\puppeteer.js                      89.9 KB
dist\es\puppeteer-agent-launcher.js       95.2 KB
dist\es\playwright.js                     95.7 KB
dist\es\playground.js                     81.1 KB
dist\es\midscene-playground.js            88.9 KB
dist\es\midscene-server.js                8.6 KB
dist\es\playwright-report.js              4.3 KB
dist\es\playwright-reporter.js            4.3 KB
dist\es\chrome-extension.js               101.5 KB
dist\es\yaml.js                           13.9 KB
dist\es\agent.js                          78.2 KB

> @midscene/web@0.20.1 postbuild C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\web-integration
> node scripts/check-exports.js

exports and typesVersions configuration is consistent and all files exist ✓
