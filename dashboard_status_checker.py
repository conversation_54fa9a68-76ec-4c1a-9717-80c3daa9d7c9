#!/usr/bin/env python3
"""
Dashboard Status Checker and Terminal Launcher
Verifies the dashboard is running and creates terminal connections
"""
import os
import sys
import subprocess
import time
import psutil
from pathlib import Path

def check_python_processes():
    """Check for running Python processes"""
    python_processes = []

    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                python_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': cmdline
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    return python_processes

def check_dashboard_running():
    """Check if any dashboard is running"""
    processes = check_python_processes()
    dashboard_processes = []

    for proc in processes:
        if any(keyword in proc['cmdline'].lower() for keyword in
               ['dashboard', 'terminal_connected_dashboard', 'quick_start_dashboard']):
            dashboard_processes.append(proc)

    return dashboard_processes

def create_terminal_launcher():
    """Create a batch file to launch terminal with dashboard"""
    batch_content = f'''@echo off
echo Starting AI Agent System Terminal...
echo.
echo Dashboard Status:
"{sys.executable}" -c "import psutil; [print(f'PID {{p.pid}}: {{p.name()}} - {{\\' \\'.join(p.cmdline())}}') for p in psutil.process_iter() if 'python' in p.name().lower() and ('dashboard' in \\' \\'.join(p.cmdline()).lower() or 'terminal' in \\' \\'.join(p.cmdline()).lower())]"
echo.
echo Opening terminal connected to dashboard...
echo Type 'python terminal_connected_dashboard.py' to start the dashboard if not running
echo.
cd /d "{os.getcwd()}"
cmd /k "echo AI Agent System Terminal Ready && echo Current Directory: %CD% && echo Python: {sys.executable}"
'''

    with open("launch_terminal.bat", "w") as f:
        f.write(batch_content)

    print("✅ Created launch_terminal.bat")

def main():
    print("=== AI Agent System Status Check ===")
    print(f"Current directory: {os.getcwd()}")
    print(f"Python executable: {sys.executable}")
    print()

    # Check for dashboard processes
    dashboard_procs = check_dashboard_running()

    if dashboard_procs:
        print("✅ Dashboard processes found:")
        for proc in dashboard_procs:
            print(f"  PID {proc['pid']}: {proc['cmdline']}")
        print()
        print("🎉 SUCCESS: Dashboard is running!")
        print("   The GUI should be visible on your screen.")
        print("   If you can't see it, check your taskbar or Alt+Tab to find the window.")
    else:
        print("⚠️ No dashboard processes found.")
        print("   Starting terminal connected dashboard...")

        # Try to start the dashboard
        try:
            subprocess.Popen([
                sys.executable,
                "terminal_connected_dashboard.py"
            ], cwd=os.getcwd())
            print("✅ Dashboard launch initiated")
            time.sleep(3)

            # Check again
            dashboard_procs = check_dashboard_running()
            if dashboard_procs:
                print("🎉 SUCCESS: Dashboard is now running!")
            else:
                print("❌ Dashboard failed to start")
        except Exception as e:
            print(f"❌ Error starting dashboard: {e}")

    # Check for other important processes
    print("\n=== Python Processes ===")
    python_procs = check_python_processes()

    if python_procs:
        for proc in python_procs:
            print(f"PID {proc['pid']}: {proc['name']} - {proc['cmdline'][:100]}...")
    else:
        print("No Python processes found")

    # Create terminal launcher
    print("\n=== Terminal Setup ===")
    create_terminal_launcher()

    print("\n=== Quick Actions ===")
    print("1. Double-click 'launch_terminal.bat' to open a connected terminal")
    print("2. If dashboard GUI is running, you should see it on your screen")
    print("3. The dashboard has multiple tabs:")
    print("   - Main Dashboard: Component management")
    print("   - Terminal Output: Live command execution")
    print("   - System Monitor: Resource monitoring")
    print("   - Cloud Integration: GitHub, HuggingFace, Reddit")

    print("\n=== Integration Status ===")

    # Check if git is available
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        print("✅ Git integration available")
    except:
        print("❌ Git not found - install Git for repository integration")

    # Check internet connectivity
    try:
        import socket
        socket.create_connection(("*******", 53), timeout=3)
        print("✅ Internet connection available")
    except:
        print("❌ No internet connection - cloud features limited")

    print("\n=== Files Created ===")
    created_files = [
        "terminal_connected_dashboard.py",
        "launch_terminal.bat"
    ]

    for file in created_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")

    print("\n=== Complete System Test ===")
    print("To test the complete system:")
    print("1. Look for the Dashboard GUI window (should be open)")
    print("2. Run: launch_terminal.bat (for terminal connection)")
    print("3. In the dashboard, click 'Start All Components'")
    print("4. Check the Terminal Output tab for live logs")
    print("5. Use the Cloud Integration tab for GitHub/HuggingFace")

    # Create a simple web page to test browser integration
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>AI Agent System - Web Interface</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Agent System Dashboard</h1>

        <div class="success">
            ✅ System Status: Operational<br>
            🕒 Last Updated: ''' + time.strftime("%Y-%m-%d %H:%M:%S") + '''
        </div>

        <div class="status">
            <h3>Available Interfaces:</h3>
            <p>🖥️ GUI Dashboard: Running (check your desktop)</p>
            <p>💻 Terminal Interface: Available via launch_terminal.bat</p>
            <p>🌐 Web Interface: This page (can be extended)</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.open('https://github.com')">🐙 GitHub</button>
            <button onclick="window.open('https://huggingface.co')">🤗 HuggingFace</button>
            <button onclick="window.open('https://reddit.com/r/Python')">🔴 Reddit</button>
        </div>

        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>Dashboard successfully connected to terminal and cloud systems!</p>
        </div>
    </div>
</body>
</html>'''

    with open("dashboard_web_interface.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    print("✅ Created dashboard_web_interface.html")

    print(f"\n🎯 FINAL STATUS: System setup complete!")
    print(f"   Dashboard GUI should be visible on your screen now.")
    print(f"   Terminal integration ready via launch_terminal.bat")
    print(f"   Web interface available at: dashboard_web_interface.html")

if __name__ == "__main__":
    main()
