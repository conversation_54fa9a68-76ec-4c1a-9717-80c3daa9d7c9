<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ system_name }} Dashboard</title>    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/dashboard.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container">
      <h1>{{ system_name }} Dashboard</h1>
      <p class="text-muted">
        Version: {{ version }} | Uptime: <span id="uptime">{{ uptime }}</span>
      </p>

      <div class="row mt-4">
        <div class="col-md-8">
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Browser View</h5>
              <div>
                <button class="btn btn-sm btn-primary" id="refreshBtn">
                  Refresh
                </button>
              </div>
            </div>
            <div class="card-body p-0">
              <div class="browser-view" id="browserView">
                <img id="browserScreenshot" src="" alt="Browser Screenshot" />
              </div>
            </div>
          </div>

          <div class="card mt-3">
            <div class="card-header">
              <h5 class="mb-0">System Logs</h5>
            </div>
            <div class="card-body p-0">
              <div class="log-window" id="logWindow">
                <!-- Log entries will be added here -->
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Agents</h5>
            </div>
            <div class="card-body">
              <div id="agentsList">
                <!-- Agent statuses will be added here -->
              </div>
            </div>
          </div>

          <div class="card mt-3">
            <div class="card-header">
              <h5 class="mb-0">Human Intervention</h5>
            </div>
            <div class="card-body">
              <div id="hitlPanel" class="d-none">
                <div class="alert alert-warning">
                  <h5>Agent needs your help</h5>
                  <p id="hitlReason"></p>
                  <div class="mb-3">
                    <label for="hitlInput" class="form-label"
                      >Your input:</label
                    >
                    <textarea
                      class="form-control"
                      id="hitlInput"
                      rows="3"
                    ></textarea>
                  </div>
                  <button class="btn btn-primary" id="hitlSubmitBtn">
                    Submit
                  </button>
                  <button class="btn btn-outline-secondary" id="hitlCancelBtn">
                    Cancel
                  </button>
                </div>
              </div>
              <div id="noHitlPanel">
                <p class="text-muted">
                  No human intervention required at the moment.
                </p>
              </div>
            </div>
          </div>

          <div class="card mt-3">
            <div class="card-header">
              <h5 class="mb-0">Actions</h5>
            </div>
            <div class="card-body">
              <button
                class="btn btn-success mb-2 w-100"
                data-bs-toggle="modal"
                data-bs-target="#newTaskModal"
              >
                New Task
              </button>
              <button class="btn btn-outline-danger mb-2 w-100" id="stopBtn">
                Stop All Agents
              </button>
              <button
                class="btn btn-outline-secondary w-100"
                id="settingsBtn"
                data-bs-toggle="modal"
                data-bs-target="#settingsModal"
              >
                Settings
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- New Task Modal -->
    <div class="modal fade" id="newTaskModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Create New Task</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form id="newTaskForm">
              <div class="mb-3">
                <label for="taskDescription" class="form-label"
                  >Task Description</label
                >
                <textarea
                  class="form-control"
                  id="taskDescription"
                  rows="4"
                  required
                ></textarea>
                <div class="form-text">
                  Describe what you want the agent to do.
                </div>
              </div>
              <div class="mb-3">
                <label for="agentSelect" class="form-label">Select Agent</label>
                <select class="form-select" id="agentSelect">
                  <option value="orchestrator">Orchestrator</option>
                  <!-- Other agents will be added dynamically -->
                </select>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" class="btn btn-primary" id="submitTaskBtn">
              Create Task
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Settings</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <form id="settingsForm">
              <div class="mb-3">
                <label for="apiKeyInput" class="form-label"
                  >Google AI API Key</label
                >
                <input type="password" class="form-control" id="apiKeyInput" />
              </div>
              <div class="mb-3">
                <label for="autoSaveCheckbox" class="form-check-label">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    id="autoSaveCheckbox"
                  />
                  Auto-save agent state
                </label>
              </div>
              <div class="mb-3">
                <label for="refreshIntervalInput" class="form-label"
                  >Screenshot Refresh Interval (ms)</label
                >
                <input
                  type="number"
                  class="form-control"
                  id="refreshIntervalInput"
                  value="1000"
                />
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" class="btn btn-primary" id="saveSettingsBtn">
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
  </body>
</html>
