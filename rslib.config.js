module.exports = {
  entry: "./src/index.js", // Adjust the entry point to your main source file
  output: {
    path: "./dist",
    filename: "index.js",
    libraryTarget: "commonjs2",
  },
  target: "node",
  lib: ["es6", "commonjs"], // Correct the lib field to be an array of strings
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: "babel-loader",
          options: {
            presets: ["@babel/preset-env"],
          },
        },
      },
    ],
  },
};
