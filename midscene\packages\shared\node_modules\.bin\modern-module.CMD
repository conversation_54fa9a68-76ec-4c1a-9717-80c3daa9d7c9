@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules\@modern-js\module-tools\bin\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules\@modern-js\module-tools\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules\@modern-js\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules\@modern-js\module-tools\bin\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules\@modern-js\module-tools\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules\@modern-js\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\@modern-js+module-tools@2.60.6_debug@4.4.0_typescript@5.8.3\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@modern-js\module-tools\bin\modern.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@modern-js\module-tools\bin\modern.js" %*
)
