"""
FastAPI dashboard launcher for the Multi-Agent AI System.

This script sets up a FastAPI application with endpoints for:
- Agent management and monitoring
- LangGraph workflow orchestration
- Browser automation with Playwright
- Abacus DeepAgent integration
- LangChain utilities

The dashboard can be exposed via Cloudflare Tunnel for secure remote access.
"""
import os
import asyncio
import uvicorn
from fastapi import Fast<PERSON><PERSON>, Depends, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from core.state_manager import StateManager
from core.agent_manager import Agent<PERSON>anager
from integrations.multi_agent_integration import MultiAgentIntegration

# Import main configuration
import config

# Set up the FastAPI app
app = FastAPI(
    title=f"{config.SYSTEM_NAME} Dashboard",
    description="Multi-Agent AI System Dashboard with LangGraph, LangChain, Abacus DeepAgent, Playwright, and WebRover",
    version=config.VERSION,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates setup
templates = Jinja2Templates(directory="templates")

# Initialize state and agent managers
state_manager = StateManager()
agent_manager = AgentManager(state_manager)

# Initialize multi-agent integration
multi_agent_integration = MultiAgentIntegration(
    state_manager=state_manager,
    agent_manager=agent_manager,
    abacus_api_key=os.environ.get("ABACUS_API_KEY"),
    abacus_base_url=os.environ.get("ABACUS_BASE_URL")
)

@app.on_event("startup")
async def startup_event():
    """Initialize components on startup."""
    # Initialize browser in non-headless mode (visible)
    await multi_agent_integration.initialize_browser(headless=False)

    # Initialize state manager
    await state_manager.initialize()

    # Initialize agent manager
    await agent_manager.start()

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    # Close browser
    await multi_agent_integration.shutdown_browser()

    # Close state manager connections
    await state_manager.close()

# Root endpoint - display dashboard
@app.get("/", response_class=HTMLResponse)
async def root():
    """Display the dashboard home page."""
    # Calculate uptime
    import time
    from datetime import datetime

    start_time = getattr(app, "start_time", time.time())
    if not hasattr(app, "start_time"):
        app.start_time = start_time

    uptime_seconds = int(time.time() - start_time)

    # Format uptime string
    days = uptime_seconds // (24 * 3600)
    uptime_seconds = uptime_seconds % (24 * 3600)
    hours = uptime_seconds // 3600
    uptime_seconds %= 3600
    minutes = uptime_seconds // 60
    seconds = uptime_seconds % 60

    uptime = ""
    if days > 0:
        uptime += f"{days}d "
    if hours > 0 or days > 0:
        uptime += f"{hours}h "
    if minutes > 0 or hours > 0 or days > 0:
        uptime += f"{minutes}m "
    uptime += f"{seconds}s"

    # Load HTML template and replace placeholders
    with open("templates/dashboard.html", "r") as file:
        html_content = file.read()

    # Replace template variables
    html_content = html_content.replace("{{ system_name }}", config.SYSTEM_NAME)
    html_content = html_content.replace("{{ version }}", config.VERSION)
    html_content = html_content.replace("{{ uptime }}", uptime)
    html_content = html_content.replace("{{ uptime_seconds }}", str(int(time.time() - start_time)))

    return html_content

# WebSocket endpoints
@app.websocket("/ws/browser-feed")
async def browser_feed_websocket(websocket: WebSocket):
    """WebSocket endpoint for browser screenshots."""
    await websocket.accept()
    try:
        interval = 1.0  # Update interval in seconds
        while True:
            # Take a screenshot
            screenshot = await multi_agent_integration.browser_actions.take_screenshot()

            # Send to client
            if screenshot:
                await websocket.send_json({"type": "screenshot", "data": screenshot})

            # Wait before next update
            await asyncio.sleep(interval)
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"WebSocket error: {e}")

@app.websocket("/ws/agent-events")
async def agent_events_websocket(websocket: WebSocket):
    """WebSocket endpoint for agent events."""
    await websocket.accept()
    try:
        # Set up a queue for agent events
        event_queue = asyncio.Queue()

        # Function to handle agent events
        async def event_listener():
            while True:
                event = await event_queue.get()
                await websocket.send_json(event)
                event_queue.task_done()

        # Start event listener task
        listener_task = asyncio.create_task(event_listener())

        # Add some sample events for testing
        await event_queue.put({"type": "log", "level": "info", "message": "WebSocket connection established"})

        # Sample agent statuses
        agents = {
            "orchestrator": {"name": "Orchestrator", "status": "Active"},
            "research_agent": {"name": "Research Agent", "status": "Idle"},
            "browser_agent": {"name": "Browser Agent", "status": "Active"},
        }

        # Send initial agent statuses
        for agent_id, agent_data in agents.items():
            await event_queue.put({
                "type": "agent_status",
                "agent_id": agent_id,
                "status": agent_data["status"]
            })

        # Keep the connection alive
        while True:
            await asyncio.sleep(30)
            await event_queue.put({"type": "heartbeat"})

    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"WebSocket error: {e}")

# API routes for agents and screenshots
@app.get("/api/agents")
async def get_agents():
    """Get all available agents."""
    # This is a placeholder - in a real implementation, you'd get this from agent_manager
    agents = {
        "orchestrator": {"name": "Orchestrator", "status": "Active"},
        "research_agent": {"name": "Research Agent", "status": "Idle"},
        "browser_agent": {"name": "Browser Agent", "status": "Active"},
    }
    return {"agents": agents}

@app.get("/api/screenshot")
async def get_screenshot():
    """Get the latest browser screenshot."""
    screenshot = await multi_agent_integration.browser_actions.take_screenshot()
    return {"screenshot": screenshot}

@app.post("/api/agents/{agent_id}/message")
async def send_message_to_agent(agent_id: str, message: dict):
    """Send a message to a specific agent."""
    # In a real implementation, you'd send this to the agent through agent_manager
    return {"status": "Message sent", "agent_id": agent_id, "message": message}

@app.post("/api/agent/hitl-continue")
async def hitl_continue(input_data: dict):
    """Continue agent execution after human intervention."""
    return {"status": "Agent continuing with human input", "input": input_data["input"]}

# Include integration router (with all integration endpoints)
app.include_router(multi_agent_integration.create_api_router())

# Expose static files
app.mount("/static", StaticFiles(directory="templates/static"), name="static")

# Additional API routes can be included here

# If running directly (not imported)
if __name__ == "__main__":
    print(f"Starting {config.SYSTEM_NAME} Dashboard...")
    print(f"Version: {config.VERSION}")

    # Get configuration from environment or defaults
    host = os.environ.get("DASHBOARD_HOST", "127.0.0.1")
    port = int(os.environ.get("DASHBOARD_PORT", "8000"))

    # Log URLs
    print(f"Dashboard will be available at: http://{host}:{port}")
    print(f"API documentation at: http://{host}:{port}/docs")

    # Start the server
    uvicorn.run("dashboard_launcher:app", host=host, port=port, reload=True)
