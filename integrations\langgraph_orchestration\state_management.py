"""
State management for LangGraph workflows.

This module provides state management functionality for LangGraph workflows,
including state persistence, checkpoint management, and state transitions.
"""
from typing import Dict, Any, List, Optional, Set, Tuple
import json
import os
from datetime import datetime
from pathlib import Path

from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
import langgraph.checkpoint as checkpoint

from core.logger import setup_logger
import config

# Set up logger
logger = setup_logger("langgraph.state_management")

class StateManagement:
    """
    State management for LangGraph workflows.

    This class provides functionality for:
    - Persisting workflow state
    - Managing checkpoints
    - Handling state transitions
    - Recovering from failures
    """

    def __init__(self, checkpoint_dir: Optional[Path] = None):
        """
        Initialize the state management system.

        Args:
            checkpoint_dir: Directory for storing checkpoints
        """
        self.checkpoint_dir = checkpoint_dir or (config.DATA_DIR / "checkpoints")
        self.checkpoint_dir.mkdir(exist_ok=True, parents=True)

        # Configure checkpoint manager
        self.checkpoint_config = checkpoint.CheckpointConfig(
            store_type="file",
            file_path=str(self.checkpoint_dir / "state_checkpoints.json"),
            wait_for_ready=True,
        )

        # Active workflow states
        self.active_states: Dict[str, Dict[str, Any]] = {}

    async def create_checkpoint(self, workflow_id: str, state: Dict[str, Any]) -> str:
        """
        Create a checkpoint for a workflow state.

        Args:
            workflow_id: ID of the workflow
            state: Current state of the workflow

        Returns:
            Checkpoint ID
        """
        checkpoint_id = f"{workflow_id}_{datetime.now().isoformat()}"

        # Convert state to serializable format if needed
        serializable_state = self._prepare_state_for_serialization(state)

        # Save to file
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"
        with open(checkpoint_file, "w") as f:
            json.dump(serializable_state, f, indent=2)

        logger.info(f"Created checkpoint {checkpoint_id} for workflow {workflow_id}")
        return checkpoint_id

    async def load_checkpoint(self, checkpoint_id: str) -> Dict[str, Any]:
        """
        Load a workflow state from a checkpoint.

        Args:
            checkpoint_id: ID of the checkpoint to load

        Returns:
            Workflow state
        """
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"

        if not checkpoint_file.exists():
            logger.error(f"Checkpoint {checkpoint_id} not found")
            raise FileNotFoundError(f"Checkpoint {checkpoint_id} not found")

        with open(checkpoint_file, "r") as f:
            state = json.load(f)

        logger.info(f"Loaded checkpoint {checkpoint_id}")
        return state

    async def list_checkpoints(self, workflow_id: Optional[str] = None) -> List[str]:
        """
        List available checkpoints.

        Args:
            workflow_id: Optional workflow ID to filter by

        Returns:
            List of checkpoint IDs
        """
        checkpoint_files = list(self.checkpoint_dir.glob("*.json"))
        checkpoint_ids = [file.stem for file in checkpoint_files]

        if workflow_id:
            checkpoint_ids = [cid for cid in checkpoint_ids if cid.startswith(workflow_id)]

        return checkpoint_ids

    async def delete_checkpoint(self, checkpoint_id: str) -> bool:
        """
        Delete a checkpoint.

        Args:
            checkpoint_id: ID of the checkpoint to delete

        Returns:
            True if successful, False otherwise
        """
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"

        if not checkpoint_file.exists():
            logger.warning(f"Checkpoint {checkpoint_id} not found, cannot delete")
            return False

        checkpoint_file.unlink()
        logger.info(f"Deleted checkpoint {checkpoint_id}")
        return True

    async def persist_state(self, workflow_id: str, state: Dict[str, Any]) -> None:
        """
        Persist the current state of a workflow.

        Args:
            workflow_id: ID of the workflow
            state: Current state of the workflow
        """
        # Update active states
        self.active_states[workflow_id] = state

        # Create checkpoint
        await self.create_checkpoint(workflow_id, state)

    async def get_active_state(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current active state of a workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Current state or None if not found
        """
        return self.active_states.get(workflow_id)

    async def list_active_workflows(self) -> List[str]:
        """
        List all active workflows.

        Returns:
            List of active workflow IDs
        """
        return list(self.active_states.keys())

    def _prepare_state_for_serialization(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare a state dictionary for serialization.

        Handles any non-serializable objects in the state by converting them
        to a serializable format.

        Args:
            state: State dictionary to prepare

        Returns:
            Serializable state dictionary
        """
        # Create a deep copy of the state to avoid modifying the original
        import copy
        serializable_state = copy.deepcopy(state)

        # Handle conversion of non-serializable objects
        # This is a simplified example - real implementation would need to handle specific types
        for key, value in serializable_state.items():
            # Convert datetime objects to ISO format
            if isinstance(value, datetime):
                serializable_state[key] = value.isoformat()

        return serializable_state

    def create_config(self, workflow_id: str) -> RunnableConfig:
        """
        Create a LangGraph runnable config with checkpoint settings.

        Args:
            workflow_id: ID of the workflow

        Returns:
            RunnableConfig for LangGraph
        """
        return {
            "configurable": {
                "checkpoint": self.checkpoint_config,
                "metadata": {"workflow_id": workflow_id}
            }
        }
