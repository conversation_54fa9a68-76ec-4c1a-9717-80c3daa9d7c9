## Start experiencing

After the configuration, you can immediately experience Midscene. There are three main tabs in the extension:

- **Action**: interact with the web page. This is also known as "Auto Planning". For example:
```
type Midscene in the search box
click the login button
```

- **Query**: extract JSON data from the web page

```
extract the user id from the page, return in \{ id: string \}
```

- **Assert**: validate the page

```
the page title is "Midscene"
```

- **Tap**: perform a single tap on the element where you want to click. This is also known as "Instant Action". 

```
the login button
```

Enjoy !

> For the different between "Auto Planning" and "Instant Action", please refer to the [API](../API.html) document.

## Want to write some code ?

After experiencing, you may want to write some code to integrate Midscene. There are multiple ways to do that. Please refer to the documents below:

* [Automate with Scripts in YAML](../automate-with-scripts-in-yaml)
