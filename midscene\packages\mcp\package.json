{"name": "@midscene/mcp", "version": "0.20.1", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "bin": "dist/index.cjs", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rslib build", "dev": "rslib build --watch", "test": "vitest run", "inspect": "node scripts/inspect.mjs", "inspect2": "mcp-inspector node ./dist/test2.cjs", "start": "node dist/index.cjs"}, "devDependencies": {"@modelcontextprotocol/inspector": "0.9.0", "@rslib/core": "^0.8.0", "@types/node": "^18.0.0", "typescript": "^5.8.3", "vitest": "3.0.5", "dotenv": "16.4.5", "@midscene/web": "workspace:*", "@midscene/report": "workspace:*", "@midscene/core": "workspace:*", "@midscene/shared": "workspace:*", "@modelcontextprotocol/sdk": "1.10.2", "zod": "3.24.3"}, "dependencies": {"puppeteer": "24.2.0"}, "license": "MIT"}