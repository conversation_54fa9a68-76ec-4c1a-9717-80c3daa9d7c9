"""
Playwright browser integration for the AI Agent System.

This module provides a browser automation class using Playwright that integrates
with the WebRover browser capabilities.
"""
import os
import asyncio
import logging
import base64
from io import BytesIO
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page, Locator, Route, Request, Response
from PIL import Image

# Import WebRover integration if available
try:
    from WebRover.backend.Browser.webrover_browser import WebRoverBrowser
    WEBROVER_AVAILABLE = True
except ImportError:
    WEBROVER_AVAILABLE = False

logger = logging.getLogger(__name__)

class PlaywrightBrowser:
    """
    Browser automation using Playwright with WebRover integration.

    This class provides browser automation capabilities using Playwright, with
    optional integration with the WebRover browser for advanced features.
    """

    def __init__(self,
                 use_webrover: bool = True,
                 headless: bool = False,
                 user_data_dir: Optional[str] = None,
                 proxy: Optional[str] = None):
        """
        Initialize the browser automation.

        Args:
            use_webrover: Whether to use WebRover browser integration
            headless: Whether to run the browser in headless mode
            user_data_dir: Path to a user data directory for persistent sessions
            proxy: Proxy server to use
        """
        self.use_webrover = use_webrover and WEBROVER_AVAILABLE
        self.headless = headless
        self.user_data_dir = user_data_dir
        self.proxy = proxy

        self._playwright = None
        self._browser = None
        self._context = None
        self._page = None
        self._webrover = None

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        """Async context manager exit."""
        await self.close()

    async def start(self):
        """Start the browser."""
        try:
            if self.use_webrover:
                await self._start_with_webrover()
            else:
                await self._start_with_playwright()
        except Exception as e:
            logger.exception(f"Failed to start browser: {e}")
            raise

    async def _start_with_webrover(self):
        """Start the browser using WebRover."""
        try:
            self._webrover = WebRoverBrowser(
                headless=self.headless,
                user_data_dir=self.user_data_dir,
                proxy=self.proxy
            )

            self._browser, self._context = await self._webrover.connect_to_chrome()

            # Use the first page or create a new one
            pages = self._context.pages
            if pages:
                self._page = pages[0]
            else:
                self._page = await self._context.new_page()

            logger.info("WebRover browser started successfully")
        except Exception as e:
            logger.exception(f"Failed to start WebRover browser: {e}")
            # Fall back to regular Playwright
            logger.info("Falling back to regular Playwright")
            self.use_webrover = False
            await self._start_with_playwright()

    async def _start_with_playwright(self):
        """Start the browser using Playwright directly."""
        try:
            self._playwright = await async_playwright().start()

            browser_type = self._playwright.chromium

            browser_kwargs = {
                "headless": self.headless
            }

            if self.proxy:
                browser_kwargs["proxy"] = {
                    "server": self.proxy
                }

            # Launch the browser
            self._browser = await browser_type.launch(**browser_kwargs)

            # Create a new context
            context_kwargs = {}

            if self.user_data_dir:
                context_kwargs["user_data_dir"] = self.user_data_dir

            self._context = await self._browser.new_context(**context_kwargs)

            # Create a new page
            self._page = await self._context.new_page()

            logger.info("Playwright browser started successfully")
        except Exception as e:
            logger.exception(f"Failed to start Playwright browser: {e}")
            raise

    async def close(self):
        """Close the browser."""
        try:
            if self._browser:
                await self._browser.close()
                self._browser = None
                self._context = None
                self._page = None

            if self._playwright:
                await self._playwright.stop()
                self._playwright = None

            logger.info("Browser closed successfully")
        except Exception as e:
            logger.exception(f"Error closing browser: {e}")

    async def goto(self, url: str) -> bool:
        """
        Navigate to a URL.

        Args:
            url: URL to navigate to

        Returns:
            Whether the navigation was successful
        """
        try:
            if not self._page:
                raise RuntimeError("Browser not started")

            await self._page.goto(url, wait_until="networkidle")
            return True
        except Exception as e:
            logger.error(f"Failed to navigate to {url}: {e}")
            return False

    async def get_current_url(self) -> str:
        """
        Get the current URL.

        Returns:
            Current URL
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        return self._page.url

    async def get_page_content(self) -> str:
        """
        Get the HTML content of the current page.

        Returns:
            HTML content
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        return await self._page.content()

    async def get_page_text(self) -> str:
        """
        Get the text content of the current page.

        Returns:
            Text content
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        # Use JavaScript to get text content
        return await self._page.evaluate("() => document.body.innerText")

    async def get_screenshot(self, full_page: bool = True) -> bytes:
        """
        Take a screenshot of the current page.

        Args:
            full_page: Whether to take a screenshot of the full page or just the viewport

        Returns:
            Screenshot as bytes
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        return await self._page.screenshot(full_page=full_page)

    async def get_screenshot_base64(self, full_page: bool = True) -> str:
        """
        Take a screenshot of the current page and return it as base64.

        Args:
            full_page: Whether to take a screenshot of the full page or just the viewport

        Returns:
            Screenshot as base64 string
        """
        screenshot = await self.get_screenshot(full_page=full_page)
        return base64.b64encode(screenshot).decode("utf-8")

    async def click(self, selector: str) -> bool:
        """
        Click on an element.

        Args:
            selector: CSS selector for the element

        Returns:
            Whether the click was successful
        """
        try:
            if not self._page:
                raise RuntimeError("Browser not started")

            await self._page.click(selector)
            return True
        except Exception as e:
            logger.error(f"Failed to click on {selector}: {e}")
            return False

    async def fill(self, selector: str, value: str) -> bool:
        """
        Fill a form field.

        Args:
            selector: CSS selector for the form field
            value: Value to fill in

        Returns:
            Whether the fill was successful
        """
        try:
            if not self._page:
                raise RuntimeError("Browser not started")

            await self._page.fill(selector, value)
            return True
        except Exception as e:
            logger.error(f"Failed to fill {selector}: {e}")
            return False

    async def wait_for_selector(self, selector: str, timeout: int = 30000) -> bool:
        """
        Wait for an element to be available.

        Args:
            selector: CSS selector for the element
            timeout: Timeout in milliseconds

        Returns:
            Whether the element was found within the timeout
        """
        try:
            if not self._page:
                raise RuntimeError("Browser not started")

            await self._page.wait_for_selector(selector, timeout=timeout)
            return True
        except Exception as e:
            logger.error(f"Failed to wait for {selector}: {e}")
            return False

    async def eval_js(self, js_code: str) -> Any:
        """
        Evaluate JavaScript code.

        Args:
            js_code: JavaScript code to evaluate

        Returns:
            Result of the evaluation
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        return await self._page.evaluate(js_code)

    async def handle_dialog(self, accept: bool = True, prompt_text: Optional[str] = None):
        """
        Configure dialog handler for alerts, confirms, and prompts.

        Args:
            accept: Whether to accept (True) or dismiss (False) dialogs
            prompt_text: Text to enter for prompt dialogs
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        await self._page.on("dialog", lambda dialog: dialog.accept(prompt_text) if accept else dialog.dismiss())

    async def get_elements(self, selector: str) -> List[Dict]:
        """
        Get information about elements matching a selector.

        Args:
            selector: CSS selector

        Returns:
            List of dictionaries with element information
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        elements = await self._page.query_selector_all(selector)
        result = []

        for i, element in enumerate(elements):
            # Get element properties
            tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
            inner_text = await element.evaluate("el => el.innerText", force_expr=True)

            # Get element attributes
            attrs = await element.evaluate("""el => {
                const attributes = el.attributes;
                const result = {};
                for (let i = 0; i < attributes.length; i++) {
                    result[attributes[i].name] = attributes[i].value;
                }
                return result;
            }""")

            # Get bounding box
            bbox = await element.bounding_box()

            result.append({
                "index": i,
                "tag_name": tag_name,
                "text": inner_text,
                "attributes": attrs,
                "bounding_box": bbox
            })

        return result

    async def scroll_to_element(self, selector: str) -> bool:
        """
        Scroll to an element.

        Args:
            selector: CSS selector for the element

        Returns:
            Whether the scroll was successful
        """
        try:
            if not self._page:
                raise RuntimeError("Browser not started")

            # Find the element
            element = await self._page.query_selector(selector)
            if not element:
                return False

            # Scroll to the element
            await element.scroll_into_view_if_needed()
            return True
        except Exception as e:
            logger.error(f"Failed to scroll to {selector}: {e}")
            return False

    async def get_page_info(self) -> Dict:
        """
        Get comprehensive information about the current page.

        Returns:
            Dictionary with page information
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        return {
            "url": self._page.url,
            "title": await self._page.title(),
            "viewport": self._page.viewport_size,
            "cookies": await self._context.cookies(),
            "headers": await self._page.evaluate("""() => {
                const req = window.performance.getEntriesByType('resource')[0];
                return req ? req.requestHeaders : {};
            }""")
        }

    async def extract_structured_data(self) -> Dict:
        """
        Extract structured data from the current page.

        Returns:
            Dictionary with structured data
        """
        if not self._page:
            raise RuntimeError("Browser not started")

        # Extract JSON-LD data
        json_ld = await self._page.evaluate("""() => {
            const elements = document.querySelectorAll('script[type="application/ld+json"]');
            const result = [];
            elements.forEach(el => {
                try {
                    result.push(JSON.parse(el.textContent));
                } catch (e) {
                    // Skip invalid JSON
                }
            });
            return result;
        }""")

        # Extract meta tags
        meta_tags = await self._page.evaluate("""() => {
            const metas = document.querySelectorAll('meta');
            const result = {};
            metas.forEach(meta => {
                if (meta.name) {
                    result[meta.name] = meta.content;
                } else if (meta.getAttribute('property')) {
                    result[meta.getAttribute('property')] = meta.content;
                }
            });
            return result;
        }""")

        return {
            "json_ld": json_ld,
            "meta_tags": meta_tags
        }
