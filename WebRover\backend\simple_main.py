from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Literal
import uvicorn

app = FastAPI(title="WebRover Backend", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class QueryRequest(BaseModel):
    query: str
    agent_type: Literal["task", "research", "deep_research"] = "task"

@app.get("/")
async def root():
    return {"message": "WebRover Backend is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "WebRover Backend"}

@app.post("/setup-browser")
async def setup_browser():
    try:
        return {"status": "success", "message": "Browser setup simulated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to setup browser: {str(e)}")

@app.post("/cleanup")
async def cleanup_browser():
    try:
        return {"status": "success", "message": "Browser cleanup simulated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cleanup browser: {str(e)}")

@app.post("/query")
async def query_agent(request: QueryRequest):
    try:
        # Simulate agent response
        response_message = f"Simulated {request.agent_type} agent response for query: {request.query}"

        return {
            "status": "success",
            "agent_type": request.agent_type,
            "query": request.query,
            "response": response_message,
            "actions_taken": [
                f"Processed {request.agent_type} query",
                "Simulated browser automation",
                "Generated response"
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query processing failed: {str(e)}")

@app.get("/models")
async def get_models():
    return {
        "models": [
            {"id": "task-agent", "name": "Task Agent", "description": "Handles web automation tasks"},
            {"id": "research-agent", "name": "Research Agent", "description": "Conducts web research"},
            {"id": "deep-research-agent", "name": "Deep Research Agent", "description": "In-depth research"}
        ]
    }

if __name__ == "__main__":
    print("🚀 Starting WebRover Backend on http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001)
