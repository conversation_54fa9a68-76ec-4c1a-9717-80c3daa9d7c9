
> @midscene/shared@0.20.1 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\shared
> npm run build:pkg && npm run build:script


> @midscene/shared@0.20.1 build:pkg
> modern build -c ./modern.config.ts

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;184;252;239mM[39m[38;2;179;249;235mo[39m[38;2;173;247;231md[39m[38;2;168;244;227me[39m[38;2;163;241;223mr[39m[38;2;158;238;219mn[39m[38;2;152;236;215m.[39m[38;2;147;233;211mj[39m[38;2;142;230;207ms[39m[38;2;142;230;207m [39m[38;2;137;227;203mM[39m[38;2;132;224;199mo[39m[38;2;126;222;194md[39m[38;2;121;219;190mu[39m[38;2;116;216;186ml[39m[38;2;111;213;182me[39m[38;2;111;213;182m [39m[38;2;105;211;178mv[39m[38;2;100;208;174m2[39m[38;2;95;205;170m.[39m[38;2;90;202;166m6[39m[38;2;84;200;162m0[39m[38;2;79;197;158m.[39m[38;2;74;194;154m6[39m[38;2;74;194;154m
[39m[22m
"node:buffer" is imported by "src/img/info.ts", but could not be resolved – treating it as an external dependency.
"node:buffer" is imported by "src/img/transform.ts", but could not be resolved – treating it as an external dependency.
Generated an empty chunk: "extractor-debug".
"node:buffer" is imported by "src/img/info.ts", but could not be resolved – treating it as an external dependency.
"node:buffer" is imported by "src/img/transform.ts", but could not be resolved – treating it as an external dependency.
Generated an empty chunk: "extractor-debug".
"node:buffer" is imported by "src/img/info.ts", but could not be resolved – treating it as an external dependency.
"node:buffer" is imported by "src/img/transform.ts", but could not be resolved – treating it as an external dependency.
Generated an empty chunk: "extractor-debug".
[1m[36minfo   [39m[22m Build succeed in [36m48.7s[39m
[1m[36minfo   [39m[22m Bundle generated 63 files

[1m[32mBundle Files                        Size[39m[22m
dist\lib\extractor-debug.d.ts       13.0 B
dist\lib\index.d.ts                 61.0 B
dist\lib\fs.d.ts                    627.0 B
dist\lib\utils.d.ts                 1.1 KB
dist\lib\logger.d.ts                277.0 B
dist\lib\common.d.ts                659.0 B
dist\lib\us-keyboard-layout.d.ts    3.1 KB
dist\lib\env.d.ts                   6.3 KB
dist\lib\constants.d.ts             4.3 KB
dist\lib\img.d.ts                   6.1 KB
dist\lib\extractor.d.ts             457.0 B
dist\lib\types.d.ts                 158.0 B
dist\lib\index-59587d83.d.ts        3.5 KB
dist\es\extractor-debug.d.ts        13.0 B
dist\es\index.d.ts                  61.0 B
dist\es\fs.d.ts                     627.0 B
dist\es\utils.d.ts                  1.1 KB
dist\es\logger.d.ts                 277.0 B
dist\es\common.d.ts                 659.0 B
dist\es\us-keyboard-layout.d.ts     3.1 KB
dist\es\env.d.ts                    6.3 KB
dist\es\constants.d.ts              4.3 KB
dist\es\img.d.ts                    6.1 KB
dist\es\extractor.d.ts              457.0 B
dist\es\types.d.ts                  158.0 B
dist\es\index-59587d83.d.ts         3.5 KB
dist\types\extractor-debug.d.ts     13.0 B
dist\types\index.d.ts               61.0 B
dist\types\fs.d.ts                  627.0 B
dist\types\utils.d.ts               1.1 KB
dist\types\logger.d.ts              277.0 B
dist\types\common.d.ts              659.0 B
dist\types\us-keyboard-layout.d.ts  3.1 KB
dist\types\env.d.ts                 6.3 KB
dist\types\constants.d.ts           4.3 KB
dist\types\img.d.ts                 6.1 KB
dist\types\extractor.d.ts           457.0 B
dist\types\types.d.ts               158.0 B
dist\types\index-59587d83.d.ts      3.5 KB
dist\es\index.js                    75.0 B
dist\es\img.js                      16.9 KB
dist\es\constants.js                4.3 KB
dist\es\extractor.js                28.7 KB
dist\es\extractor-debug.js          21.4 KB
dist\es\fs.js                       2.3 KB
dist\es\utils.js                    2.1 KB
dist\es\logger.js                   8.4 KB
dist\es\common.js                   6.6 KB
dist\es\us-keyboard-layout.js       16.8 KB
dist\es\env.js                      9.8 KB
dist\es\types.js                    77.0 B
dist\lib\index.js                   961.0 B
dist\lib\img.js                     19.7 KB
dist\lib\constants.js               6.0 KB
dist\lib\extractor.js               30.3 KB
dist\lib\extractor-debug.js         21.5 KB
dist\lib\fs.js                      4.2 KB
dist\lib\utils.js                   3.4 KB
dist\lib\logger.js                  10.5 KB
dist\lib\common.js                  8.6 KB
dist\lib\us-keyboard-layout.js      17.9 KB
dist\lib\env.js                     13.3 KB
dist\lib\types.js                   1.1 KB

> @midscene/shared@0.20.1 build:script
> modern build -c ./modern.inspect.config.ts

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;184;252;239mM[39m[38;2;179;249;235mo[39m[38;2;173;247;231md[39m[38;2;168;244;227me[39m[38;2;163;241;223mr[39m[38;2;158;238;219mn[39m[38;2;152;236;215m.[39m[38;2;147;233;211mj[39m[38;2;142;230;207ms[39m[38;2;142;230;207m [39m[38;2;137;227;203mM[39m[38;2;132;224;199mo[39m[38;2;126;222;194md[39m[38;2;121;219;190mu[39m[38;2;116;216;186ml[39m[38;2;111;213;182me[39m[38;2;111;213;182m [39m[38;2;105;211;178mv[39m[38;2;100;208;174m2[39m[38;2;95;205;170m.[39m[38;2;90;202;166m6[39m[38;2;84;200;162m0[39m[38;2;79;197;158m.[39m[38;2;74;194;154m6[39m[38;2;74;194;154m
[39m[22m
[1m[36minfo   [39m[22m Build succeed in [36m0.2s[39m
[1m[36minfo   [39m[22m Bundle generated 2 files

[1m[32mBundle Files                     Size[39m[22m
dist\script\htmlElement.js       55.4 KB
dist\script\htmlElementDebug.js  46.8 KB
