
> @midscene/recorder@0.20.1 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\recorder
> rslib build

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;179;249;235mR[39m[38;2;168;244;227ms[39m[38;2;158;238;219ml[39m[38;2;147;233;211mi[39m[38;2;137;227;203mb[39m[38;2;137;227;203m [39m[38;2;126;222;194mv[39m[38;2;116;216;186m0[39m[38;2;105;211;178m.[39m[38;2;95;205;170m8[39m[38;2;84;200;162m.[39m[38;2;74;194;154m0[39m[38;2;74;194;154m
[39m[22m
[1m[32mready  [39m[22m built in [1m0.47[22m s[90m (esm)[39m
[1m[32mready  [39m[22m built in [1m0.45[22m s[90m (iife)[39m
[1m[36mstart  [39m[22m generating declaration files... [90m(esm)[39m
[1m[32mready  [39m[22m declaration files generated in [1m7.70[22m s [90m(esm)[39m

[34mFile (iife)             Size      Gzip   [39m
[2mdist\[22m[36mrecorder-iife.js[39m   12.8 kB   [32m2.6 kB[39m


[34mFile (esm)                    Size      Gzip   [39m
[2mdist\[22m[36mrecorder-iife-index.js[39m   0.17 kB   [32m0.12 kB[39m
[2mdist\[22m[33mRecordTimeline.css[39m       0.46 kB   [32m0.22 kB[39m
[2mdist\[22m[33mbutton.css[39m               0.52 kB   [32m0.26 kB[39m
[2mdist\[22m[36mButton.js[39m                0.66 kB   [32m0.37 kB[39m
[2mdist\[22m[36mindex.js[39m                 0.90 kB   [32m0.27 kB[39m
[2mdist\[22m[36mrecorder.js[39m              12.3 kB   [32m2.7 kB[39m
[2mdist\[22m[36mRecordTimeline.js[39m        48.8 kB   [32m3.8 kB[39m

                     [35mTotal:[39m   63.8 kB   [32m7.7 kB[39m

