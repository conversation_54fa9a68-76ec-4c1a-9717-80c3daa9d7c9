#!/usr/bin/env python3
"""
OpenAI Operator-Style Browser AI - Simplified Version
Integrates with local AI agent system and provides exact Operator functionality
"""

import sys
import os
import subprocess
import requests
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class LocalSystemConnector:
    """Connects to the user's existing local AI agent system"""
    
    def __init__(self):
        self.connections = {}
        self.initialize_connections()
    
    def initialize_connections(self):
        """Initialize connections to local AI agent system"""
        print("🔗 Connecting to local AI agent system...")
        
        # Test WebRover connection
        try:
            response = requests.get("http://localhost:8000/health", timeout=3)
            if response.status_code == 200:
                self.connections["webrover"] = "http://localhost:8000"
                print("✅ Connected to WebRover")
        except:
            print("⚠️ WebRover not available")
        
        # Test UI-TARS connection
        try:
            response = requests.get("http://localhost:8080/health", timeout=3)
            if response.status_code == 200:
                self.connections["ui_tars"] = "http://localhost:8080"
                print("✅ Connected to UI-TARS")
        except:
            print("⚠️ UI-TARS not available")
        
        # Test Ollama connection
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if response.status_code == 200:
                self.connections["ollama"] = "http://localhost:11434"
                print("✅ Connected to Ollama")
        except:
            print("⚠️ Ollama not available")
        
        print(f"📊 Connected to {len(self.connections)} services")
    
    def chat_with_llm(self, message: str, context: str = "") -> str:
        """Chat with local LLM"""
        if "ollama" not in self.connections:
            return "❌ No local LLM available. Please start Ollama."
        
        try:
            payload = {
                "model": "llama3.2",  # Default model
                "prompt": f"Context: {context}\n\nUser: {message}\n\nAssistant:",
                "stream": False
            }
            
            response = requests.post(
                f"{self.connections['ollama']}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "No response from LLM")
            else:
                return f"❌ LLM Error: {response.status_code}"
                
        except Exception as e:
            return f"❌ LLM Error: {str(e)}"
    
    def execute_browser_automation(self, task: str) -> Dict[str, Any]:
        """Execute browser automation through available services"""
        if "webrover" in self.connections:
            return self.execute_webrover_task(task)
        elif "ui_tars" in self.connections:
            return self.execute_ui_tars_task(task)
        else:
            return {"error": "No browser automation service available", "message": "Please start WebRover or UI-TARS"}
    
    def execute_webrover_task(self, task: str) -> Dict[str, Any]:
        """Execute task through WebRover"""
        try:
            payload = {
                "query": task,
                "agent_type": "task"
            }
            response = requests.post(f"{self.connections['webrover']}/query", json=payload, timeout=30)
            if response.status_code == 200:
                return {"success": True, "result": response.json()}
            else:
                return {"error": f"WebRover error: {response.status_code}"}
        except Exception as e:
            return {"error": f"WebRover execution failed: {str(e)}"}
    
    def execute_ui_tars_task(self, task: str) -> Dict[str, Any]:
        """Execute task through UI-TARS"""
        try:
            payload = {
                "action": task,
                "type": "browser_automation"
            }
            response = requests.post(f"{self.connections['ui_tars']}/execute", json=payload, timeout=30)
            if response.status_code == 200:
                return {"success": True, "result": response.json()}
            else:
                return {"error": f"UI-TARS error: {response.status_code}"}
        except Exception as e:
            return {"error": f"UI-TARS execution failed: {str(e)}"}

class OperatorBrowser:
    """OpenAI Operator-like browser controller"""
    
    def __init__(self):
        self.connector = LocalSystemConnector()
        self.current_task = None
        self.task_steps = []
        self.step_index = 0
        self.user_control = False
        self.paused = False
        
    def start_task(self, user_input: str) -> Dict[str, Any]:
        """Start a task exactly like OpenAI Operator"""
        print(f"\n🤖 **Starting Task:** {user_input}")
        
        self.current_task = user_input
        self.step_index = 0
        self.user_control = False
        self.paused = False
        
        # Use local LLM to plan the task
        planning_prompt = f"""
        You are an AI browser automation agent like OpenAI's Operator. 
        Break down this user request into specific, actionable browser automation steps:
        
        User Request: {user_input}
        
        Provide a numbered step-by-step plan that can be executed through browser automation.
        Each step should be a single, clear action.
        """
        
        print("🧠 Planning task with local LLM...")
        llm_response = self.connector.chat_with_llm(planning_prompt)
        
        # Parse the response into steps
        self.task_steps = self.parse_steps(llm_response)
        
        print(f"\n📋 **Execution Plan ({len(self.task_steps)} steps):**")
        for i, step in enumerate(self.task_steps, 1):
            print(f"{i}. {step}")
        
        print(f"\n🎮 **Control Options (Just Like Operator):**")
        print("- Type 'next' - Execute one step at a time")
        print("- Type 'takeover' - You control, I assist")
        print("- Type 'auto' - Run all steps automatically")
        print("- Type 'pause' - Stop and wait for instructions")
        
        return {
            "task": user_input,
            "steps": self.task_steps,
            "total_steps": len(self.task_steps),
            "status": "ready"
        }
    
    def parse_steps(self, llm_response: str) -> List[str]:
        """Parse LLM response into actionable steps"""
        lines = llm_response.split('\n')
        steps = []
        
        for line in lines:
            line = line.strip()
            # Look for numbered steps or bullet points
            if (line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or 
                line.startswith('-') or line.startswith('•')):
                # Clean up the step
                step = line.lstrip('123456789.-• ').strip()
                if step and len(step) > 5:  # Ignore very short steps
                    steps.append(step)
        
        # If no structured steps found, use the whole response as one step
        if not steps:
            steps = [llm_response.strip()]
        
        return steps
    
    def execute_next_step(self) -> Dict[str, Any]:
        """Execute the next step like Operator"""
        if self.paused:
            return {"status": "paused", "message": "⏸️ Automation is paused. Type 'resume' to continue."}
        
        if self.user_control:
            return {"status": "user_control", "message": "🎮 User has control. Type 'resume' when ready."}
        
        if self.step_index >= len(self.task_steps):
            return {"status": "completed", "message": "✅ Task completed successfully!"}
        
        current_step = self.task_steps[self.step_index]
        
        print(f"\n▶️ **Step {self.step_index + 1}/{len(self.task_steps)}:** {current_step}")
        
        # Execute the step through the agent system
        result = self.connector.execute_browser_automation(current_step)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"✅ Step completed")
        
        self.step_index += 1
        progress = (self.step_index / len(self.task_steps)) * 100
        
        print(f"📊 Progress: {progress:.1f}% ({self.step_index}/{len(self.task_steps)} steps)")
        
        return {
            "step": current_step,
            "step_number": self.step_index,
            "total_steps": len(self.task_steps),
            "result": result,
            "status": "in_progress" if self.step_index < len(self.task_steps) else "completed",
            "progress": progress
        }
    
    def takeover_control(self) -> str:
        """User takes control like in Operator"""
        self.user_control = True
        self.paused = True
        return """🎮 **USER CONTROL ACTIVATED**

You now have full control of the browser! 

**What I'll do:**
- ✅ Monitor your actions
- ✅ Provide assistance when asked
- ✅ Remember context for when you hand back control
- ✅ Continue the task from where you left off

**To resume automation:** Type 'resume'
"""
    
    def resume_control(self) -> str:
        """Resume AI control like in Operator"""
        self.user_control = False
        self.paused = False
        return """🤖 **AI CONTROL RESUMED**

I'm back in control and will continue the automation!

**Current Status:**
- ✅ Resuming from where we left off
- ✅ All context preserved
- ✅ Ready to execute remaining steps

Type 'next' for next step or 'auto' to continue automatically.
"""
    
    def get_status(self) -> str:
        """Get current status like Operator"""
        if not self.current_task:
            return """📊 **Status: Ready**

No active automation task.
Ready to help with browser automation!

**Connected Services:**
""" + self.format_connections()
        
        return f"""📊 **Automation Status**

**Current Task:** {self.current_task}
**Progress:** {self.step_index}/{len(self.task_steps)} steps ({(self.step_index/len(self.task_steps)*100):.1f}%)
**Remaining:** {len(self.task_steps) - self.step_index} steps

**Control Status:**
- User Control: {"✅ Active" if self.user_control else "❌ AI Control"}
- Automation: {"⏸️ Paused" if self.paused else "▶️ Running"}

**Connected Services:**
""" + self.format_connections()
    
    def format_connections(self) -> str:
        """Format service connection status"""
        status = ""
        for service, url in self.connector.connections.items():
            status += f"✅ {service.replace('_', ' ').title()}: {url}\n"
        
        if not self.connector.connections:
            status = "❌ No services connected\n"
        
        return status

def main():
    """Main function - Command line interface like Operator"""
    print("🤖 **OpenAI Operator-Style Browser AI**")
    print("**Exact same features as OpenAI's Operator • Connected to your local AI agent system**")
    print("=" * 80)
    
    operator = OperatorBrowser()
    
    print("\n✅ **Ready!** Describe what you want to accomplish:")
    print("Examples:")
    print("- 'Help me book a flight to New York'")
    print("- 'Find and order office supplies online'")
    print("- 'Research competitors and compile a report'")
    print("- 'Schedule a meeting through my calendar'")
    
    while True:
        try:
            user_input = input("\n🎯 You: ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            # Handle control commands
            if user_input.lower() in ['takeover', 'take control']:
                print(operator.takeover_control())
                continue
            
            if user_input.lower() in ['resume', 'continue']:
                print(operator.resume_control())
                continue
            
            if user_input.lower() in ['status', 'progress']:
                print(operator.get_status())
                continue
            
            if user_input.lower() in ['next', 'next step']:
                result = operator.execute_next_step()
                print(result.get("message", "Step executed"))
                continue
            
            if user_input.lower() in ['pause', 'stop']:
                operator.paused = True
                print("⏸️ **AUTOMATION PAUSED**")
                continue
            
            if user_input.lower() == 'auto':
                print("🤖 **AUTO MODE:** Executing all remaining steps...")
                while operator.step_index < len(operator.task_steps) and not operator.paused:
                    result = operator.execute_next_step()
                    if result["status"] == "completed":
                        print(result["message"])
                        break
                    time.sleep(2)  # Brief pause between steps
                continue
            
            # Start new task
            operator.start_task(user_input)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
