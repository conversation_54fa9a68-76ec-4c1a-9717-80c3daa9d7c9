
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <link rel="stylesheet" href="/static/dashboard.css">
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#1a1a1a">
</head>
<body>
    <div id="app">
        <header class="header">
            <h1>🤖 Unified AI Control Hub</h1>
            <div class="header-controls">
                <button id="voice-toggle" class="btn-toggle">🎤 Voice</button>
                <button id="vision-toggle" class="btn-toggle">👁️ Vision</button>
                <div class="system-stats">
                    <span id="cpu-usage">CPU: --</span>
                    <span id="memory-usage">RAM: --</span>
                    <span id="agents-count">Agents: --</span>
                </div>
            </div>
        </header>

        <main class="main">
            <div class="dashboard-grid">
                <!-- Agent Control Panel -->
                <section class="panel agent-panel">
                    <h2>🎮 Agent Control</h2>
                    <div id="agents-grid" class="agents-grid">
                        <!-- Agents will be populated here -->
                    </div>
                </section>

                <!-- Browser Control Panel -->
                <section class="panel browser-panel">
                    <h2>🌐 Browser Control</h2>
                    <div class="browser-controls">
                        <input type="url" id="browser-url" placeholder="Enter URL..." value="https://google.com">
                        <button onclick="navigateTo()">Navigate</button>
                        <button onclick="takeScreenshot()">Screenshot</button>
                    </div>
                    <div id="browser-view" class="browser-view">
                        <!-- Browser preview will be shown here -->
                    </div>
                </section>

                <!-- System Monitor Panel -->
                <section class="panel monitor-panel">
                    <h2>📊 System Monitor</h2>
                    <div id="system-charts">
                        <div class="chart-container">
                            <canvas id="cpu-chart"></canvas>
                        </div>
                        <div class="chart-container">
                            <canvas id="memory-chart"></canvas>
                        </div>
                    </div>
                </section>

                <!-- Live Console Panel -->
                <section class="panel console-panel">
                    <h2>📺 Live Console</h2>
                    <div id="console-output" class="console-output"></div>
                    <div class="console-input">
                        <input type="text" id="console-command" placeholder="Enter command...">
                        <button onclick="executeCommand()">Execute</button>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script src="/static/dashboard.js"></script>
</body>
</html>
        