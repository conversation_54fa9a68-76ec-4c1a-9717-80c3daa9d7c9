.DS_Store

.pnp
.pnp.js
.evn.local
.env.*.local
.history
.rts*
*.log*
*.pid
*.pid.*
*.report
*.lcov
lib-cov

doc_build/
node_modules/
.npm
.lock-wscript
.yarn-integrity
.node_repl_history
.nyc_output
*.tsbuildinfo
.eslintcache
.sonarlint

coverage/
release/
output/
output_resource/

.vscode/**/*
!.vscode/settings.json
!.vscode/extensions.json
.idea/

**/*/api/typings/auto-generated
**/*/adapters/**/index.ts
**/*/adapters/**/index.js

src/
midscene_run/
log/
docs/
tests/