{"name": "@midscene/recorder", "version": "0.20.1", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rslib build", "dev": "rslib build --watch"}, "devDependencies": {"@rsbuild/plugin-react": "^1.3.1", "@rslib/core": "^0.8.0", "@types/react": "^18.3.1", "react": "18.3.1", "typescript": "^5.8.3"}, "dependencies": {"@ant-design/icons": "^5.3.1", "antd": "^5.21.6", "dayjs": "^1.11.11", "react-dom": "18.3.1", "@midscene/shared": "workspace:*"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}}