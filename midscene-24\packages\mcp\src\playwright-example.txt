// examples:
// Reference the following code to generate Midscene test cases
// The following is test code for Midscene AI, for reference
// The following is Playwright syntax, you can use <PERSON><PERSON> to assist in test generation
import { test as base } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { PlaywrightAiFixture } from '@midscene/web/playwright';

export const test = base.extend<PlayWrightAiFixtureType>(PlaywrightAiFixture({
  waitForNetworkIdleTimeout: 2000, // optional, the timeout for waiting for network idle between each action, default is 2000ms
  }));


test.beforeEach(async ({ page }) => {
  await page.goto('https://www.xxx.com/');
  await page.setViewportSize({ width: 1920, height: 1080 });
});

test('ai shop', async ({
  aiInput,
  aiAssert,
  aiQuery,
  aiKeyboardPress,
  aiHover,
  aiTap,
  agentForPage,
  page,
}) => {
  // login
  await aiAssert('The page shows the login interface');
  await aiInput('user_name', 'in user name input');
  await aiInput('password', 'in password input');
  await aiKeyboardPress('Enter', 'Login Button');

  // check the login success
  await aiWaitFor('The page shows that the loading is complete');
  await aiAssert('The current page shows the product detail page');

  // check the product info
  const dataA = await aiQuery({
    userInfo: 'User information in the format {name: string}',
    theFirstProductInfo: 'The first product info in the format {name: string, price: number}',
  });
  expect(dataA.theFirstProductInfo.name).toBe('xxx');
  expect(dataA.theFirstProductInfo.price).toBe(100);


  // add to cart
  await aiTap('click add to cart button');
  
  await aiTap('click right top cart icon');
  await aiAssert('The cart icon shows the number 1');
});