// Copyright 2016 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.!

#include "init.h"
#include "testharness.h"

#ifdef OS_WIN
ABSL_FLAG(std::string, test_srcdir, "..\\data", "Data directory.");
#else
ABSL_FLAG(std::string, test_srcdir, "../data", "Data directory.");
#endif

ABSL_FLAG(std::string, test_tmpdir, "test_tmp", "Temporary directory.");

int main(int argc, char **argv) {
  sentencepiece::ScopedResourceDestructor cleaner;
  sentencepiece::ParseCommandLineFlags(argv[0], &argc, &argv, true);
  sentencepiece::test::RunAllTests();
  return 0;
}
