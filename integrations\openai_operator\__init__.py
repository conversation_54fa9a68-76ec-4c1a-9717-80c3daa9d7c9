"""
OpenAI operator integration for the AI Agent System.
This module provides a client for the OpenAI API with extended capabilities for agent operations.
"""
import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable, Union, Generator
import openai
from openai import OpenAI, AsyncOpenAI

# Set up logger
logger = logging.getLogger("openai_operator")

class OpenAIOperator:
    """Enhanced OpenAI client with operator functionality for AI agents."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the OpenAI operator.

        Args:
            api_key: OpenAI API key. If not provided, uses OPENAI_API_KEY environment variable.
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")

        if not self.api_key:
            raise ValueError("OpenAI API key not provided and not found in environment variables")

        self.client = OpenAI(api_key=self.api_key)
        self.async_client = AsyncOpenAI(api_key=self.api_key)

        # Default models
        self.default_chat_model = "gpt-4o"
        self.default_embedding_model = "text-embedding-ada-002"

        # Tool definitions
        self.available_tools = self._get_default_tools()

        # Function call handlers
        self.function_handlers = {}

    def _get_default_tools(self) -> List[Dict[str, Any]]:
        """Get the default tools available to the operator."""
        return [
            {
                "type": "function",
                "function": {
                    "name": "search_web",
                    "description": "Search the web for information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query"
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_current_time",
                    "description": "Get the current time",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            }
        ]

    def register_tool(self, tool_definition: Dict[str, Any]):
        """
        Register a new tool with the operator.

        Args:
            tool_definition: Tool definition in the OpenAI format
        """
        self.available_tools.append(tool_definition)

    def register_function_handler(self, function_name: str, handler: Callable):
        """
        Register a handler for a function call.

        Args:
            function_name: Name of the function
            handler: Function to call when the function is called
        """
        self.function_handlers[function_name] = handler

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Union[Dict[str, Any], Generator]:
        """
        Send a chat completion request to OpenAI.

        Args:
            messages: List of message objects
            model: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            tools: List of tools to make available
            stream: Whether to stream the response

        Returns:
            The response from OpenAI
        """
        model = model or self.default_chat_model
        tools = tools or self.available_tools

        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                tools=tools,
                stream=stream
            )

            if stream:
                return response
            else:
                return response.model_dump()

        except Exception as e:
            logger.exception(f"Error in chat completion: {e}")
            raise

    async def async_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        Send a chat completion request to OpenAI asynchronously.

        Args:
            messages: List of message objects
            model: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            tools: List of tools to make available
            stream: Whether to stream the response

        Returns:
            The response from OpenAI
        """
        model = model or self.default_chat_model
        tools = tools or self.available_tools

        try:
            response = await self.async_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                tools=tools,
                stream=stream
            )

            if stream:
                return response
            else:
                return response.model_dump()

        except Exception as e:
            logger.exception(f"Error in async chat completion: {e}")
            raise

    def handle_function_calls(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle function calls in a response.

        Args:
            response: Response from OpenAI

        Returns:
            The response with handled function calls
        """
        if "choices" not in response:
            return response

        choice = response["choices"][0]
        message = choice.get("message", {})

        # Check for tool calls
        tool_calls = message.get("tool_calls", [])

        if not tool_calls:
            return response

        # Handle each tool call
        for tool_call in tool_calls:
            if tool_call["type"] != "function":
                continue

            function = tool_call["function"]
            function_name = function["name"]

            if function_name not in self.function_handlers:
                logger.warning(f"No handler for function {function_name}")
                continue

            # Parse the arguments
            try:
                arguments = json.loads(function["arguments"])
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON in function arguments: {function['arguments']}")
                continue

            # Call the handler
            try:
                result = self.function_handlers[function_name](**arguments)

                # Add the result to the messages
                response["messages"].append({
                    "role": "function",
                    "name": function_name,
                    "content": json.dumps(result)
                })

            except Exception as e:
                logger.exception(f"Error calling function {function_name}: {e}")

                # Add an error result
                response["messages"].append({
                    "role": "function",
                    "name": function_name,
                    "content": json.dumps({"error": str(e)})
                })

        return response

    def embeddings(self, texts: List[str], model: Optional[str] = None) -> Dict[str, Any]:
        """
        Get embeddings for texts.

        Args:
            texts: List of texts to get embeddings for
            model: Model to use

        Returns:
            Embeddings response
        """
        model = model or self.default_embedding_model

        try:
            response = self.client.embeddings.create(
                model=model,
                input=texts
            )

            return response.model_dump()

        except Exception as e:
            logger.exception(f"Error in embeddings: {e}")
            raise

    async def async_embeddings(self, texts: List[str], model: Optional[str] = None) -> Dict[str, Any]:
        """
        Get embeddings for texts asynchronously.

        Args:
            texts: List of texts to get embeddings for
            model: Model to use

        Returns:
            Embeddings response
        """
        model = model or self.default_embedding_model

        try:
            response = await self.async_client.embeddings.create(
                model=model,
                input=texts
            )

            return response.model_dump()

        except Exception as e:
            logger.exception(f"Error in async embeddings: {e}")
            raise
