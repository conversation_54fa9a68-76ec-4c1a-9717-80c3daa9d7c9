
import subprocess
import sys
from mem0_memory import add_memory

def main():
    print("Starting chat with memory. Type 'exit' to end.")
    while True:
        user_input = input("You: ")
        if user_input.lower() == 'exit':
            break

        # This is a placeholder for interacting with the Gemini agent.
        # In a real implementation, this would be a call to the Gemini API.
        # For now, we'll just echo the input as the agent's response.
        agent_response = f"Echo: {user_input}"
        print(f"Agent: {agent_response}")

        # Add the conversation to memory
        add_memory(user_input, agent_response)

if __name__ == "__main__":
    main()
