# AI Agent System with Deep Agent and Cloudflare Tunnel

This system integrates Deep Agent capabilities with your existing AI Agent System and exposes the dashboard via a secure Cloudflare tunnel.

## Setup

1. Make sure you have installed the Cloudflare tunnel client (`cloudflared`). The MSI installer is included in this repository.

2. Set up your environment variables:

   ```
   DEEP_AGENT_API_KEY=your_deep_agent_api_key
   CLOUDFLARE_TUNNEL_NAME=your-custom-tunnel-name (optional, defaults to "ai-agent-dashboard")
   ```

3. Ensure all required packages are installed:

   ```
   pip install -r requirements.txt
   ```

## Running the Dashboard

You can start the dashboard with Cloudflare tunneling using:

```
python launch_dashboard_with_tunnel.py
```

Or simply run:

```
start_dashboard.bat
```

The system will:

1. Start the AI Agent System dashboard
2. Create a Cloudflare tunnel if it doesn't exist
3. Run the tunnel to expose your dashboard securely
4. Display the public URL where your dashboard is accessible

## Deep Agent Integration

The system now includes Deep Agent integration with the following capabilities:

- Create, list, and manage Deep Agents
- Run agents with custom input
- Add tools to agents
- Train agents with examples

### REST API Endpoints

The following new endpoints are available:

- `GET /api/deep-agents` - List all Deep Agents
- `POST /api/deep-agents` - Create a new Deep Agent
- `GET /api/deep-agents/{agent_id}` - Get information about a specific Deep Agent
- `DELETE /api/deep-agents/{agent_id}` - Delete a Deep Agent
- `POST /api/deep-agents/{agent_id}/run` - Run a Deep Agent with given input
- `POST /api/deep-agents/{agent_id}/tools` - Add a tool to a Deep Agent
- `POST /api/deep-agents/{agent_id}/train` - Train a Deep Agent with examples

## Google Cloud Integration

The system also includes Google Cloud integration:

- `GET /api/gcp/buckets` - List all Google Cloud Storage buckets
- `POST /api/gcp/upload` - Upload a file to Google Cloud Storage
- `POST /api/gcp/download` - Download a file from Google Cloud Storage
- `POST /api/gcp/files` - List files in a Google Cloud Storage bucket
