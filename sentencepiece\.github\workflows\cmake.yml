name: CI for general build

on:
  push:
    branches: [ master ]
    tags:
      - 'v*'
  pull_request:
    branches: [ master ]

permissions:
  contents: read

jobs:
  build:
    strategy:
      matrix:
        os: [ ubuntu-latest, ubuntu-20.04, windows-latest, macOS-11 ]
        arch: [ x64 ]
        include:
          - os: windows-latest
            arch: x86
    runs-on: ${{ matrix.os }}

    permissions:
      contents: write # svenstaro/upload-release-action 

    steps:
    - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
    - uses: actions/setup-python@39cd14951b08e74b54015e9e001cdefcf80e669f # v5.1.1
      with:
        python-version: '3.x'
        architecture: ${{matrix.arch}}

    - name: Config for Windows
      if: runner.os == 'Windows'
      run: |
        if ("${{matrix.arch}}" -eq "x64") {
          $msbuildPlatform = "x64"
        } else {
          $msbuildPlatform = "Win32"
        }
        cmake -A $msbuildPlatform -B ${{github.workspace}}/build -DSPM_BUILD_TEST=ON -DSPM_ENABLE_SHARED=OFF -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root

    - name: Config for Unix
      if: runner.os != 'Windows'
      run: cmake -B ${{github.workspace}}/build -DSPM_BUILD_TEST=ON -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root
      env:
        CMAKE_OSX_ARCHITECTURES: arm64;x86_64

    - name: Build
      run: cmake --build ${{github.workspace}}/build --config Release --target install --parallel 8

    - name: Test
      working-directory: ${{github.workspace}}/build
      run: ctest -C Release --output-on-failure

    - name: Package
      working-directory: ${{github.workspace}}/build
      run: cpack

    - name: Build Python wrapper
      working-directory: ${{github.workspace}}/python
      run: |
        python -m pip install --require-hashes --no-dependencies -r ../.github/workflows/requirements/base.txt
        python setup.py build
        python setup.py bdist_wheel
        python -m pytest

    - name: Upload artifcacts
      uses: actions/upload-artifact@v3
      with:
        name: artifcacts
        path: ./build/*.7z

    - name: Upload Release Assets
      if: startsWith(github.ref, 'refs/tags/')
      uses: svenstaro/upload-release-action@04733e069f2d7f7f0b4aebc4fbdbce8613b03ccd # v2.9.0
      with:
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        file: ./build/*.7z
        tag: ${{ github.ref }}
        overwrite: true
        prerelease: true
        file_glob: true
        body: "This is my release text"
