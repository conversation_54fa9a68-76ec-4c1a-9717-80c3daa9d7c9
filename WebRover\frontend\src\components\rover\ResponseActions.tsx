import { useState } from "react";
import { CheckIcon, DocumentIcon } from "@heroicons/react/24/outline";

export function ResponseActions({ content }: { content: string }) {
  const [copying, setCopying] = useState(false);

  const handleCopy = async () => {
    try {
      setCopying(true);
      await navigator.clipboard.writeText(content);
      setTimeout(() => setCopying(false), 1000);
    } catch (error) {
      console.error("Failed to copy:", error);
      setCopying(false);
    }
  };

  return (
    <div className="flex items-center gap-2 mt-4">
      <button
        onClick={handleCopy}
        className="px-3 py-1.5 text-xs rounded-lg bg-zinc-800/50 hover:bg-zinc-700/50
                 border border-zinc-700/50 text-zinc-300 transition-all duration-200
                 flex items-center gap-1.5"
      >
        {copying ? (
          <CheckIcon className="w-3.5 h-3.5" />
        ) : (
          <DocumentIcon className="w-3.5 h-3.5" />
        )}
        {copying ? "Copied!" : "Copy"}
      </button>
    </div>
  );
}
