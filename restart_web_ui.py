import subprocess
import sys
import time
import requests
import os
import signal

def kill_process_on_port(port):
    """Kill any process running on the specified port"""
    try:
        # Find process using the port
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        lines = result.stdout.split('\n')

        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                # Extract PID (last column)
                parts = line.split()
                if len(parts) > 4:
                    pid = parts[-1]
                    try:
                        subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True)
                        print(f"Killed process {pid} on port {port}")
                    except:
                        pass
    except Exception as e:
        print(f"Error killing process on port {port}: {e}")

def main():
    print("🔄 Restarting Web UI...")

    # Kill any existing web UI on port 7789
    kill_process_on_port(7789)

    # Wait a moment
    time.sleep(2)

    print("🚀 Starting updated Web UI...")

    # Start the new web UI
    try:
        subprocess.run([sys.executable, "simple_web_ui.py"])
    except KeyboardInterrupt:
        print("\n🛑 Web UI stopped")
    except Exception as e:
        print(f"❌ Error starting Web UI: {e}")

if __name__ == "__main__":
    main()
