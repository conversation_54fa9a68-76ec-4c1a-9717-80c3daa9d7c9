// Enhanced Unified AI Control Hub Dashboard JavaScript

class EnhancedDashboard {
  constructor() {
    this.websocket = null;
    this.serviceStatusInterval = null;
    this.initializeComponents();
    this.connectWebSocket();
    this.startServiceMonitoring();
  }

  initializeComponents() {
    console.log("🚀 Initializing Enhanced Dashboard Components");

    // Add event listeners
    this.setupEventListeners();

    // Initialize tabs
    this.setupTabs();

    // Load initial service status
    this.loadServiceStatus();
  }

  setupEventListeners() {
    // Natural Language input
    const nlInput = document.getElementById("nl-input");
    if (nlInput) {
      nlInput.addEventListener("keypress", (e) => {
        if (e.key === "Enter" && e.ctrlKey) {
          this.executeNaturalLanguage();
        }
      });
    }

    // AI Chat input
    const aiInput = document.getElementById("ai-input");
    if (aiInput) {
      aiInput.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
          this.sendAIMessage();
        }
      });
    }
  }

  setupTabs() {
    // Tab switching functionality
    window.switchTab = (tabName) => {
      // Hide all tab contents
      document.querySelectorAll(".tab-content").forEach((tab) => {
        tab.classList.remove("active");
      });

      // Remove active class from all tab buttons
      document.querySelectorAll(".tab-button").forEach((btn) => {
        btn.classList.remove("active");
      });

      // Show selected tab content
      const selectedTab = document.getElementById(`${tabName}-tab`);
      if (selectedTab) {
        selectedTab.classList.add("active");
      }

      // Activate selected tab button
      event.target.classList.add("active");
    };
  }

  connectWebSocket() {
    try {
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      const wsUrl = `${protocol}//${window.location.host}/ws`;

      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        console.log("✅ WebSocket connected");
        this.updateConnectionStatus(true);
      };

      this.websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleWebSocketMessage(data);
      };

      this.websocket.onclose = () => {
        console.log("❌ WebSocket disconnected");
        this.updateConnectionStatus(false);
        // Attempt to reconnect after 3 seconds
        setTimeout(() => this.connectWebSocket(), 3000);
      };

      this.websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    } catch (error) {
      console.error("Failed to connect WebSocket:", error);
    }
  }

  handleWebSocketMessage(data) {
    console.log("📨 WebSocket message:", data);

    switch (data.type) {
      case "natural_language_executed":
        this.updateNaturalLanguageResponse(data);
        break;
      case "browser_action_executed":
        this.updateBrowserAction(data);
        break;
      case "services_status":
        this.updateServicesDisplay(data.services);
        break;
    }
  }

  startServiceMonitoring() {
    this.serviceStatusInterval = setInterval(() => {
      this.loadServiceStatus();
    }, 5000); // Update every 5 seconds
  }

  async loadServiceStatus() {
    try {
      const response = await fetch("/api/services/status");
      const data = await response.json();

      if (data.status === "success") {
        this.updateServicesDisplay(data.services);
      }
    } catch (error) {
      console.error("Failed to load service status:", error);
    }
  }

  updateServicesDisplay(services) {
    // Update header status indicators
    this.updateStatusIndicator(
      "chrome-status",
      services.chrome_browser,
      "🌐 Chrome"
    );
    this.updateStatusIndicator("n8n-status", services.n8n_server, "⚡ N8N");
    this.updateStatusIndicator(
      "mcp-status",
      Object.keys(services.mcp_servers || {}).length > 0,
      "🔌 MCP"
    );
    this.updateStatusIndicator(
      "ai-status",
      Object.keys(services.ai_agents || {}).length > 0,
      "🤖 AI"
    );

    // Update detailed service cards
    this.updateServiceCard(
      "chrome-detail-status",
      services.chrome_browser ? "Running" : "Stopped"
    );
    this.updateServiceCard(
      "n8n-detail-status",
      services.n8n_server ? "Running" : "Stopped"
    );

    // Update MCP servers list
    this.updateMCPServersList(services.mcp_servers || {});

    // Update AI agents list
    this.updateAIAgentsList(services.ai_agents || {});
  }

  updateStatusIndicator(elementId, isActive, label) {
    const element = document.getElementById(elementId);
    if (element) {
      const status = isActive ? "Running" : "Stopped";
      const statusClass = isActive ? "status-active" : "status-inactive";

      element.textContent = `${label}: ${status}`;
      element.className = `service-status ${statusClass}`;
    }
  }

  updateServiceCard(elementId, status) {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = status;
      element.className =
        status === "Running" ? "status-running" : "status-stopped";
    }
  }

  updateMCPServersList(mcpServers) {
    const container = document.getElementById("mcp-servers-list");
    if (container) {
      const serversList = Object.entries(mcpServers)
        .map(
          ([name, info]) =>
            `<div class="server-item">${name}: ${info.status} (${info.port})</div>`
        )
        .join("");

      container.innerHTML =
        serversList || '<div class="no-servers">No MCP servers running</div>';
    }
  }

  updateAIAgentsList(aiAgents) {
    const container = document.getElementById("ai-agents-list");
    if (container) {
      const agentsList = Object.entries(aiAgents)
        .map(
          ([name, info]) =>
            `<div class="agent-item">${name.replace(/_/g, " ")}: ${
              info.status
            }</div>`
        )
        .join("");

      container.innerHTML =
        agentsList || '<div class="no-agents">No AI agents running</div>';
    }
  }

  updateConnectionStatus(connected) {
    // Update UI to show connection status
    const statusElements = document.querySelectorAll(".connection-status");
    statusElements.forEach((el) => {
      el.textContent = connected ? "Connected" : "Disconnected";
      el.className = `connection-status ${
        connected ? "connected" : "disconnected"
      }`;
    });
  }

  async executeNaturalLanguage() {
    const input = document.getElementById("nl-input");
    const responseDiv = document.getElementById("nl-response");

    if (!input || !responseDiv) return;

    const command = input.value.trim();
    if (!command) return;

    try {
      responseDiv.innerHTML =
        '<div class="loading">🔄 Processing your command...</div>';

      const response = await fetch("/api/natural-language/execute", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ command }),
      });

      const result = await response.json();
      this.handleNLResponse(command, result);
    } catch (error) {
      responseDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
    }
  }

  handleNLResponse(command, result) {
    const responseDiv = document.getElementById("nl-response");

    if (result.result && result.result.response) {
      // Format the conversational response
      const response = result.result;
      let suggestionsHtml = "";

      if (response.suggestions && response.suggestions.length > 0) {
        suggestionsHtml = `
          <div class="suggestions">
            <h5>💡 Suggestions:</h5>
            <ul>
              ${response.suggestions
                .map(
                  (s) =>
                    `<li onclick="useSuggestion('${s.replace(
                      /'/g,
                      "\\'"
                    )}')">${s}</li>`
                )
                .join("")}
            </ul>
          </div>
        `;
      }

      let executionResultHtml = "";
      if (response.execution_result) {
        executionResultHtml = `
          <div class="execution-result">
            <h5>⚙️ Execution Details:</h5>
            <pre>${JSON.stringify(response.execution_result, null, 2)}</pre>
          </div>
        `;
      }

      responseDiv.innerHTML = `
        <div class="nl-conversation">
          <div class="user-command">
            <span class="command-label">👤 You said:</span>
            <span class="command-text">"${command}"</span>
          </div>
          <div class="ai-response">
            <span class="response-label">🤖 Assistant:</span>
            <div class="response-text">${response.response}</div>
            ${suggestionsHtml}
            ${executionResultHtml}
          </div>
          <div class="timestamp">⏰ ${new Date(
            response.timestamp || Date.now()
          ).toLocaleTimeString()}</div>
        </div>
      `;
    } else {
      // Fallback for older format
      responseDiv.innerHTML = `
        <div class="nl-result">
          <h4>Command: ${command}</h4>
          <div class="result-content">
            <pre>${JSON.stringify(result, null, 2)}</pre>
          </div>
        </div>
      `;
    }
  }

  async sendAIMessage() {
    const input = document.getElementById("ai-input");
    const chatDiv = document.getElementById("ai-chat");

    if (!input || !chatDiv) return;

    const message = input.value.trim();
    if (!message) return;

    // Add user message to chat
    this.addChatMessage(chatDiv, "👤", message, "user");
    input.value = "";

    // Add loading indicator
    const loadingId = "loading-" + Date.now();
    this.addChatMessage(chatDiv, "🤖", "Thinking...", "system", loadingId);

    try {
      const response = await fetch("/api/ai/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message }),
      });

      const result = await response.json();

      // Remove loading indicator
      const loadingElement = document.getElementById(loadingId);
      if (loadingElement) {
        loadingElement.remove();
      }

      // Add AI response
      if (result.status === "success") {
        this.addChatMessage(chatDiv, "🤖", result.response, "system");
      } else {
        this.addChatMessage(
          chatDiv,
          "❌",
          "Sorry, I encountered an error processing your message.",
          "system"
        );
      }
    } catch (error) {
      // Remove loading indicator
      const loadingElement = document.getElementById(loadingId);
      if (loadingElement) {
        loadingElement.remove();
      }

      this.addChatMessage(chatDiv, "❌", `Error: ${error.message}`, "system");
    }
  }

  addChatMessage(container, icon, text, type, id = null) {
    const messageDiv = document.createElement("div");
    messageDiv.className = `ai-message ${type}`;
    if (id) messageDiv.id = id;

    messageDiv.innerHTML = `
      <span class="message-type">${icon}</span>
      <span class="message-text">${text}</span>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
  }

  updateConnectionStatus(connected) {
    // Update connection indicators
    console.log(
      `Connection status: ${connected ? "Connected" : "Disconnected"}`
    );
  }
}

// Global functions for HTML onclick events
window.executeNaturalLanguage = () => {
  if (window.dashboard) {
    window.dashboard.executeNaturalLanguage();
  }
};

window.sendAIMessage = () => {
  if (window.dashboard) {
    window.dashboard.sendAIMessage();
  }
};

window.useSuggestion = (suggestionText) => {
  const input = document.getElementById("nl-input");
  if (input) {
    input.value = suggestionText;
    input.focus();
  }
};

// Browser control functions
window.navigateRealTime = async () => {
  const urlInput = document.getElementById("browser-url");
  if (!urlInput) return;

  const url = urlInput.value.trim();
  if (!url) return;

  try {
    const response = await fetch("/api/browser/real-time-control", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ action: "navigate", params: { url } }),
    });

    const result = await response.json();
    console.log("Navigation result:", result);

    // Update live view placeholder
    const liveView = document.getElementById("browser-live-view");
    if (liveView) {
      liveView.innerHTML = `<div class="live-status">✅ Navigating to: ${url}</div>`;
    }
  } catch (error) {
    console.error("Navigation error:", error);
  }
};

window.takeScreenshot = async () => {
  try {
    const response = await fetch("/api/browser/real-time-control", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ action: "screenshot", params: {} }),
    });

    const result = await response.json();
    console.log("Screenshot result:", result);

    // Display screenshot if available
    const liveView = document.getElementById("browser-live-view");
    if (liveView && result.result && result.result.screenshot) {
      liveView.innerHTML = `<img src="${result.result.screenshot}" alt="Browser Screenshot" style="max-width: 100%; height: auto;">`;
    }
  } catch (error) {
    console.error("Screenshot error:", error);
  }
};

window.startLiveView = async () => {
  const liveView = document.getElementById("browser-live-view");
  if (!liveView) return;

  try {
    // Start live stream
    liveView.innerHTML = `
      <div class="live-stream-container">
        <div class="live-indicator">🔴 LIVE</div>
        <iframe src="/api/browser/live-stream" style="width: 100%; height: 300px; border: none;"></iframe>
      </div>
    `;
  } catch (error) {
    console.error("Live view error:", error);
    liveView.innerHTML = `<div class="error">❌ Failed to start live view: ${error.message}</div>`;
  }
};

// Workflow functions
window.createWorkflowFromCode = async () => {
  const codeInput = document.getElementById("workflow-code");
  if (!codeInput) return;

  const code = codeInput.value.trim();
  if (!code) return;

  try {
    const response = await fetch("/api/n8n/create-workflow", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ workflow_code: code }),
    });

    const result = await response.json();
    console.log("Workflow creation result:", result);

    if (result.status === "success") {
      alert(`✅ Workflow created successfully: ${result.workflow.name}`);
    } else {
      alert(`❌ Error creating workflow: ${result.error}`);
    }
  } catch (error) {
    console.error("Workflow creation error:", error);
    alert(`❌ Error: ${error.message}`);
  }
};

window.createWorkflowFromDescription = async () => {
  const descInput = document.getElementById("workflow-description");
  if (!descInput) return;

  const description = descInput.value.trim();
  if (!description) return;

  try {
    const response = await fetch("/api/n8n/create-workflow", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ description: description }),
    });

    const result = await response.json();
    console.log("Workflow generation result:", result);

    if (result.status === "success") {
      alert(`✅ Workflow generated successfully: ${result.workflow.name}`);
    } else {
      alert(`❌ Error generating workflow: ${result.error}`);
    }
  } catch (error) {
    console.error("Workflow generation error:", error);
    alert(`❌ Error: ${error.message}`);
  }
};

window.editWorkflow = (workflowId) => {
  console.log(`Editing workflow: ${workflowId}`);
  // Implement workflow editing logic
};

window.openN8N = () => {
  window.open("http://localhost:5678", "_blank");
};

// Initialize dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  console.log("🚀 Initializing Enhanced Unified AI Control Hub Dashboard");
  window.dashboard = new EnhancedDashboard();
});
