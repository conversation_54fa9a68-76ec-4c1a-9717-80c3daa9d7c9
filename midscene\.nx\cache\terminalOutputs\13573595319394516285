
> @midscene/mcp@0.20.1 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\mcp
> rslib build

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;179;249;235mR[39m[38;2;168;244;227ms[39m[38;2;158;238;219ml[39m[38;2;147;233;211mi[39m[38;2;137;227;203mb[39m[38;2;137;227;203m [39m[38;2;126;222;194mv[39m[38;2;116;216;186m0[39m[38;2;105;211;178m.[39m[38;2;95;205;170m8[39m[38;2;84;200;162m.[39m[38;2;74;194;154m0[39m[38;2;74;194;154m
[39m[22m
[1m[36mstart  [39m[22m generating declaration files... [90m(esm)[39m
[1m[32mready  [39m[22m built in [1m31.5[22m s[90m (esm)[39m
[1m[32mready  [39m[22m built in [1m31.5[22m s[90m (cjs)[39m
[1m[33mwarn   [39m[22m [1m[33mBuild warnings: 
[39m[22mFile: [36mC:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\buffer-util.js:1:1[39m
  ⚠ Module not found: Can't resolve 'bufferutil' in 'C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[117:23]
 115 │ if (!process.env.WS_NO_BUFFER_UTIL) {
 116 │   try {
 117 │     const bufferUtil = require('bufferutil');
     ·                        ─────────────────────
 118 │ 
 119 │     module.exports.mask = function (source, mask, output, offset, length) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/websocket.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/in-memory-adapter.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/index.js
 @ ../../node_modules/.pnpm/socket.io@4.8.1/node_modules/socket.io/dist/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

File: [36mC:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\validation.js:1:1[39m
  ⚠ Module not found: Can't resolve 'utf-8-validate' in 'C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[122:24]
 120 │ } /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {
 121 │   try {
 122 │     const isValidUTF8 = require('utf-8-validate');
     ·                         ─────────────────────────
 123 │ 
 124 │     module.exports.isValidUTF8 = function (buf) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/sender.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/in-memory-adapter.js
 @ ../../node_modules/.pnpm/socket.io-adapter@2.5.5/node_modules/socket.io-adapter/dist/index.js
 @ ../../node_modules/.pnpm/socket.io@4.8.1/node_modules/socket.io/dist/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

File: [36mC:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\buffer-util.js:1:1[39m
  ⚠ Module not found: Can't resolve 'bufferutil' in 'C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[117:23]
 115 │ if (!process.env.WS_NO_BUFFER_UTIL) {
 116 │   try {
 117 │     const bufferUtil = require('bufferutil');
     ·                        ─────────────────────
 118 │ 
 119 │     module.exports.mask = function (source, mask, output, offset, length) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/receiver.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/transports/websocket.node.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/index.js
 @ ../../node_modules/.pnpm/socket.io-client@4.8.1/node_modules/socket.io-client/build/cjs/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

File: [36mC:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib\validation.js:1:1[39m
  ⚠ Module not found: Can't resolve 'utf-8-validate' in 'C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\ws@8.17.1\node_modules\ws\lib'
     ╭─[122:24]
 120 │ } /* istanbul ignore else  */ else if (!process.env.WS_NO_UTF_8_VALIDATE) {
 121 │   try {
 122 │     const isValidUTF8 = require('utf-8-validate');
     ·                         ─────────────────────────
 123 │ 
 124 │     module.exports.isValidUTF8 = function (buf) {
     ╰────

 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/lib/receiver.js
 @ ../../node_modules/.pnpm/ws@8.17.1/node_modules/ws/index.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/transports/websocket.node.js
 @ ../../node_modules/.pnpm/engine.io-client@6.6.2/node_modules/engine.io-client/build/cjs/index.js
 @ ../../node_modules/.pnpm/socket.io-client@4.8.1/node_modules/socket.io-client/build/cjs/index.js
 @ ../web-integration/dist/lib/bridge-mode.js
 @ ./src/midscene.ts
 @ ./src/index.ts

[1m[32mready  [39m[22m declaration files generated in [1m34.2[22m s [90m(esm)[39m

[34mFile (cjs)       Size         [39m
[2mdist\[22m[36m450.cjs[39m     13.3 kB   
[2mdist\[22m[35mAPI.mdx[39m     26.5 kB   
[2mdist\[22m[36m18.cjs[39m      549.1 kB  
[2mdist\[22m[36m952.cjs[39m     1492.1 kB 
[2mdist\[22m[36m251.cjs[39m     1543.3 kB 
[2mdist\[22m[36mindex.cjs[39m   12920.1 kB

        [35mTotal:[39m   16544.4 kB


[34mFile (esm)      Size         [39m
[2mdist\[22m[36m450.js[39m     13.4 kB   
[2mdist\[22m[35mAPI.mdx[39m    26.5 kB   
[2mdist\[22m[36m18.js[39m      549.1 kB  
[2mdist\[22m[36m952.js[39m     1490.1 kB 
[2mdist\[22m[36m251.js[39m     1543.3 kB 
[2mdist\[22m[36mindex.js[39m   12898.1 kB

       [35mTotal:[39m   16520.5 kB

