"""
Deep Agent client for AI Agent System.
"""
import os
import requests
import json
import logging
from typing import Dict, List, Optional, Any, Union

logger = logging.getLogger(__name__)

class DeepAgentClient:
    """
    Client for interacting with Deep Agent API.
    """

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        Initialize Deep Agent client.

        Args:
            api_key: API key for Deep Agent. Defaults to DEEP_AGENT_API_KEY env var.
            base_url: Base URL for Deep Agent API. Defaults to DEEP_AGENT_BASE_URL env var.
        """
        self.api_key = api_key or os.getenv("DEEP_AGENT_API_KEY")
        self.base_url = base_url or os.getenv("DEEP_AGENT_BASE_URL", "https://api.deepagent.ai/v1")

        if not self.api_key:
            logger.warning("Deep Agent API key not provided. Some functionality may be limited.")

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                     params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """
        Make a request to the Deep Agent API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request data
            params: Query parameters
            headers: HTTP headers

        Returns:
            Response data as dictionary
        """
        url = f"{self.base_url}/{endpoint}"

        # Set up headers
        _headers = {
            "Content-Type": "application/json"
        }

        if self.api_key:
            _headers["Authorization"] = f"Bearer {self.api_key}"

        if headers:
            _headers.update(headers)

        try:
            response = requests.request(
                method=method,
                url=url,
                headers=_headers,
                params=params,
                json=data
            )

            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Error making request to Deep Agent API: {e}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"Response content: {e.response.text}")
            raise

    def create_agent(self, name: str, capabilities: List[str], description: Optional[str] = None,
                   parameters: Optional[Dict] = None) -> Dict:
        """
        Create a new Deep Agent.

        Args:
            name: Name of the agent
            capabilities: List of agent capabilities
            description: Description of the agent
            parameters: Additional parameters for the agent

        Returns:
            Agent information
        """
        data = {
            "name": name,
            "capabilities": capabilities
        }

        if description:
            data["description"] = description

        if parameters:
            data["parameters"] = parameters

        return self._make_request("POST", "agents", data=data)

    def get_agent(self, agent_id: str) -> Dict:
        """
        Get information about a Deep Agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent information
        """
        return self._make_request("GET", f"agents/{agent_id}")

    def list_agents(self) -> Dict:
        """
        List all Deep Agents.

        Returns:
            List of agents
        """
        return self._make_request("GET", "agents")

    def delete_agent(self, agent_id: str) -> Dict:
        """
        Delete a Deep Agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Result of the operation
        """
        return self._make_request("DELETE", f"agents/{agent_id}")

    def run_agent(self, agent_id: str, input_data: Dict, context: Optional[Dict] = None,
                tools: Optional[List[Dict]] = None, stream: bool = False) -> Union[Dict, Any]:
        """
        Run a Deep Agent with given input.

        Args:
            agent_id: ID of the agent
            input_data: Input data for the agent
            context: Context information for the agent
            tools: Tools available to the agent
            stream: Whether to stream the response

        Returns:
            Agent execution result
        """
        data = {
            "input": input_data
        }

        if context:
            data["context"] = context

        if tools:
            data["tools"] = tools

        if stream:
            # For streaming, we need to handle the response differently
            # This is a simplified implementation - in practice you might use SSE or WebSockets
            headers = {"Accept": "text/event-stream"}
            url = f"{self.base_url}/agents/{agent_id}/run"
            _headers = {"Content-Type": "application/json"}

            if self.api_key:
                _headers["Authorization"] = f"Bearer {self.api_key}"

            _headers.update(headers)

            response = requests.post(url, headers=_headers, json=data, stream=True)
            response.raise_for_status()
            return response
        else:
            return self._make_request("POST", f"agents/{agent_id}/run", data=data)

    def add_tool(self, agent_id: str, tool_data: Dict) -> Dict:
        """
        Add a tool to a Deep Agent.

        Args:
            agent_id: ID of the agent
            tool_data: Tool data

        Returns:
            Result of the operation
        """
        return self._make_request("POST", f"agents/{agent_id}/tools", data=tool_data)

    def train_agent(self, agent_id: str, training_data: List[Dict]) -> Dict:
        """
        Train a Deep Agent with examples.

        Args:
            agent_id: ID of the agent
            training_data: List of training examples

        Returns:
            Training result
        """
        return self._make_request("POST", f"agents/{agent_id}/train", data={"examples": training_data})
