# Main system requirements
fastapi>=0.95.0
uvicorn>=0.21.1
pydantic>=1.10.7
pyjwt>=2.6.0
python-dotenv>=1.0.0
requests>=2.28.2
aiohttp>=3.8.4
websockets>=11.0.1
httpx>=0.24.0
jinja2>=3.1.2
pillow>=9.5.0
python-multipart>=0.0.6

# Google Cloud integration
google-cloud-storage>=2.9.0
google-auth>=2.17.3

# Cloudflare integration
# No specific Python package needed for cloudflared as we're using the executable

# Security
cryptography>=40.0.1
