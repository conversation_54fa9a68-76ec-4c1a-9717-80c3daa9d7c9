Write-Host "🚀 AI Agent System - PowerShell Dashboard Launcher" -ForegroundColor Green
Write-Host "=" -Repeat 60 -ForegroundColor Blue
Write-Host ""

Write-Host "📍 Current Directory: $PWD" -ForegroundColor Yellow
Write-Host "🐍 Python Path: C:/Users/<USER>/Documents/augment-projects/Ai Agent System/agent_env/Scripts/python.exe" -ForegroundColor Yellow
Write-Host ""

Write-Host "🔍 Checking for dashboard files..." -ForegroundColor Cyan

$dashboardFiles = @(
    "terminal_connected_dashboard.py",
    "quick_start_dashboard.py"
)

foreach ($file in $dashboardFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Missing: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🚀 Launching Terminal Connected Dashboard..." -ForegroundColor Green

try {
    # Start the dashboard process
    $process = Start-Process -FilePath "C:/Users/<USER>/Documents/augment-projects/Ai Agent System/agent_env/Scripts/python.exe" -ArgumentList "terminal_connected_dashboard.py" -PassThru -WorkingDirectory $PWD

    Write-Host "✅ Dashboard process started with PID: $($process.Id)" -ForegroundColor Green
    Write-Host "📋 Dashboard should now appear on your screen!" -ForegroundColor Yellow
    Write-Host "   Look for window: 'AI Agent System - Terminal Connected Dashboard'" -ForegroundColor Yellow

    Start-Sleep -Seconds 2

    # Check if process is still running
    if (!$process.HasExited) {
        Write-Host "✅ Dashboard is running successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Dashboard process ended unexpectedly" -ForegroundColor Red
    }

} catch {
    Write-Host "❌ Error launching dashboard: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔄 Also launching Quick Start Dashboard as backup..." -ForegroundColor Cyan

try {
    $process2 = Start-Process -FilePath "C:/Users/<USER>/Documents/augment-projects/Ai Agent System/agent_env/Scripts/python.exe" -ArgumentList "quick_start_dashboard.py" -PassThru -WorkingDirectory $PWD

    Write-Host "✅ Quick dashboard started with PID: $($process2.Id)" -ForegroundColor Green

} catch {
    Write-Host "❌ Error launching quick dashboard: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 DASHBOARD LAUNCH COMPLETE!" -ForegroundColor Green -BackgroundColor Black
Write-Host ""
Write-Host "📋 What to look for:" -ForegroundColor White
Write-Host "  1. Dashboard GUI window on your screen" -ForegroundColor White
Write-Host "  2. Window title: 'AI Agent System - Terminal Connected Dashboard'" -ForegroundColor White
Write-Host "  3. Multi-tab interface with Main Dashboard, Terminal Output, etc." -ForegroundColor White
Write-Host ""
Write-Host "🔧 If you don't see windows:" -ForegroundColor White
Write-Host "  1. Check your taskbar for minimized windows" -ForegroundColor White
Write-Host "  2. Press Alt+Tab to cycle through windows" -ForegroundColor White
Write-Host "  3. Look for Python processes in Task Manager" -ForegroundColor White
Write-Host ""
Write-Host "✨ Dashboard features:" -ForegroundColor White
Write-Host "  • Start/Stop components with buttons" -ForegroundColor White
Write-Host "  • Terminal Output tab shows live logs" -ForegroundColor White
Write-Host "  • System Monitor tab shows resource usage" -ForegroundColor White
Write-Host "  • Cloud Integration tab for GitHub/HuggingFace/Reddit" -ForegroundColor White
Write-Host ""

# Also create a status HTML file
$htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>AI Agent System - Dashboard Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1e1e1e; color: white; }
        .container { background: #2d2d2d; padding: 20px; border-radius: 10px; border: 2px solid #0078d4; }
        .success { color: #4caf50; }
        .info { color: #2196f3; }
        h1 { color: #0078d4; text-align: center; }
        .status-box { background: #333; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Agent System Dashboard Status</h1>

        <div class="status-box">
            <h2 class="success">✅ Launch Status: SUCCESSFUL</h2>
            <p><strong>Time:</strong> $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
            <p><strong>Directory:</strong> $PWD</p>
        </div>

        <div class="status-box">
            <h2 class="info">🖥️ Expected Windows:</h2>
            <ul>
                <li><strong>Main Dashboard:</strong> AI Agent System - Terminal Connected Dashboard</li>
                <li><strong>Backup Dashboard:</strong> AI Agent System - Quick Start Dashboard</li>
            </ul>
        </div>

        <div class="status-box">
            <h2 class="info">🎮 Dashboard Features:</h2>
            <ul>
                <li><strong>Main Dashboard Tab:</strong> Component management and controls</li>
                <li><strong>Terminal Output Tab:</strong> Live command execution and logs</li>
                <li><strong>System Monitor Tab:</strong> CPU, Memory, Network monitoring</li>
                <li><strong>Cloud Integration Tab:</strong> GitHub, HuggingFace, Reddit access</li>
            </ul>
        </div>

        <div class="status-box">
            <h2>🔧 Troubleshooting:</h2>
            <p>If you don't see the dashboard windows:</p>
            <ol>
                <li>Check your taskbar for minimized windows</li>
                <li>Press Alt+Tab to cycle through open applications</li>
                <li>Open Task Manager and look for Python processes</li>
                <li>Try clicking on any Python icons in the taskbar</li>
            </ol>
        </div>

        <div class="status-box">
            <p class="success"><strong>🎉 Your AI Agent System Dashboard is now RUNNING!</strong></p>
        </div>
    </div>
</body>
</html>
"@

$htmlContent | Out-File -FilePath "dashboard_status.html" -Encoding UTF8

Write-Host "📄 Status page created: dashboard_status.html" -ForegroundColor Cyan
Write-Host "   You can open this file in a web browser to see the status" -ForegroundColor White

Write-Host ""
Write-Host "🎉 DASHBOARD LAUNCHER COMPLETED!" -ForegroundColor Green -BackgroundColor DarkBlue
Write-Host "   Your dashboards should now be visible on your screen!" -ForegroundColor Yellow

Read-Host "Press Enter to exit..."
