// Apply theme to dashboard
function applyTheme(theme) {
  const root = document.documentElement;

  switch (theme) {
    case "light":
      root.style.setProperty("--primary-color", "#6200EA");
      root.style.setProperty("--primary-light", "#9D46FF");
      root.style.setProperty("--primary-dark", "#0a0047");
      root.style.setProperty("--accent-color", "#00E5FF");
      root.style.setProperty("--success-color", "#00E676");
      root.style.setProperty("--warning-color", "#FFEA00");
      root.style.setProperty("--error-color", "#FF1744");
      root.style.setProperty("--background-dark", "#FFFFFF");
      root.style.setProperty("--background-medium", "#F0F0F0");
      root.style.setProperty("--background-light", "#E0E0E0");
      root.style.setProperty("--text-primary", "#000000");
      root.style.setProperty("--text-secondary", "rgba(0, 0, 0, 0.7)");
      root.style.setProperty("--text-disabled", "rgba(0, 0, 0, 0.5)");
      root.style.setProperty("--border-color", "rgba(0, 0, 0, 0.12)");
      break;
    case "dark":
    default:
      root.style.setProperty("--primary-color", "#6200EA");
      root.style.setProperty("--primary-light", "#9D46FF");
      root.style.setProperty("--primary-dark", "#0a0047");
      root.style.setProperty("--accent-color", "#00E5FF");
      root.style.setProperty("--success-color", "#00E676");
      root.style.setProperty("--warning-color", "#FFEA00");
      root.style.setProperty("--error-color", "#FF1744");
      root.style.setProperty("--background-dark", "#121212");
      root.style.setProperty("--background-medium", "#1E1E1E");
      root.style.setProperty("--background-light", "#2D2D2D");
      root.style.setProperty("--text-primary", "#FFFFFF");
      root.style.setProperty("--text-secondary", "rgba(255, 255, 255, 0.7)");
      root.style.setProperty("--text-disabled", "rgba(255, 255, 255, 0.5)");
      root.style.setProperty("--border-color", "rgba(255, 255, 255, 0.12)");
      break;
  }
}
