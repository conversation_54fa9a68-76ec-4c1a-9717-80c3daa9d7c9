# Multi-Agent AI System with Cloudflare Tunnel

This document provides instructions for setting up and running the Multi-Agent AI System dashboard with Cloudflare tunneling, allowing your dashboard to be accessible from anywhere on the internet securely.

## Prerequisites

- Python 3.10 or higher
- `.venvAI` virtual environment
- Cloudflared installed (MSI installer included in this repository)
- Google Cloud credentials (optional, for GCP integration)

## Installation

### 1. Install Dependencies

Run the dependency installation script:

```
.\install_tunnel_dependencies.bat
```

This script will:

- Activate the `.venvAI` virtual environment
- Install all required packages for tunneling and Google Cloud integration

### 2. Set Up Google Cloud Integration (Optional)

If you want to use the Google Cloud integration, you need to:

1. Create a Google Cloud Platform service account
2. Download the service account key as a JSON file
3. Save the JSON file to `credentials/gcp-credentials.json`
4. Set the environment variable in your `.env` file:

```
GOOGLE_APPLICATION_CREDENTIALS=credentials/gcp-credentials.json
```

## Running the Dashboard with Cloudflare Tunnel

To start the dashboard with the Cloudflare tunnel, run:

```
.\start_dashboard_with_tunnel.bat
```

This script will:

1. Activate the `.venvAI` virtual environment
2. Start the Cloudflare tunnel
3. Start the FastAPI dashboard
4. Display the public URL where your dashboard is accessible

The dashboard will be available at a URL like `https://ai-agent-dashboard.trycloudflare.com`

## API Endpoints

### Main System Endpoints

- `GET /api/status` - Get system status
- `GET /api/agents` - List all agents
- `GET /api/agents/{agent_id}` - Get information about a specific agent
- `POST /api/agents/{agent_id}/message` - Send a message to an agent
- `POST /api/agent/hitl-continue` - Continue after a human-in-the-loop pause

### Google AI Studio Integration

- `POST /api/ai-studio/generate` - Generate text from a prompt
- `POST /api/ai-studio/export-code` - Export a code snippet for using a prompt

### OnDemand.io Integration

- `GET /api/ondemand/applications` - List all applications
- `POST /api/ondemand/deploy` - Deploy an application
- `POST /api/ondemand/create-rest-agent` - Create a REST API agent

### Google Cloud Integration

- `GET /api/gcp/buckets` - List all GCS buckets
- `POST /api/gcp/upload` - Upload a file to GCS
- `POST /api/gcp/download` - Download a file from GCS
- `POST /api/gcp/files` - List files in a GCS bucket

### Cloudflare Tunnel Integration

- `GET /api/tunnel/info` - Get information about the current Cloudflare tunnel

### WebSocket Endpoints

- `ws://your-host/ws/browser-feed` - Browser feed WebSocket
- `ws://your-host/ws/agent-events` - Agent events WebSocket

## Security Considerations

- The current implementation uses a wildcard CORS policy (`"*"`) for development. For production use, restrict this to specific trusted origins.
- Ensure the JWT secret is properly set in the `.env` file for production:
  ```
  JWT_SECRET=your_secure_secret_key_here
  ```

```
- The Cloudflare tunnel provides encryption for data in transit, but you should still implement proper authentication for sensitive operations.

## Troubleshooting

### Tunnel Issues

If the tunnel doesn't start properly:

1. Check if Cloudflared is installed and available in the PATH
2. Run `cloudflared tunnel list` to see if the tunnel is created
3. Look for error messages in the logs
```
