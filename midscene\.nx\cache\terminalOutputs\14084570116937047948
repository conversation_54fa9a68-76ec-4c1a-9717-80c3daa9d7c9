
> @midscene/core@0.20.1 build C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\core
> modern build

[1m[38;2;189;255;243m [39m[38;2;189;255;243m [39m[38;2;184;252;239mM[39m[38;2;179;249;235mo[39m[38;2;173;247;231md[39m[38;2;168;244;227me[39m[38;2;163;241;223mr[39m[38;2;158;238;219mn[39m[38;2;152;236;215m.[39m[38;2;147;233;211mj[39m[38;2;142;230;207ms[39m[38;2;142;230;207m [39m[38;2;137;227;203mM[39m[38;2;132;224;199mo[39m[38;2;126;222;194md[39m[38;2;121;219;190mu[39m[38;2;116;216;186ml[39m[38;2;111;213;182me[39m[38;2;111;213;182m [39m[38;2;105;211;178mv[39m[38;2;100;208;174m2[39m[38;2;95;205;170m.[39m[38;2;90;202;166m6[39m[38;2;84;200;162m0[39m[38;2;79;197;158m.[39m[38;2;74;194;154m6[39m[38;2;74;194;154m
[39m[22m
[1m[36minfo   [39m[22m Build succeed in [36m45.2s[39m
[1m[36minfo   [39m[22m Bundle generated 30 files

[1m[32mBundle Files                           Size[39m[22m
dist\es\tree.d.ts                      94.0 B
dist\es\utils.d.ts                     1.7 KB
dist\es\index.d.ts                     5.8 KB
dist\es\ai-model.d.ts                  3.1 KB
dist\es\types-b0b4c68e.d.ts            17.6 KB
dist\es\llm-planning-fe687364.d.ts     3.7 KB
dist\lib\tree.d.ts                     94.0 B
dist\lib\utils.d.ts                    1.7 KB
dist\lib\index.d.ts                    5.8 KB
dist\lib\ai-model.d.ts                 3.1 KB
dist\lib\types-b0b4c68e.d.ts           17.6 KB
dist\lib\llm-planning-fe687364.d.ts    3.7 KB
dist\types\tree.d.ts                   94.0 B
dist\types\utils.d.ts                  1.7 KB
dist\types\index.d.ts                  5.8 KB
dist\types\ai-model.d.ts               3.1 KB
dist\types\types-b0b4c68e.d.ts         17.6 KB
dist\types\llm-planning-fe687364.d.ts  3.7 KB
dist\es\index.js                       15.6 KB
dist\es\utils.js                       555.0 B
dist\es\chunk-2OLUMFSC.js              7.8 KB
dist\es\tree.js                        181.0 B
dist\es\ai-model.js                    723.0 B
dist\es\chunk-GYKQPK6J.js              85.9 KB
dist\lib\index.js                      17.3 KB
dist\lib\utils.js                      948.0 B
dist\lib\chunk-2OLUMFSC.js             9.4 KB
dist\lib\tree.js                       310.0 B
dist\lib\ai-model.js                   1.2 KB
dist\lib\chunk-GYKQPK6J.js             89.0 KB
