"""
Google Cloud Platform integration for the AI Agent System
"""
import os
from typing import Dict, Any, List, Optional
from google.cloud import storage
from google.oauth2 import service_account
from core.logger import setup_logger

logger = setup_logger("gcp_integration")

class GoogleCloudIntegration:
    def __init__(self, credentials_path: Optional[str] = None):
        """
        Initialize the Google Cloud Platform integration.

        Args:
            credentials_path: Path to the service account credentials JSON file.
                             If None, will use the GOOGLE_APPLICATION_CREDENTIALS environment variable.
        """
        self.credentials_path = credentials_path

        # Try to load credentials
        try:
            if credentials_path:
                self.credentials = service_account.Credentials.from_service_account_file(credentials_path)
                # Set environment variable for other GCP libraries that may use it
                os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = credentials_path
            else:
                # Use the default credentials from environment variable
                self.credentials = None

            # Initialize GCP clients
            self.storage_client = storage.Client(credentials=self.credentials)

            logger.info("Successfully initialized Google Cloud Platform integration")
        except Exception as e:
            logger.error(f"Failed to initialize Google Cloud Platform integration: {e}")
            raise

    def list_buckets(self) -> List[str]:
        """List all available storage buckets."""
        try:
            buckets = list(self.storage_client.list_buckets())
            return [bucket.name for bucket in buckets]
        except Exception as e:
            logger.error(f"Failed to list buckets: {e}")
            return []

    def upload_file(self, bucket_name: str, source_file_path: str, destination_blob_name: str) -> bool:
        """
        Upload a file to a Google Cloud Storage bucket.

        Args:
            bucket_name: Name of the GCS bucket
            source_file_path: Path to the local file to upload
            destination_blob_name: Name to give the file in GCS

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)

            blob.upload_from_filename(source_file_path)

            logger.info(f"File {source_file_path} uploaded to {destination_blob_name} in bucket {bucket_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to upload file to GCS: {e}")
            return False

    def download_file(self, bucket_name: str, source_blob_name: str, destination_file_path: str) -> bool:
        """
        Download a file from a Google Cloud Storage bucket.

        Args:
            bucket_name: Name of the GCS bucket
            source_blob_name: Name of the file in GCS
            destination_file_path: Local path to save the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(source_blob_name)

            blob.download_to_filename(destination_file_path)

            logger.info(f"File {source_blob_name} downloaded from bucket {bucket_name} to {destination_file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to download file from GCS: {e}")
            return False

    def list_files(self, bucket_name: str, prefix: str = None) -> List[str]:
        """
        List files in a Google Cloud Storage bucket.

        Args:
            bucket_name: Name of the GCS bucket
            prefix: Optional prefix to filter objects by

        Returns:
            List of file names in the bucket
        """
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blobs = list(bucket.list_blobs(prefix=prefix))
            return [blob.name for blob in blobs]
        except Exception as e:
            logger.error(f"Failed to list files in bucket {bucket_name}: {e}")
            return []
