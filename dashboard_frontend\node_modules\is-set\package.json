{"name": "is-set", "version": "2.0.3", "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "sideEffects": false, "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "posttest": "npx aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "keywords": ["map", "set", "collection", "is", "robust"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "homepage": "https://github.com/inspect-js/is-set#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "engines": {"node": ">= 0.4"}}