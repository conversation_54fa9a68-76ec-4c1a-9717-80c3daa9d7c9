{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"environmentName": {"value": "${AZURE_ENV_NAME}"}, "location": {"value": "${AZURE_LOCATION}"}, "resourceGroupName": {"value": "rg-${AZURE_ENV_NAME}"}, "OPENAI_BASE_URL": {"value": ""}, "OPENAI_API_KEY": {"value": ""}, "MIDSCENE_MODEL_NAME": {"value": ""}, "MIDSCENE_USE_GEMINI": {"value": ""}, "GEMINI_API_KEY": {"value": ""}, "GEMINI_MODEL_NAME": {"value": ""}, "VENAI_ENV": {"value": ""}}}