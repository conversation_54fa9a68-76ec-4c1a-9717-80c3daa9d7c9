#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules/@rsbuild/core/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules/@rsbuild/core/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules/@rsbuild/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules/@rsbuild/core/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules/@rsbuild/core/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules/@rsbuild/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/@rsbuild+core@1.3.22/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Ai Agent System/midscene/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@rsbuild/core/bin/rsbuild.js" "$@"
else
  exec node  "$basedir/../@rsbuild/core/bin/rsbuild.js" "$@"
fi
