{"name": "has-proto", "version": "1.2.0", "description": "Does this environment have the ability to get the [[Prototype]] of an object on creation with `__proto__`?", "main": "index.js", "exports": {".": "./index.js", "./accessor": "./accessor.js", "./mutator": "./mutator.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/has-proto.git"}, "keywords": ["prototype", "proto", "set", "get", "__proto__", "getPrototypeOf", "setPrototypeOf", "has"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/has-proto/issues"}, "homepage": "https://github.com/inspect-js/has-proto#readme", "testling": {"files": "test/index.js"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/gopd": "^1.0.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "gopd": "^1.2.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "reflect.getprototypeof": "^1.0.7", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "types"]}, "dependencies": {"dunder-proto": "^1.0.0"}}