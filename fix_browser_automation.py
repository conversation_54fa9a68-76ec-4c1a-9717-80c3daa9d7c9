#!/usr/bin/env python3
"""
Browser Automation System Fixer
Diagnoses and fixes WebRover and Midscene.js integration issues
"""

import subprocess
import sys
import time
import requests
import json
import os
from pathlib import Path

class BrowserAutomationFixer:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.services = {
            "WebRover Backend": {"port": 8001, "url": "http://localhost:8001", "status": False},
            "WebRover Frontend": {"port": 3000, "url": "http://localhost:3000", "status": False},
            "Browser-Use Web UI": {"port": 7788, "url": "http://localhost:7788", "status": False},
            "Midscene MCP": {"port": 8081, "url": "http://localhost:8081", "status": False}
        }
        
    def print_header(self, text):
        print(f"\n{'='*60}")
        print(f"  🔧 {text}")
        print(f"{'='*60}")
        
    def check_service_status(self, service_name, url):
        """Check if a service is running"""
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"✅ {service_name}: Running")
                return True
        except:
            pass
        print(f"❌ {service_name}: Not running")
        return False
        
    def check_all_services(self):
        """Check status of all services"""
        self.print_header("SERVICE STATUS CHECK")
        for service_name, config in self.services.items():
            config["status"] = self.check_service_status(service_name, config["url"])
            
    def install_dependencies(self):
        """Install missing dependencies"""
        self.print_header("INSTALLING DEPENDENCIES")
        
        # Python dependencies
        print("📦 Installing Python dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn", "pydantic", "requests"], 
                      capture_output=True)
        
        # WebRover Frontend dependencies
        frontend_path = self.project_root / "WebRover" / "frontend"
        if frontend_path.exists():
            print("📦 Installing WebRover Frontend dependencies...")
            subprocess.run(["npm", "install"], cwd=frontend_path, capture_output=True)
            
        # Midscene dependencies
        midscene_path = self.project_root / "midscene"
        if midscene_path.exists():
            print("📦 Installing Midscene dependencies...")
            subprocess.run(["npm", "install"], cwd=midscene_path, capture_output=True)
            
        print("✅ Dependencies installation complete")
        
    def start_webrover_backend(self):
        """Start WebRover backend"""
        if not self.services["WebRover Backend"]["status"]:
            print("🚀 Starting WebRover Backend...")
            backend_path = self.project_root / "WebRover" / "backend"
            subprocess.Popen([sys.executable, "simple_main.py"], cwd=backend_path)
            time.sleep(3)
            
    def start_webrover_frontend(self):
        """Start WebRover frontend"""
        if not self.services["WebRover Frontend"]["status"]:
            print("🚀 Starting WebRover Frontend...")
            frontend_path = self.project_root / "WebRover" / "frontend"
            subprocess.Popen(["npm", "run", "dev"], cwd=frontend_path)
            time.sleep(5)
            
    def start_browser_use_webui(self):
        """Start Browser-Use Web UI"""
        if not self.services["Browser-Use Web UI"]["status"]:
            print("🚀 Starting Browser-Use Web UI...")
            webui_path = self.project_root / "browser-use-webui"
            if webui_path.exists():
                subprocess.Popen([sys.executable, "webui.py", "--ip", "127.0.0.1", "--port", "7788"], 
                               cwd=webui_path)
                time.sleep(3)
                
    def start_midscene_mcp(self):
        """Start Midscene MCP Server"""
        if not self.services["Midscene MCP"]["status"]:
            print("🚀 Starting Midscene MCP Server...")
            mcp_path = self.project_root / "midscene" / "packages" / "mcp"
            if mcp_path.exists():
                subprocess.Popen(["npm", "start"], cwd=mcp_path)
                time.sleep(3)
                
    def create_integration_config(self):
        """Create integration configuration"""
        self.print_header("CREATING INTEGRATION CONFIG")
        
        config = {
            "browser_automation": {
                "services": {
                    "webrover_backend": "http://localhost:8001",
                    "webrover_frontend": "http://localhost:3000",
                    "browser_use_webui": "http://localhost:7788",
                    "midscene_mcp": "http://localhost:8081"
                },
                "integration_mode": "unified",
                "auto_fallback": True,
                "health_check_interval": 30
            }
        }
        
        config_path = self.project_root / "config" / "browser_automation_integration.json"
        config_path.parent.mkdir(exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
            
        print(f"✅ Integration config created: {config_path}")
        
    def run_full_fix(self):
        """Run complete fix process"""
        self.print_header("BROWSER AUTOMATION SYSTEM FIXER")
        
        # Step 1: Check current status
        self.check_all_services()
        
        # Step 2: Install dependencies
        self.install_dependencies()
        
        # Step 3: Start services
        self.print_header("STARTING SERVICES")
        self.start_webrover_backend()
        self.start_webrover_frontend()
        self.start_browser_use_webui()
        self.start_midscene_mcp()
        
        # Step 4: Create integration config
        self.create_integration_config()
        
        # Step 5: Final status check
        print("\n⏳ Waiting for services to start...")
        time.sleep(10)
        self.check_all_services()
        
        # Step 6: Summary
        self.print_header("SUMMARY")
        running_services = sum(1 for service in self.services.values() if service["status"])
        total_services = len(self.services)
        
        print(f"🎯 Services running: {running_services}/{total_services}")
        
        if running_services == total_services:
            print("🎉 All services are running successfully!")
            print("\n🌐 Available interfaces:")
            for service_name, config in self.services.items():
                if config["status"]:
                    print(f"   • {service_name}: {config['url']}")
        else:
            print("⚠️  Some services failed to start. Check the logs above.")
            
        return running_services == total_services

if __name__ == "__main__":
    fixer = BrowserAutomationFixer()
    success = fixer.run_full_fix()
    sys.exit(0 if success else 1)
