"""
Memory implementations for LangChain-based agents and chains.
"""
from typing import Dict, List, Any, Optional

from langchain.memory import (
    ConversationBufferMemory,
    ConversationBufferWindowMemory,
    ConversationSummaryMemory,
    VectorStoreRetrieverMemory
)
from langchain_community.vectorstores import Chroma
from langchain_core.embeddings import Embeddings
from langchain_openai import OpenAIEmbeddings


def create_conversation_memory(
    memory_key: str = "history",
    input_key: str = "input",
    output_key: str = "output",
    return_messages: bool = False,
    window_size: Optional[int] = None,
    use_summary: bool = False,
    llm=None
) -> ConversationBufferMemory:
    """
    Create a conversation memory for LangChain chains.

    Args:
        memory_key: Key to store memory under
        input_key: Key for input values
        output_key: Key for output values
        return_messages: Whether to return messages or a string
        window_size: If set, only keeps last N interactions
        use_summary: If True, uses summarization for memory
        llm: Language model (required if use_summary=True)

    Returns:
        A conversation memory instance
    """
    if window_size and use_summary:
        raise ValueError("Cannot use both window_size and use_summary simultaneously")

    if use_summary and not llm:
        raise ValueError("LLM must be provided when using summary memory")

    if window_size:
        return ConversationBufferWindowMemory(
            memory_key=memory_key,
            input_key=input_key,
            output_key=output_key,
            return_messages=return_messages,
            k=window_size
        )

    if use_summary:
        return ConversationSummaryMemory(
            memory_key=memory_key,
            input_key=input_key,
            output_key=output_key,
            return_messages=return_messages,
            llm=llm
        )

    return ConversationBufferMemory(
        memory_key=memory_key,
        input_key=input_key,
        output_key=output_key,
        return_messages=return_messages
    )


def create_vector_memory(
    embeddings: Optional[Embeddings] = None,
    collection_name: str = "conversation_memory",
    k: int = 3
) -> VectorStoreRetrieverMemory:
    """
    Create a vector-store-based memory for more sophisticated retrieval.

    Args:
        embeddings: Embedding model to use (defaults to OpenAI)
        collection_name: Name of the collection in the vector store
        k: Number of similar memories to return

    Returns:
        A vector store memory instance
    """
    if embeddings is None:
        embeddings = OpenAIEmbeddings()

    vectorstore = Chroma(
        collection_name=collection_name,
        embedding_function=embeddings
    )

    retriever = vectorstore.as_retriever(search_kwargs={"k": k})

    return VectorStoreRetrieverMemory(
        retriever=retriever
    )
