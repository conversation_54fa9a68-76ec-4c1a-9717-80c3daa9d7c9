#!/usr/bin/env python3
"""
Enhanced Midscene Chrome Browser Bridge
Creates a direct bridge between Midscene.js and Chrome browser with OpenAI o1-preview reasoning
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
import websockets
from typing import Dict, Any, Optional, List
import os
from datetime import datetime
from pathlib import Path
import base64
import tempfile

# OpenAI integration for reasoning
import openai
from openai import OpenAI

logger = logging.getLogger(__name__)

class MidsceneChromeBridge:
    """Enhanced Chrome Bridge with OpenAI o1-preview reasoning integration"""

    def __init__(self):
        # Initialize OpenAI with the provided API key
        self.openai_client = OpenAI(
            api_key="********************************************************************************************************************************************************************"
        )

        # Best reasoning model for complex tasks
        self.reasoning_model = "o1-preview"  # Best OpenAI reasoning model
        self.fast_model = "gpt-4o"  # For quick tasks

        # Chrome browser settings
        self.chrome_process = None
        self.chrome_debugging_port = 9222
        self.chrome_user_data_dir = None

        # Midscene integration
        self.midscene_websocket = None
        self.midscene_session_id = None
        self.is_connected = False

        # Browser automation state
        self.current_page_url = None
        self.current_page_title = None
        self.page_elements = []
        self.screenshot_data = None

    async def initialize_chrome_bridge(self) -> Dict[str, Any]:
        """Initialize Chrome browser with debugging enabled"""
        try:
            # Create temporary user data directory
            self.chrome_user_data_dir = tempfile.mkdtemp(prefix="midscene_chrome_")

            # Chrome launch arguments for automation
            chrome_args = [
                "--remote-debugging-port=9222",
                f"--user-data-dir={self.chrome_user_data_dir}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--enable-automation",
                "--disable-blink-features=AutomationControlled",
                "--window-size=1920,1080"
            ]

            # Find Chrome executable
            chrome_paths = [
                "C:/Program Files/Google/Chrome/Application/chrome.exe",
                "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
                "chrome.exe",
                "google-chrome",
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            ]

            chrome_exe = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_exe = path
                    break

            if not chrome_exe:
                # Try to find Chrome in PATH
                try:
                    result = subprocess.run(["where", "chrome"], capture_output=True, text=True)
                    if result.returncode == 0:
                        chrome_exe = result.stdout.strip().split('\n')[0]
                except:
                    pass

            if not chrome_exe:
                return {
                    "status": "error",
                    "error": "Chrome browser not found. Please install Google Chrome."
                }

            # Launch Chrome with debugging
            self.chrome_process = subprocess.Popen([chrome_exe] + chrome_args)

            # Wait for Chrome to start
            await asyncio.sleep(3)

            logger.info(f"✅ Chrome launched with debugging on port {self.chrome_debugging_port}")

            # Connect to Chrome DevTools
            await self.connect_to_chrome_devtools()

            return {
                "status": "success",
                "message": "Chrome bridge initialized successfully",
                "debugging_port": self.chrome_debugging_port,
                "process_id": self.chrome_process.pid
            }

        except Exception as e:
            logger.error(f"Failed to initialize Chrome bridge: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def connect_to_chrome_devtools(self):
        """Connect to Chrome DevTools Protocol"""
        try:
            import aiohttp
            import json

            # Get available tabs from Chrome
            async with aiohttp.ClientSession() as session:
                async with session.get(f"http://localhost:{self.chrome_debugging_port}/json") as resp:
                    tabs = await resp.json()

            if tabs:
                # Use the first available tab
                tab = tabs[0]
                websocket_url = tab['webSocketDebuggerUrl']

                # Connect to the tab's WebSocket
                self.chrome_ws = await websockets.connect(websocket_url)

                # Enable necessary domains
                await self.send_chrome_command("Runtime.enable")
                await self.send_chrome_command("Page.enable")
                await self.send_chrome_command("DOM.enable")

                logger.info("✅ Connected to Chrome DevTools")

        except Exception as e:
            logger.error(f"Failed to connect to Chrome DevTools: {e}")

    async def send_chrome_command(self, method: str, params: Dict = None) -> Dict:
        """Send command to Chrome via DevTools Protocol"""
        try:
            command = {
                "id": int(time.time() * 1000),
                "method": method,
                "params": params or {}
            }

            await self.chrome_ws.send(json.dumps(command))

            # Wait for response
            response = await self.chrome_ws.recv()
            return json.loads(response)

        except Exception as e:
            logger.error(f"Chrome command failed: {e}")
            return {"error": str(e)}

    async def analyze_page_with_reasoning(self, task_description: str) -> Dict[str, Any]:
        """Use OpenAI o1-preview to analyze the current page and plan actions"""
        try:
            # Take screenshot
            screenshot_result = await self.take_screenshot()
            if screenshot_result["status"] != "success":
                return screenshot_result

            # Get page DOM structure
            dom_result = await self.get_page_dom()

            # Get page text content
            page_text = await self.get_page_text()

            # Create comprehensive context for reasoning
            context = f"""
Current Page Analysis:
- URL: {self.current_page_url}
- Title: {self.current_page_title}
- Page Text (first 2000 chars): {page_text[:2000]}
- DOM Elements: {len(dom_result.get('nodes', []))} elements detected

Task to accomplish: {task_description}

Available Actions:
1. Click on elements (by selector, text, or description)
2. Fill input fields
3. Navigate to URLs
4. Scroll pages
5. Take screenshots
6. Extract text or data

Please analyze the current page and provide a step-by-step plan to accomplish the task.
Consider the page structure, available elements, and the most efficient way to complete the objective.
            """

            # Use o1-preview for complex reasoning
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.openai_client.chat.completions.create(
                    model=self.reasoning_model,
                    messages=[
                        {
                            "role": "user",
                            "content": context
                        }
                    ],
                    max_completion_tokens=4000
                )
            )

            reasoning_result = response.choices[0].message.content

            # Use GPT-4o to structure the response into actionable steps
            structure_response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.openai_client.chat.completions.create(
                    model=self.fast_model,
                    messages=[
                        {
                            "role": "system",
                            "content": "Convert the reasoning analysis into a structured JSON response with actionable steps. Each step should have: type (click/fill/navigate/scroll/wait), selector/target, value (if needed), and description."
                        },
                        {
                            "role": "user",
                            "content": f"Reasoning analysis: {reasoning_result}\n\nConvert this to structured JSON with steps array."
                        }
                    ],
                    response_format={"type": "json_object"}
                )
            )

            try:
                structured_plan = json.loads(structure_response.choices[0].message.content)
            except:
                structured_plan = {
                    "steps": [
                        {
                            "type": "analysis",
                            "description": reasoning_result
                        }
                    ]
                }

            return {
                "status": "success",
                "reasoning": reasoning_result,
                "action_plan": structured_plan,
                "screenshot_available": True,
                "page_context": {
                    "url": self.current_page_url,
                    "title": self.current_page_title,
                    "elements_count": len(dom_result.get('nodes', []))
                }
            }

        except Exception as e:
            logger.error(f"Page analysis failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def execute_action_plan(self, action_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the action plan generated by AI reasoning"""
        try:
            results = []
            steps = action_plan.get("steps", [])

            for i, step in enumerate(steps):
                step_type = step.get("type")
                description = step.get("description", "")

                logger.info(f"Executing step {i+1}/{len(steps)}: {description}")

                if step_type == "click":
                    result = await self.smart_click(step.get("target", ""), step.get("selector", ""))
                elif step_type == "fill":
                    result = await self.smart_fill(step.get("target", ""), step.get("value", ""))
                elif step_type == "navigate":
                    result = await self.navigate_to_url(step.get("url", ""))
                elif step_type == "scroll":
                    result = await self.scroll_page(step.get("direction", "down"))
                elif step_type == "wait":
                    await asyncio.sleep(step.get("seconds", 2))
                    result = {"status": "success", "action": "wait"}
                else:
                    result = {"status": "skipped", "reason": f"Unknown action type: {step_type}"}

                results.append({
                    "step": i + 1,
                    "type": step_type,
                    "description": description,
                    "result": result
                })

                # Small delay between actions
                await asyncio.sleep(1)

            return {
                "status": "success",
                "steps_executed": len(results),
                "results": results
            }

        except Exception as e:
            logger.error(f"Action plan execution failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def smart_click(self, target_description: str, selector: str = None) -> Dict[str, Any]:
        """Intelligent click using AI vision and element detection"""
        try:
            if selector:
                # Use provided selector
                result = await self.send_chrome_command("Runtime.evaluate", {
                    "expression": f"document.querySelector('{selector}').click()"
                })
            else:
                # Use AI to find the element
                element_result = await self.find_element_by_description(target_description)
                if element_result["status"] == "success":
                    selector = element_result["selector"]
                    result = await self.send_chrome_command("Runtime.evaluate", {
                        "expression": f"document.querySelector('{selector}').click()"
                    })
                else:
                    return element_result

            return {
                "status": "success",
                "action": "click",
                "target": target_description,
                "selector": selector
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def smart_fill(self, target_description: str, value: str) -> Dict[str, Any]:
        """Intelligent form filling using AI element detection"""
        try:
            # Find the input element
            element_result = await self.find_element_by_description(target_description)
            if element_result["status"] != "success":
                return element_result

            selector = element_result["selector"]

            # Fill the field
            result = await self.send_chrome_command("Runtime.evaluate", {
                "expression": f"document.querySelector('{selector}').value = '{value}'"
            })

            return {
                "status": "success",
                "action": "fill",
                "target": target_description,
                "value": value,
                "selector": selector
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def find_element_by_description(self, description: str) -> Dict[str, Any]:
        """Use AI to find elements by natural language description"""
        try:
            # Get all interactive elements
            elements_result = await self.send_chrome_command("Runtime.evaluate", {
                "expression": """
                Array.from(document.querySelectorAll('button, input, a, select, textarea, [onclick], [role="button"]'))
                    .map(el => ({
                        tag: el.tagName,
                        type: el.type || '',
                        text: el.textContent.trim().substring(0, 100),
                        placeholder: el.placeholder || '',
                        id: el.id || '',
                        className: el.className || '',
                        selector: el.tagName.toLowerCase() +
                                 (el.id ? '#' + el.id : '') +
                                 (el.className ? '.' + el.className.split(' ').join('.') : '')
                    }))
                """
            })

            if "result" not in elements_result:
                return {"status": "error", "error": "Failed to get page elements"}

            elements = elements_result["result"]["value"]

            # Use AI to match description to element
            elements_text = "\n".join([
                f"Element {i}: {elem.get('tag', '')} - Text: '{elem.get('text', '')}' - Placeholder: '{elem.get('placeholder', '')}' - ID: '{elem.get('id', '')}' - Selector: '{elem.get('selector', '')}'"
                for i, elem in enumerate(elements[:20])  # Limit to first 20 elements
            ])

            matching_prompt = f"""
Find the best matching element for this description: "{description}"

Available elements:
{elements_text}

Return only the element number (0-based index) that best matches the description.
If no good match is found, return -1.
            """

            match_response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.openai_client.chat.completions.create(
                    model=self.fast_model,
                    messages=[
                        {"role": "user", "content": matching_prompt}
                    ],
                    max_tokens=10
                )
            )

            try:
                element_index = int(match_response.choices[0].message.content.strip())
                if 0 <= element_index < len(elements):
                    selected_element = elements[element_index]
                    return {
                        "status": "success",
                        "element": selected_element,
                        "selector": selected_element["selector"]
                    }
                else:
                    return {"status": "error", "error": "No matching element found"}
            except ValueError:
                return {"status": "error", "error": "Failed to parse AI response"}

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def take_screenshot(self) -> Dict[str, Any]:
        """Take a screenshot of the current page"""
        try:
            result = await self.send_chrome_command("Page.captureScreenshot", {
                "format": "png",
                "quality": 80
            })

            if "result" in result:
                screenshot_data = result["result"]["data"]
                self.screenshot_data = screenshot_data

                return {
                    "status": "success",
                    "screenshot_data": screenshot_data,
                    "format": "png"
                }
            else:
                return {"status": "error", "error": "Failed to capture screenshot"}

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def navigate_to_url(self, url: str) -> Dict[str, Any]:
        """Navigate to a specific URL"""
        try:
            result = await self.send_chrome_command("Page.navigate", {"url": url})

            # Wait for page to load
            await asyncio.sleep(3)

            # Update current page info
            self.current_page_url = url

            return {
                "status": "success",
                "url": url
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def get_page_dom(self) -> Dict[str, Any]:
        """Get the page DOM structure"""
        try:
            result = await self.send_chrome_command("DOM.getDocument")
            return result.get("result", {})
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def get_page_text(self) -> str:
        """Get the text content of the page"""
        try:
            result = await self.send_chrome_command("Runtime.evaluate", {
                "expression": "document.body.innerText"
            })

            if "result" in result:
                return result["result"]["value"]
            return ""

        except Exception as e:
            logger.error(f"Failed to get page text: {e}")
            return ""

    async def scroll_page(self, direction: str = "down", amount: int = 500) -> Dict[str, Any]:
        """Scroll the page"""
        try:
            scroll_expression = f"window.scrollBy(0, {amount if direction == 'down' else -amount})"
            result = await self.send_chrome_command("Runtime.evaluate", {
                "expression": scroll_expression
            })

            return {
                "status": "success",
                "action": "scroll",
                "direction": direction,
                "amount": amount
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def close_chrome_bridge(self) -> Dict[str, Any]:
        """Close the Chrome bridge and clean up"""
        try:
            # Close WebSocket connections
            if hasattr(self, 'chrome_ws'):
                await self.chrome_ws.close()

            # Terminate Chrome process
            if self.chrome_process:
                self.chrome_process.terminate()
                try:
                    self.chrome_process.wait(timeout=5)
                except:
                    self.chrome_process.kill()

            # Clean up temp directory
            if self.chrome_user_data_dir and os.path.exists(self.chrome_user_data_dir):
                import shutil
                shutil.rmtree(self.chrome_user_data_dir, ignore_errors=True)

            return {
                "status": "success",
                "message": "Chrome bridge closed successfully"
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

# Global bridge instance
chrome_bridge = MidsceneChromeBridge()

async def initialize_chrome_bridge():
    """Initialize the Chrome bridge on startup"""
    logger.info("Initializing Midscene Chrome Bridge...")
    result = await chrome_bridge.initialize_chrome_bridge()
    if result["status"] == "success":
        logger.info("✅ Midscene Chrome Bridge established successfully")
    else:
        logger.error(f"❌ Failed to establish Chrome Bridge: {result.get('error')}")
    return result

async def execute_intelligent_automation(task_description: str, url: str = None) -> Dict[str, Any]:
    """Execute intelligent automation using AI reasoning"""
    try:
        # Navigate to URL if provided
        if url:
            await chrome_bridge.navigate_to_url(url)

        # Analyze page and create action plan
        analysis_result = await chrome_bridge.analyze_page_with_reasoning(task_description)
        if analysis_result["status"] != "success":
            return analysis_result

        # Execute the action plan
        execution_result = await chrome_bridge.execute_action_plan(analysis_result["action_plan"])

        return {
            "status": "success",
            "task": task_description,
            "analysis": analysis_result["reasoning"],
            "execution": execution_result
        }

    except Exception as e:
        logger.error(f"Intelligent automation failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }
