
// Unified AI Control Hub Frontend
class UnifiedDashboard {
    constructor() {
        this.ws = null;
        this.agents = [];
        this.systemStats = {};
        this.voiceEnabled = false;
        this.visionEnabled = false;

        this.init();
    }

    async init() {
        await this.loadAgents();
        this.setupWebSocket();
        this.setupEventListeners();
        this.startPolling();

        console.log('🚀 Unified AI Control Hub initialized');
    }

    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('📡 WebSocket connected');
            this.logToConsole('🔗 Real-time connection established');
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };

        this.ws.onclose = () => {
            console.log('📡 WebSocket disconnected');
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.setupWebSocket(), 3000);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'system_stats':
                this.updateSystemStats(data.stats);
                break;
            case 'agent_status_changed':
                this.updateAgentStatus(data.agent, data.status);
                break;
            case 'agent_started':
                this.logToConsole(`✅ Agent ${data.agent} started successfully`);
                break;
            case 'agent_stopped':
                this.logToConsole(`⏹️ Agent ${data.agent} stopped`);
                break;
            case 'voice_command_executed':
                this.logToConsole(`🎤 Voice command executed: ${data.command}`);
                break;
        }
    }

    async loadAgents() {
        try {
            const response = await fetch('/api/agents');
            const data = await response.json();
            this.agents = data.agents;
            this.renderAgents();
        } catch (error) {
            console.error('Failed to load agents:', error);
        }
    }

    renderAgents() {
        const agentsGrid = document.getElementById('agents-grid');
        agentsGrid.innerHTML = '';

        this.agents.forEach(agent => {
            const agentCard = document.createElement('div');
            agentCard.className = `agent-card ${agent.status}`;
            agentCard.innerHTML = `
                <div class="agent-name">${agent.name}</div>
                <div class="agent-description">${agent.description}</div>
                <div class="agent-status">Status: ${agent.status}</div>
                <div class="agent-controls">
                    <button class="btn-agent btn-start" onclick="dashboard.startAgent('${agent.id}')">
                        Start
                    </button>
                    <button class="btn-agent btn-stop" onclick="dashboard.stopAgent('${agent.id}')">
                        Stop
                    </button>
                </div>
            `;
            agentsGrid.appendChild(agentCard);
        });
    }

    async startAgent(agentId) {
        try {
            this.logToConsole(`🚀 Starting agent: ${agentId}`);
            const response = await fetch(`/api/agents/${agentId}/start`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.status === 'success') {
                this.logToConsole(`✅ ${result.message}`);
                await this.loadAgents();
            } else {
                this.logToConsole(`❌ Failed to start ${agentId}: ${result.message}`);
            }
        } catch (error) {
            this.logToConsole(`❌ Error starting agent: ${error.message}`);
        }
    }

    async stopAgent(agentId) {
        try {
            this.logToConsole(`⏹️ Stopping agent: ${agentId}`);
            const response = await fetch(`/api/agents/${agentId}/stop`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.status === 'success') {
                this.logToConsole(`✅ ${result.message}`);
                await this.loadAgents();
            } else {
                this.logToConsole(`❌ Failed to stop ${agentId}: ${result.message}`);
            }
        } catch (error) {
            this.logToConsole(`❌ Error stopping agent: ${error.message}`);
        }
    }

    updateAgentStatus(agentId, status) {
        const agent = this.agents.find(a => a.id === agentId);
        if (agent) {
            agent.status = status;
            this.renderAgents();
        }
    }

    updateSystemStats(stats) {
        this.systemStats = stats;

        // Update header stats
        document.getElementById('cpu-usage').textContent = `CPU: ${stats.cpu?.percent?.toFixed(1) || '--'}%`;
        document.getElementById('memory-usage').textContent = `RAM: ${stats.memory?.percent?.toFixed(1) || '--'}%`;
        document.getElementById('agents-count').textContent = `Agents: ${stats.agents?.running || 0}/${stats.agents?.total || 0}`;
    }

    setupEventListeners() {
        // Voice toggle
        document.getElementById('voice-toggle').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/voice/toggle', { method: 'POST' });
                const result = await response.json();
                this.voiceEnabled = result.voice_enabled;

                const btn = document.getElementById('voice-toggle');
                btn.classList.toggle('active', this.voiceEnabled);

                this.logToConsole(`🎤 Voice control ${this.voiceEnabled ? 'enabled' : 'disabled'}`);

                if (this.voiceEnabled) {
                    this.startVoiceRecognition();
                }
            } catch (error) {
                this.logToConsole(`❌ Voice toggle error: ${error.message}`);
            }
        });

        // Vision toggle
        document.getElementById('vision-toggle').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/vision/toggle', { method: 'POST' });
                const result = await response.json();
                this.visionEnabled = result.vision_enabled;

                const btn = document.getElementById('vision-toggle');
                btn.classList.toggle('active', this.visionEnabled);

                this.logToConsole(`👁️ Vision capabilities ${this.visionEnabled ? 'enabled' : 'disabled'}`);
            } catch (error) {
                this.logToConsole(`❌ Vision toggle error: ${error.message}`);
            }
        });

        // Console command input
        document.getElementById('console-command').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand();
            }
        });
    }

    startVoiceRecognition() {
        if (!('webkitSpeechRecognition' in window)) {
            this.logToConsole('❌ Speech recognition not supported');
            return;
        }

        const recognition = new webkitSpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        recognition.onresult = (event) => {
            const transcript = event.results[event.results.length - 1][0].transcript;
            if (event.results[event.results.length - 1].isFinal) {
                this.handleVoiceCommand(transcript);
            }
        };

        recognition.start();
        this.logToConsole('🎤 Voice recognition started - say commands');
    }

    handleVoiceCommand(command) {
        this.logToConsole(`🎤 Voice command: "${command}"`);

        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'voice_command',
                command: command
            }));
        }
    }

    async executeCommand() {
        const input = document.getElementById('console-command');
        const command = input.value.trim();

        if (!command) return;

        this.logToConsole(`> ${command}`);
        input.value = '';

        // Handle special commands
        if (command.startsWith('/')) {
            this.handleSpecialCommand(command);
        } else {
            // Send as regular command
            this.logToConsole('⚡ Command executed');
        }
    }

    handleSpecialCommand(command) {
        const parts = command.split(' ');
        const cmd = parts[0];

        switch (cmd) {
            case '/help':
                this.logToConsole('📖 Available commands:');
                this.logToConsole('  /help - Show this help');
                this.logToConsole('  /agents - List all agents');
                this.logToConsole('  /stats - Show system stats');
                this.logToConsole('  /clear - Clear console');
                break;

            case '/agents':
                this.logToConsole('🤖 Available agents:');
                this.agents.forEach(agent => {
                    this.logToConsole(`  ${agent.name} (${agent.status})`);
                });
                break;

            case '/stats':
                this.logToConsole('📊 System Statistics:');
                this.logToConsole(`  CPU: ${this.systemStats.cpu?.percent?.toFixed(1) || '--'}%`);
                this.logToConsole(`  Memory: ${this.systemStats.memory?.percent?.toFixed(1) || '--'}%`);
                this.logToConsole(`  Agents: ${this.systemStats.agents?.running || 0}/${this.systemStats.agents?.total || 0}`);
                break;

            case '/clear':
                document.getElementById('console-output').innerHTML = '';
                break;

            default:
                this.logToConsole(`❌ Unknown command: ${cmd}`);
        }
    }

    logToConsole(message) {
        const console = document.getElementById('console-output');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        console.appendChild(logEntry);
        console.scrollTop = console.scrollHeight;
    }

    async startPolling() {
        // Poll for system stats every 5 seconds
        setInterval(async () => {
            try {
                const response = await fetch('/api/system/stats');
                const stats = await response.json();
                this.updateSystemStats(stats);
            } catch (error) {
                console.error('Failed to fetch system stats:', error);
            }
        }, 5000);
    }
}

// Browser control functions
async function navigateTo() {
    const url = document.getElementById('browser-url').value;
    try {
        const response = await fetch('/api/browser/control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'navigate',
                params: { url: url }
            })
        });
        const result = await response.json();
        dashboard.logToConsole(`🌐 Navigated to: ${url}`);
    } catch (error) {
        dashboard.logToConsole(`❌ Navigation failed: ${error.message}`);
    }
}

async function takeScreenshot() {
    try {
        const response = await fetch('/api/browser/control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'screenshot',
                params: {}
            })
        });
        const result = await response.json();
        dashboard.logToConsole(`📸 Screenshot taken (${result.result?.size || 0} bytes)`);
    } catch (error) {
        dashboard.logToConsole(`❌ Screenshot failed: ${error.message}`);
    }
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new UnifiedDashboard();
});

// Service worker registration for PWA support
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/static/sw.js')
        .then(() => console.log('Service Worker registered'))
        .catch(err => console.log('Service Worker registration failed'));
}
        