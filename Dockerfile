# Dockerfile for Unified Agent Dashboard (FastAPI backend)
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies for Python packages (e.g., for <PERSON><PERSON>, tkinter, etc.)
RUN apt-get update && \
    apt-get install -y gcc libffi-dev libsm6 libxext6 libxrender-dev libgl1-mesa-glx && \
    rm -rf /var/lib/apt/lists/*

# Copy requirements and install
COPY unified_agent_dashboard/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the code
COPY . .

# Expose FastAPI port
EXPOSE 8000

# Start FastAPI app
CMD ["uvicorn", "unified_agent_dashboard.web:app", "--host", "0.0.0.0", "--port", "8000"]
