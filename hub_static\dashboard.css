
/* Modern Dark Theme Dashboard */
:root {
    --primary-color: #00ff00;
    --secondary-color: #0078d4;
    --background: #1a1a1a;
    --surface: #2d2d30;
    --surface-light: #3e3e42;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --danger: #ff4444;
    --warning: #ffaa00;
    --success: #00ff88;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    overflow-x: hidden;
}

.header {
    background: var(--surface);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--primary-color);
}

.header h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-toggle {
    padding: 0.5rem 1rem;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-toggle:hover {
    background: var(--secondary-color);
}

.btn-toggle.active {
    background: var(--primary-color);
    color: var(--background);
}

.system-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.main {
    padding: 2rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 2rem;
    height: calc(100vh - 120px);
}

.panel {
    background: var(--surface);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--surface-light);
    overflow-y: auto;
}

.panel h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.agent-card {
    background: var(--surface-light);
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.agent-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.agent-card.running {
    border-color: var(--success);
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.agent-card.stopped {
    border-color: var(--danger);
}

.agent-name {
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.agent-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.agent-controls {
    display: flex;
    gap: 0.5rem;
}

.btn-agent {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-start {
    background: var(--success);
    color: var(--background);
}

.btn-stop {
    background: var(--danger);
    color: var(--text-primary);
}

.btn-agent:hover {
    opacity: 0.8;
}

.browser-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.browser-controls input {
    flex: 1;
    padding: 0.5rem;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    border-radius: 4px;
}

.browser-controls button {
    padding: 0.5rem 1rem;
    background: var(--secondary-color);
    border: none;
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
}

.browser-view {
    background: var(--background);
    border: 1px solid var(--surface-light);
    border-radius: 4px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.console-output {
    background: var(--background);
    border: 1px solid var(--surface-light);
    border-radius: 4px;
    height: 200px;
    padding: 1rem;
    font-family: 'Consolas', monospace;
    font-size: 0.9rem;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.console-input {
    display: flex;
    gap: 1rem;
}

.console-input input {
    flex: 1;
    padding: 0.5rem;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    border-radius: 4px;
    font-family: 'Consolas', monospace;
}

.console-input button {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    border: none;
    color: var(--background);
    border-radius: 4px;
    cursor: pointer;
}

.chart-container {
    margin-bottom: 1rem;
}

.chart-container canvas {
    max-width: 100%;
    height: 100px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, auto);
    }

    .header {
        flex-direction: column;
        gap: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .agents-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s infinite;
}
        