#!/usr/bin/env python3
"""
Dashboard Launcher with File Output
Launches dashboard and writes status to files for verification
"""
import os
import sys
import subprocess
import time
from datetime import datetime

def write_status(message):
    """Write status to both console and file"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    status_message = f"[{timestamp}] {message}"

    print(status_message)

    # Write to status file
    with open("dashboard_launch_status.txt", "a", encoding="utf-8") as f:
        f.write(status_message + "\n")

def main():
    # Clear previous status
    if os.path.exists("dashboard_launch_status.txt"):
        os.remove("dashboard_launch_status.txt")

    write_status("🚀 Starting Dashboard Launcher")
    write_status(f"Python: {sys.executable}")
    write_status(f"Directory: {os.getcwd()}")

    # Check if dashboard files exist
    dashboard_files = [
        "terminal_connected_dashboard.py",
        "quick_start_dashboard.py"
    ]

    for file in dashboard_files:
        if os.path.exists(file):
            write_status(f"✅ Found: {file}")
        else:
            write_status(f"❌ Missing: {file}")

    # Try to launch the terminal connected dashboard
    write_status("🔄 Launching terminal_connected_dashboard.py...")

    try:
        # Start the dashboard process
        process = subprocess.Popen([
            sys.executable,
            "terminal_connected_dashboard.py"
        ],
        cwd=os.getcwd(),
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
        )

        write_status(f"✅ Dashboard process started with PID: {process.pid}")
        write_status("📝 Dashboard should now be visible on your screen")
        write_status("   Look for a window titled 'AI Agent System - Terminal Connected Dashboard'")

        # Wait a moment to see if process starts successfully
        time.sleep(3)

        # Check if process is still running
        if process.poll() is None:
            write_status("✅ Dashboard process is running successfully!")
        else:
            write_status("❌ Dashboard process ended unexpectedly")
            stdout, stderr = process.communicate()
            if stdout:
                write_status(f"STDOUT: {stdout.decode()}")
            if stderr:
                write_status(f"STDERR: {stderr.decode()}")

    except Exception as e:
        write_status(f"❌ Error launching dashboard: {str(e)}")

    # Try to launch quick dashboard as backup
    write_status("🔄 Also launching quick_start_dashboard.py as backup...")

    try:
        process2 = subprocess.Popen([
            sys.executable,
            "quick_start_dashboard.py"
        ], cwd=os.getcwd())

        write_status(f"✅ Quick dashboard started with PID: {process2.pid}")

    except Exception as e:
        write_status(f"❌ Error launching quick dashboard: {str(e)}")

    # Create a simple tkinter test to verify GUI works
    write_status("🔄 Testing GUI with simple window...")

    try:
        import tkinter as tk

        root = tk.Tk()
        root.title("🚀 AI Agent System - GUI Test")
        root.geometry("500x300")
        root.configure(bg="lightblue")

        # Add content
        label1 = tk.Label(root, text="🎉 AI Agent System GUI Working!",
                         font=("Arial", 16, "bold"), bg="lightblue")
        label1.pack(pady=30)

        label2 = tk.Label(root, text="Dashboard should be launching now...",
                         font=("Arial", 12), bg="lightblue")
        label2.pack(pady=10)

        label3 = tk.Label(root, text="Check your taskbar for dashboard windows",
                         font=("Arial", 10), bg="lightblue")
        label3.pack(pady=10)

        def close_test():
            write_status("✅ GUI test window closed")
            root.destroy()

        button = tk.Button(root, text="Close Test Window", command=close_test,
                          font=("Arial", 12), bg="white")
        button.pack(pady=20)

        write_status("✅ GUI test window created and displayed")

        # Auto-close after 10 seconds
        root.after(10000, close_test)

        # Show the window
        root.mainloop()

        write_status("✅ GUI test completed successfully")

    except Exception as e:
        write_status(f"❌ GUI test failed: {str(e)}")

    # Final status
    write_status("🏁 Dashboard launcher completed")
    write_status("📋 Check your screen for:")
    write_status("   1. Terminal Connected Dashboard window")
    write_status("   2. Quick Start Dashboard window")
    write_status("   3. Any error dialogs or messages")
    write_status("📄 Full log saved to: dashboard_launch_status.txt")

    # Also create a simple HTML status page
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <title>AI Agent System Status</title>
    <meta http-equiv="refresh" content="5">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }}
        .container {{ background: white; padding: 20px; border-radius: 10px; }}
        .success {{ color: green; }}
        .error {{ color: red; }}
        .info {{ color: blue; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Agent System Status</h1>
        <p class="info"><strong>Last Updated:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>

        <h2>Launch Status:</h2>
        <p class="success">✅ Dashboard launcher executed</p>
        <p class="success">✅ GUI test window displayed</p>
        <p class="info">📝 Check your desktop for dashboard windows</p>

        <h2>Expected Windows:</h2>
        <ul>
            <li>AI Agent System - Terminal Connected Dashboard</li>
            <li>AI Agent System - Quick Start Dashboard</li>
        </ul>

        <h2>If you don't see windows:</h2>
        <ol>
            <li>Check your taskbar for minimized windows</li>
            <li>Press Alt+Tab to cycle through open windows</li>
            <li>Look for Python processes in Task Manager</li>
            <li>Check dashboard_launch_status.txt for details</li>
        </ol>

        <p><strong>System Directory:</strong> {os.getcwd()}</p>
        <p><strong>Python:</strong> {sys.executable}</p>
    </div>
</body>
</html>'''

    with open("dashboard_status.html", "w", encoding="utf-8") as f:
        f.write(html_content)

    write_status("✅ Created dashboard_status.html for web viewing")

if __name__ == "__main__":
    main()
