"""
n8n workflow integration for the AI Agent System.
This module provides a wrapper around the n8n API to create and manage workflows.
"""
import os
import json
import requests
from typing import Dict, List, Any, Optional
import logging

# Set up logger
logger = logging.getLogger("n8n_integration")

class N8nClient:
    """Client for interacting with n8n API."""

    def __init__(self, base_url: str = None, api_key: str = None):
        """
        Initialize the n8n client.

        Args:
            base_url: Base URL for the n8n instance
            api_key: API key for n8n authentication
        """
        self.base_url = base_url or os.getenv("N8N_BASE_URL", "http://localhost:5678")
        self.api_key = api_key or os.getenv("N8N_API_KEY")

        # Set up headers for API requests
        self.headers = {
            "Content-Type": "application/json"
        }

        if self.api_key:
            self.headers["X-N8N-API-KEY"] = self.api_key

    def get_workflows(self) -> List[Dict[str, Any]]:
        """
        Get all workflows from n8n.

        Returns:
            List of workflows
        """
        url = f"{self.base_url}/api/v1/workflows"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()["data"]

    def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get a specific workflow by ID.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Workflow data
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()["data"]

    def create_workflow(self, name: str, nodes: List[Dict[str, Any]], connections: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new workflow in n8n.

        Args:
            name: Name of the workflow
            nodes: List of workflow nodes
            connections: Connections between nodes

        Returns:
            Created workflow data
        """
        workflow_data = {
            "name": name,
            "nodes": nodes,
            "connections": connections,
            "active": False  # Create as inactive by default
        }

        url = f"{self.base_url}/api/v1/workflows"
        response = requests.post(url, headers=self.headers, json=workflow_data)
        response.raise_for_status()
        return response.json()["data"]

    def update_workflow(self, workflow_id: str, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing workflow.

        Args:
            workflow_id: ID of the workflow
            workflow_data: Updated workflow data

        Returns:
            Updated workflow data
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
        response = requests.put(url, headers=self.headers, json=workflow_data)
        response.raise_for_status()
        return response.json()["data"]

    def delete_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Delete a workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Response data
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}"
        response = requests.delete(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def activate_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Activate a workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Response data
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/activate"
        response = requests.post(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def deactivate_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """
        Deactivate a workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            Response data
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/deactivate"
        response = requests.post(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def execute_workflow(self, workflow_id: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute a workflow with data.

        Args:
            workflow_id: ID of the workflow
            data: Data to pass to the workflow

        Returns:
            Execution response
        """
        url = f"{self.base_url}/api/v1/workflows/{workflow_id}/execute"
        response = requests.post(url, headers=self.headers, json=data or {})
        response.raise_for_status()
        return response.json()

class WorkflowBuilder:
    """Helper class to build n8n workflows."""

    def __init__(self, name: str):
        """
        Initialize the workflow builder.

        Args:
            name: Name of the workflow
        """
        self.name = name
        self.nodes = []
        self.connections = {
            "main": []
        }
        self.node_id_counter = 1

    def add_node(self, type_name: str, parameters: Dict[str, Any], position: Tuple[int, int]) -> int:
        """
        Add a node to the workflow.

        Args:
            type_name: Type name of the node
            parameters: Parameters for the node
            position: Position [x, y] of the node

        Returns:
            ID of the created node
        """
        node_id = self.node_id_counter
        self.node_id_counter += 1

        node = {
            "id": str(node_id),
            "name": type_name,
            "type": type_name,
            "typeVersion": 1,
            "position": list(position),
            "parameters": parameters
        }

        self.nodes.append(node)
        return node_id

    def connect_nodes(self, source_id: int, target_id: int, source_output: int = 0, target_input: int = 0):
        """
        Connect two nodes in the workflow.

        Args:
            source_id: ID of the source node
            target_id: ID of the target node
            source_output: Output index of the source node
            target_input: Input index of the target node
        """
        connection = {
            "node": str(source_id),
            "type": "main",
            "index": source_output
        }

        # Check if there's already a connection to this target
        target_connection_exists = False
        for conn_list in self.connections["main"]:
            if conn_list.get("node") == str(target_id):
                target_connection_exists = True
                conn_list["links"].append(connection)
                break

        # If no connection exists, create a new one
        if not target_connection_exists:
            self.connections["main"].append({
                "node": str(target_id),
                "type": "main",
                "index": target_input,
                "links": [connection]
            })

    def build(self) -> Dict[str, Any]:
        """
        Build the workflow.

        Returns:
            Workflow data
        """
        return {
            "name": self.name,
            "nodes": self.nodes,
            "connections": self.connections,
            "active": False
        }
