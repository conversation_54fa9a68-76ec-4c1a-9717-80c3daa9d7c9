#!/usr/bin/env python3
"""
Unified AI Agent Control Hub
A comprehensive web-based dashboard with voice control, vision capabilities, and mobile support
Integrates all agents, tools, and services into one centralized command center
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import threading
import time
import webbrowser
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import psutil

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

# Import our components
import config
from core.logger import setup_logger
from core.state_manager import StateManager
from core.agent_manager import AgentManager

# Set up logging
logger = setup_logger("unified_control_hub")

class UnifiedAIControlHub:
    """Comprehensive AI Agent Control Hub with web interface and advanced features"""

    def __init__(self):
        self.app = FastAPI(title="Unified AI Control Hub", version="2.0.0")
        self.setup_cors()
        self.setup_routes()
        self.setup_static_files()

        # Initialize core components
        self.state_manager = StateManager()
        self.agent_manager = AgentManager(self.state_manager)

        # WebSocket connections for real-time updates
        self.active_connections: List[WebSocket] = []

        # System monitoring
        self.system_stats = {}
        self.agent_statuses = {}

        # Voice and vision components
        self.voice_enabled = False
        self.vision_enabled = False

        # Initialize agent discovery
        self.discover_agents()

        logger.info("Unified AI Control Hub initialized")

    def setup_cors(self):
        """Setup CORS for web access and mobile support"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Allow all origins for mobile access
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_static_files(self):
        """Setup static file serving"""
        # Create static directories if they don't exist
        static_dir = Path("hub_static")
        static_dir.mkdir(exist_ok=True)

        templates_dir = Path("hub_templates")
        templates_dir.mkdir(exist_ok=True)

        self.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        self.templates = Jinja2Templates(directory=str(templates_dir))

    def setup_routes(self):
        """Setup all API routes"""

        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard(request: Request):
            """Main dashboard page"""
            return self.templates.TemplateResponse("dashboard.html", {
                "request": request,
                "title": "Unified AI Control Hub",
                "agents": self.get_all_agents(),
                "system_stats": self.get_system_stats()
            })

        @self.app.get("/api/agents")
        async def get_agents():
            """Get all available agents"""
            return {"agents": self.get_all_agents()}

        @self.app.post("/api/agents/{agent_name}/start")
        async def start_agent(agent_name: str):
            """Start an agent"""
            try:
                result = await self.start_agent(agent_name)
                await self.broadcast_update({"type": "agent_started", "agent": agent_name, "result": result})
                return {"status": "success", "message": f"Agent {agent_name} started", "result": result}
            except Exception as e:
                logger.error(f"Failed to start agent {agent_name}: {e}")
                return {"status": "error", "message": str(e)}

        @self.app.post("/api/agents/{agent_name}/stop")
        async def stop_agent(agent_name: str):
            """Stop an agent"""
            try:
                result = await self.stop_agent(agent_name)
                await self.broadcast_update({"type": "agent_stopped", "agent": agent_name, "result": result})
                return {"status": "success", "message": f"Agent {agent_name} stopped", "result": result}
            except Exception as e:
                logger.error(f"Failed to stop agent {agent_name}: {e}")
                return {"status": "error", "message": str(e)}

        @self.app.get("/api/system/stats")
        async def get_system_stats():
            """Get system statistics"""
            return self.get_system_stats()

        @self.app.post("/api/voice/toggle")
        async def toggle_voice():
            """Toggle voice control"""
            self.voice_enabled = not self.voice_enabled
            return {"status": "success", "voice_enabled": self.voice_enabled}

        @self.app.post("/api/vision/toggle")
        async def toggle_vision():
            """Toggle vision capabilities"""
            self.vision_enabled = not self.vision_enabled
            return {"status": "success", "vision_enabled": self.vision_enabled}

        @self.app.post("/api/browser/control")
        async def browser_control(request: Request):
            """Browser automation control"""
            data = await request.json()
            action = data.get("action")
            params = data.get("params", {})

            try:
                result = await self.execute_browser_action(action, params)
                return {"status": "success", "result": result}
            except Exception as e:
                return {"status": "error", "message": str(e)}

        @self.app.post("/api/midscene/bridge/init")
        async def init_midscene_bridge():
            """Initialize Midscene bridge connection"""
            try:
                # Import here to avoid startup issues
                from integrations.midscene.bridge_flow import midscene_bridge
                result = await midscene_bridge.create_bridge_connection()
                await self.broadcast_update({
                    "type": "midscene_bridge_status",
                    "status": result["status"],
                    "session_id": result.get("session_id")
                })
                return result
            except Exception as e:
                logger.error(f"Failed to initialize Midscene bridge: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/midscene/smart_click")
        async def midscene_smart_click(request: Request):
            """Execute smart click using Midscene"""
            try:
                data = await request.json()
                element_description = data.get("element_description")
                page_context = data.get("page_context", {})

                from integrations.midscene.bridge_flow import midscene_bridge
                result = await midscene_bridge.execute_click_action(element_description, page_context)
                return result
            except Exception as e:
                logger.error(f"Midscene smart click failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/midscene/smart_fill")
        async def midscene_smart_fill(request: Request):
            """Execute smart fill using Midscene"""
            try:
                data = await request.json()
                field_description = data.get("field_description")
                text_value = data.get("text_value")
                page_context = data.get("page_context", {})

                from integrations.midscene.bridge_flow import midscene_bridge
                result = await midscene_bridge.execute_fill_action(field_description, text_value, page_context)
                return result
            except Exception as e:
                logger.error(f"Midscene smart fill failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/midscene/automation")
        async def midscene_automation(request: Request):
            """Execute automated task using Midscene"""
            try:
                data = await request.json()
                page_url = data.get("page_url")
                task_description = data.get("task_description")
                screenshot_data = data.get("screenshot_data")

                from integrations.midscene.bridge_flow import midscene_bridge
                result = await midscene_bridge.smart_automation(
                    page_url,
                    task_description,
                    bytes.fromhex(screenshot_data) if screenshot_data else None
                )
                return result
            except Exception as e:
                logger.error(f"Midscene automation failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/chrome/bridge/init")
        async def init_chrome_bridge():
            """Initialize Chrome bridge with OpenAI reasoning"""
            try:
                from integrations.midscene.chrome_bridge import initialize_chrome_bridge
                result = await initialize_chrome_bridge()
                await self.broadcast_update({
                    "type": "chrome_bridge_status",
                    "status": result["status"],
                    "debugging_port": result.get("debugging_port")
                })
                return result
            except Exception as e:
                logger.error(f"Failed to initialize Chrome bridge: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/chrome/intelligent_automation")
        async def chrome_intelligent_automation(request: Request):
            """Execute intelligent automation with AI reasoning"""
            try:
                data = await request.json()
                task_description = data.get("task_description")
                url = data.get("url")

                from integrations.midscene.chrome_bridge import execute_intelligent_automation
                result = await execute_intelligent_automation(task_description, url)

                await self.broadcast_update({
                    "type": "automation_completed",
                    "task": task_description,
                    "status": result["status"]
                })

                return result
            except Exception as e:
                logger.error(f"Intelligent automation failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/chrome/analyze_page")
        async def chrome_analyze_page(request: Request):
            """Analyze current page with AI reasoning"""
            try:
                data = await request.json()
                task_description = data.get("task_description", "Analyze this page")

                from integrations.midscene.chrome_bridge import chrome_bridge
                result = await chrome_bridge.analyze_page_with_reasoning(task_description)
                return result
            except Exception as e:
                logger.error(f"Page analysis failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/chrome/screenshot")
        async def chrome_screenshot():
            """Take a screenshot of the current Chrome page"""
            try:
                from integrations.midscene.chrome_bridge import chrome_bridge
                result = await chrome_bridge.take_screenshot()
                return result
            except Exception as e:
                logger.error(f"Screenshot failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.post("/api/chrome/navigate")
        async def chrome_navigate(request: Request):
            """Navigate Chrome to a specific URL"""
            try:
                data = await request.json()
                url = data.get("url")

                from integrations.midscene.chrome_bridge import chrome_bridge
                result = await chrome_bridge.navigate_to_url(url)
                return result
            except Exception as e:
                logger.error(f"Navigation failed: {e}")
                return {"status": "error", "error": str(e)}

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await self.connect_websocket(websocket)
            try:
                while True:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    await self.handle_websocket_message(websocket, message)
            except WebSocketDisconnect:
                await self.disconnect_websocket(websocket)

    async def connect_websocket(self, websocket: WebSocket):
        """Handle new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    async def disconnect_websocket(self, websocket: WebSocket):
        """Handle WebSocket disconnection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def broadcast_update(self, message: Dict[str, Any]):
        """Broadcast update to all connected WebSocket clients"""
        if self.active_connections:
            message["timestamp"] = datetime.now().isoformat()
            disconnected = []

            for connection in self.active_connections:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    disconnected.append(connection)

            # Remove disconnected connections
            for conn in disconnected:
                if conn in self.active_connections:
                    self.active_connections.remove(conn)

    async def handle_websocket_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        msg_type = message.get("type")

        if msg_type == "ping":
            await websocket.send_text(json.dumps({"type": "pong"}))
        elif msg_type == "get_agents":
            await websocket.send_text(json.dumps({
                "type": "agents_list",
                "agents": self.get_all_agents()
            }))
        elif msg_type == "voice_command":
            await self.handle_voice_command(message.get("command", ""))

    def discover_agents(self):
        """Discover all available agents in the system"""
        self.agents = {}

        # Core agents from config
        for agent_name, agent_config in config.AGENT_CONFIG.items():
            self.agents[agent_name] = {
                "name": agent_config["name"],
                "description": agent_config["description"],
                "enabled": agent_config["enabled"],
                "status": "stopped",
                "type": "core",
                "category": "assistant"
            }

        # Discover script-based agents
        script_patterns = [
            "agent_*.py",
            "*_agent.py",
            "jarvis*.py",
            "alpha_evolve*.py",
            "ui_tars*.py"
        ]

        for pattern in script_patterns:
            for script in Path(".").glob(pattern):
                if script.is_file() and script.suffix == ".py":
                    agent_name = script.stem
                    if agent_name not in self.agents:
                        self.agents[agent_name] = {
                            "name": agent_name.replace("_", " ").title(),
                            "description": f"Script-based agent: {agent_name}",
                            "enabled": True,
                            "status": "stopped",
                            "type": "script",
                            "category": "automation",
                            "script_path": str(script)
                        }

        # Discover integration agents
        integrations_dir = Path("integrations")
        if integrations_dir.exists():
            for integration in integrations_dir.iterdir():
                if integration.is_dir() and not integration.name.startswith("_"):
                    agent_name = f"integration_{integration.name}"
                    if agent_name not in self.agents:
                        self.agents[agent_name] = {
                            "name": integration.name.replace("_", " ").title(),
                            "description": f"Integration agent for {integration.name}",
                            "enabled": True,
                            "status": "stopped",
                            "type": "integration",
                            "category": "integration",
                            "integration_path": str(integration)
                        }

    def get_all_agents(self) -> List[Dict[str, Any]]:
        """Get all discovered agents with their current status"""
        agents_list = []
        for agent_name, agent_info in self.agents.items():
            # Update status from process list
            agent_info["status"] = self.get_agent_status(agent_name)
            agents_list.append({
                "id": agent_name,
                **agent_info
            })
        return agents_list

    def get_agent_status(self, agent_name: str) -> str:
        """Check if an agent is currently running"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                cmdline = proc.info.get('cmdline', [])
                if cmdline and any(agent_name in cmd for cmd in cmdline):
                    return "running"
        except:
            pass
        return "stopped"

    async def start_agent(self, agent_name: str) -> Dict[str, Any]:
        """Start an agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent {agent_name} not found")

        agent = self.agents[agent_name]

        try:
            if agent["type"] == "script":
                # Start script-based agent
                script_path = agent.get("script_path")
                if script_path and Path(script_path).exists():
                    process = subprocess.Popen([
                        sys.executable, script_path
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                    self.agents[agent_name]["process"] = process
                    self.agents[agent_name]["pid"] = process.pid

                    return {
                        "method": "script",
                        "pid": process.pid,
                        "script": script_path
                    }

            elif agent["type"] == "integration":
                # Start integration-based agent
                integration_path = agent.get("integration_path")
                main_file = Path(integration_path) / "__main__.py"

                if main_file.exists():
                    process = subprocess.Popen([
                        sys.executable, str(main_file)
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                    self.agents[agent_name]["process"] = process
                    self.agents[agent_name]["pid"] = process.pid

                    return {
                        "method": "integration",
                        "pid": process.pid,
                        "path": integration_path
                    }

            elif agent["type"] == "core":
                # Start core agent through agent manager
                result = await self.agent_manager.start_agent(agent_name)
                return {
                    "method": "core",
                    "result": result
                }

        except Exception as e:
            logger.error(f"Failed to start agent {agent_name}: {e}")
            raise

    async def stop_agent(self, agent_name: str) -> Dict[str, Any]:
        """Stop an agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent {agent_name} not found")

        agent = self.agents[agent_name]

        try:
            # Try to stop via process
            if "process" in agent:
                process = agent["process"]
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()

                del agent["process"]
                if "pid" in agent:
                    del agent["pid"]

                return {"method": "process_termination"}

            # Try to stop via agent manager
            if agent["type"] == "core":
                result = await self.agent_manager.stop_agent(agent_name)
                return {"method": "core", "result": result}

        except Exception as e:
            logger.error(f"Failed to stop agent {agent_name}: {e}")
            raise

    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Get network stats
            network = psutil.net_io_counters()

            # Count running agents
            running_agents = sum(1 for agent in self.agents.values()
                               if self.get_agent_status(agent.get("id", "")) == "running")

            return {
                "timestamp": datetime.now().isoformat(),
                "cpu": {
                    "percent": cpu_percent,
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv
                },
                "agents": {
                    "total": len(self.agents),
                    "running": running_agents,
                    "stopped": len(self.agents) - running_agents
                }
            }
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
            return {"error": str(e)}

    async def execute_browser_action(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute browser automation actions using Playwright"""
        try:
            from playwright.async_api import async_playwright

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=False)
                page = await browser.new_page()

                if action == "navigate":
                    url = params.get("url", "https://google.com")
                    await page.goto(url)
                    result = {"action": "navigate", "url": url, "title": await page.title()}

                elif action == "click":
                    selector = params.get("selector")
                    if selector:
                        await page.click(selector)
                        result = {"action": "click", "selector": selector}
                    else:
                        result = {"error": "No selector provided"}

                elif action == "type":
                    selector = params.get("selector")
                    text = params.get("text", "")
                    if selector:
                        await page.fill(selector, text)
                        result = {"action": "type", "selector": selector, "text": text}
                    else:
                        result = {"error": "No selector provided"}

                elif action == "screenshot":
                    screenshot = await page.screenshot()
                    result = {"action": "screenshot", "size": len(screenshot)}

                else:
                    result = {"error": f"Unknown action: {action}"}

                await browser.close()
                return result

        except Exception as e:
            logger.error(f"Browser action failed: {e}")
            return {"error": str(e)}

    async def handle_voice_command(self, command: str):
        """Handle voice commands"""
        if not self.voice_enabled:
            return

        command = command.lower().strip()

        # Parse voice commands
        if "start" in command:
            # Extract agent name from command
            for agent_name in self.agents.keys():
                if agent_name.lower() in command:
                    try:
                        await self.start_agent(agent_name)
                        await self.broadcast_update({
                            "type": "voice_command_executed",
                            "command": command,
                            "action": "start_agent",
                            "agent": agent_name
                        })
                    except Exception as e:
                        logger.error(f"Voice command failed: {e}")
                    break

        elif "stop" in command:
            # Extract agent name from command
            for agent_name in self.agents.keys():
                if agent_name.lower() in command:
                    try:
                        await self.stop_agent(agent_name)
                        await self.broadcast_update({
                            "type": "voice_command_executed",
                            "command": command,
                            "action": "stop_agent",
                            "agent": agent_name
                        })
                    except Exception as e:
                        logger.error(f"Voice command failed: {e}")
                    break

    async def start_monitoring(self):
        """Start system monitoring task"""
        while True:
            try:
                # Update system stats
                self.system_stats = self.get_system_stats()

                # Broadcast system stats to connected clients
                await self.broadcast_update({
                    "type": "system_stats",
                    "stats": self.system_stats
                })

                # Update agent statuses
                for agent_name in self.agents.keys():
                    current_status = self.get_agent_status(agent_name)
                    if self.agent_statuses.get(agent_name) != current_status:
                        self.agent_statuses[agent_name] = current_status
                        await self.broadcast_update({
                            "type": "agent_status_changed",
                            "agent": agent_name,
                            "status": current_status
                        })

                await asyncio.sleep(5)  # Update every 5 seconds

            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(10)

    def run(self, host: str = "0.0.0.0", port: int = 8080):
        """Run the unified control hub"""
        # Create dashboard HTML template
        self.create_dashboard_template()
        self.create_static_files()

        # Start monitoring task
        async def startup():
            asyncio.create_task(self.start_monitoring())

        self.app.add_event_handler("startup", startup)

        logger.info(f"Starting Unified AI Control Hub on http://{host}:{port}")

        # Open browser automatically
        threading.Timer(2.0, lambda: webbrowser.open(f"http://localhost:{port}")).start()

        # Run the server
        uvicorn.run(self.app, host=host, port=port, log_level="info")

    def create_dashboard_template(self):
        """Create the main dashboard HTML template"""
        template_path = Path("hub_templates/dashboard.html")
        template_path.parent.mkdir(exist_ok=True)

        html_content = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <link rel="stylesheet" href="/static/dashboard.css">
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#1a1a1a">
</head>
<body>
    <div id="app">
        <header class="header">
            <h1>🤖 Unified AI Control Hub</h1>
            <div class="header-controls">
                <button id="voice-toggle" class="btn-toggle">🎤 Voice</button>
                <button id="vision-toggle" class="btn-toggle">👁️ Vision</button>
                <div class="system-stats">
                    <span id="cpu-usage">CPU: --</span>
                    <span id="memory-usage">RAM: --</span>
                    <span id="agents-count">Agents: --</span>
                </div>
            </div>
        </header>

        <main class="main">
            <div class="dashboard-grid">
                <!-- Agent Control Panel -->
                <section class="panel agent-panel">
                    <h2>🎮 Agent Control</h2>
                    <div id="agents-grid" class="agents-grid">
                        <!-- Agents will be populated here -->
                    </div>
                </section>

                <!-- Browser Control Panel -->
                <section class="panel browser-panel">
                    <h2>🌐 Browser Control</h2>
                    <div class="browser-controls">
                        <input type="url" id="browser-url" placeholder="Enter URL..." value="https://google.com">
                        <button onclick="navigateTo()">Navigate</button>
                        <button onclick="takeScreenshot()">Screenshot</button>
                    </div>
                    <div id="browser-view" class="browser-view">
                        <!-- Browser preview will be shown here -->
                    </div>
                </section>

                <!-- System Monitor Panel -->
                <section class="panel monitor-panel">
                    <h2>📊 System Monitor</h2>
                    <div id="system-charts">
                        <div class="chart-container">
                            <canvas id="cpu-chart"></canvas>
                        </div>
                        <div class="chart-container">
                            <canvas id="memory-chart"></canvas>
                        </div>
                    </div>
                </section>

                <!-- Live Console Panel -->
                <section class="panel console-panel">
                    <h2>📺 Live Console</h2>
                    <div id="console-output" class="console-output"></div>
                    <div class="console-input">
                        <input type="text" id="console-command" placeholder="Enter command...">
                        <button onclick="executeCommand()">Execute</button>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script src="/static/dashboard.js"></script>
</body>
</html>
        '''

        template_path.write_text(html_content, encoding='utf-8')

    def create_static_files(self):
        """Create CSS and JavaScript files"""
        static_dir = Path("hub_static")
        static_dir.mkdir(exist_ok=True)

        # Create CSS
        css_content = '''
/* Modern Dark Theme Dashboard */
:root {
    --primary-color: #00ff00;
    --secondary-color: #0078d4;
    --background: #1a1a1a;
    --surface: #2d2d30;
    --surface-light: #3e3e42;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --danger: #ff4444;
    --warning: #ffaa00;
    --success: #00ff88;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    overflow-x: hidden;
}

.header {
    background: var(--surface);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--primary-color);
}

.header h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-toggle {
    padding: 0.5rem 1rem;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-toggle:hover {
    background: var(--secondary-color);
}

.btn-toggle.active {
    background: var(--primary-color);
    color: var(--background);
}

.system-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.main {
    padding: 2rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 2rem;
    height: calc(100vh - 120px);
}

.panel {
    background: var(--surface);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--surface-light);
    overflow-y: auto;
}

.panel h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.agent-card {
    background: var(--surface-light);
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.agent-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.agent-card.running {
    border-color: var(--success);
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.agent-card.stopped {
    border-color: var(--danger);
}

.agent-name {
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.agent-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.agent-controls {
    display: flex;
    gap: 0.5rem;
}

.btn-agent {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-start {
    background: var(--success);
    color: var(--background);
}

.btn-stop {
    background: var(--danger);
    color: var(--text-primary);
}

.btn-agent:hover {
    opacity: 0.8;
}

.browser-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.browser-controls input {
    flex: 1;
    padding: 0.5rem;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    border-radius: 4px;
}

.browser-controls button {
    padding: 0.5rem 1rem;
    background: var(--secondary-color);
    border: none;
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
}

.browser-view {
    background: var(--background);
    border: 1px solid var(--surface-light);
    border-radius: 4px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.console-output {
    background: var(--background);
    border: 1px solid var(--surface-light);
    border-radius: 4px;
    height: 200px;
    padding: 1rem;
    font-family: 'Consolas', monospace;
    font-size: 0.9rem;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.console-input {
    display: flex;
    gap: 1rem;
}

.console-input input {
    flex: 1;
    padding: 0.5rem;
    background: var(--surface-light);
    border: 1px solid var(--secondary-color);
    color: var(--text-primary);
    border-radius: 4px;
    font-family: 'Consolas', monospace;
}

.console-input button {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    border: none;
    color: var(--background);
    border-radius: 4px;
    cursor: pointer;
}

.chart-container {
    margin-bottom: 1rem;
}

.chart-container canvas {
    max-width: 100%;
    height: 100px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, auto);
    }

    .header {
        flex-direction: column;
        gap: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .agents-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s infinite;
}
        '''

        (static_dir / "dashboard.css").write_text(css_content, encoding='utf-8')

        # Create JavaScript
        js_content = '''
// Unified AI Control Hub Frontend
class UnifiedDashboard {
    constructor() {
        this.ws = null;
        this.agents = [];
        this.systemStats = {};
        this.voiceEnabled = false;
        this.visionEnabled = false;

        this.init();
    }

    async init() {
        await this.loadAgents();
        this.setupWebSocket();
        this.setupEventListeners();
        this.startPolling();

        console.log('🚀 Unified AI Control Hub initialized');
    }

    setupWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('📡 WebSocket connected');
            this.logToConsole('🔗 Real-time connection established');
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };

        this.ws.onclose = () => {
            console.log('📡 WebSocket disconnected');
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.setupWebSocket(), 3000);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'system_stats':
                this.updateSystemStats(data.stats);
                break;
            case 'agent_status_changed':
                this.updateAgentStatus(data.agent, data.status);
                break;
            case 'agent_started':
                this.logToConsole(`✅ Agent ${data.agent} started successfully`);
                break;
            case 'agent_stopped':
                this.logToConsole(`⏹️ Agent ${data.agent} stopped`);
                break;
            case 'voice_command_executed':
                this.logToConsole(`🎤 Voice command executed: ${data.command}`);
                break;
        }
    }

    async loadAgents() {
        try {
            const response = await fetch('/api/agents');
            const data = await response.json();
            this.agents = data.agents;
            this.renderAgents();
        } catch (error) {
            console.error('Failed to load agents:', error);
        }
    }

    renderAgents() {
        const agentsGrid = document.getElementById('agents-grid');
        agentsGrid.innerHTML = '';

        this.agents.forEach(agent => {
            const agentCard = document.createElement('div');
            agentCard.className = `agent-card ${agent.status}`;
            agentCard.innerHTML = `
                <div class="agent-name">${agent.name}</div>
                <div class="agent-description">${agent.description}</div>
                <div class="agent-status">Status: ${agent.status}</div>
                <div class="agent-controls">
                    <button class="btn-agent btn-start" onclick="dashboard.startAgent('${agent.id}')">
                        Start
                    </button>
                    <button class="btn-agent btn-stop" onclick="dashboard.stopAgent('${agent.id}')">
                        Stop
                    </button>
                </div>
            `;
            agentsGrid.appendChild(agentCard);
        });
    }

    async startAgent(agentId) {
        try {
            this.logToConsole(`🚀 Starting agent: ${agentId}`);
            const response = await fetch(`/api/agents/${agentId}/start`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.status === 'success') {
                this.logToConsole(`✅ ${result.message}`);
                await this.loadAgents();
            } else {
                this.logToConsole(`❌ Failed to start ${agentId}: ${result.message}`);
            }
        } catch (error) {
            this.logToConsole(`❌ Error starting agent: ${error.message}`);
        }
    }

    async stopAgent(agentId) {
        try {
            this.logToConsole(`⏹️ Stopping agent: ${agentId}`);
            const response = await fetch(`/api/agents/${agentId}/stop`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.status === 'success') {
                this.logToConsole(`✅ ${result.message}`);
                await this.loadAgents();
            } else {
                this.logToConsole(`❌ Failed to stop ${agentId}: ${result.message}`);
            }
        } catch (error) {
            this.logToConsole(`❌ Error stopping agent: ${error.message}`);
        }
    }

    updateAgentStatus(agentId, status) {
        const agent = this.agents.find(a => a.id === agentId);
        if (agent) {
            agent.status = status;
            this.renderAgents();
        }
    }

    updateSystemStats(stats) {
        this.systemStats = stats;

        // Update header stats
        document.getElementById('cpu-usage').textContent = `CPU: ${stats.cpu?.percent?.toFixed(1) || '--'}%`;
        document.getElementById('memory-usage').textContent = `RAM: ${stats.memory?.percent?.toFixed(1) || '--'}%`;
        document.getElementById('agents-count').textContent = `Agents: ${stats.agents?.running || 0}/${stats.agents?.total || 0}`;
    }

    setupEventListeners() {
        // Voice toggle
        document.getElementById('voice-toggle').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/voice/toggle', { method: 'POST' });
                const result = await response.json();
                this.voiceEnabled = result.voice_enabled;

                const btn = document.getElementById('voice-toggle');
                btn.classList.toggle('active', this.voiceEnabled);

                this.logToConsole(`🎤 Voice control ${this.voiceEnabled ? 'enabled' : 'disabled'}`);

                if (this.voiceEnabled) {
                    this.startVoiceRecognition();
                }
            } catch (error) {
                this.logToConsole(`❌ Voice toggle error: ${error.message}`);
            }
        });

        // Vision toggle
        document.getElementById('vision-toggle').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/vision/toggle', { method: 'POST' });
                const result = await response.json();
                this.visionEnabled = result.vision_enabled;

                const btn = document.getElementById('vision-toggle');
                btn.classList.toggle('active', this.visionEnabled);

                this.logToConsole(`👁️ Vision capabilities ${this.visionEnabled ? 'enabled' : 'disabled'}`);
            } catch (error) {
                this.logToConsole(`❌ Vision toggle error: ${error.message}`);
            }
        });

        // Console command input
        document.getElementById('console-command').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand();
            }
        });
    }

    startVoiceRecognition() {
        if (!('webkitSpeechRecognition' in window)) {
            this.logToConsole('❌ Speech recognition not supported');
            return;
        }

        const recognition = new webkitSpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        recognition.onresult = (event) => {
            const transcript = event.results[event.results.length - 1][0].transcript;
            if (event.results[event.results.length - 1].isFinal) {
                this.handleVoiceCommand(transcript);
            }
        };

        recognition.start();
        this.logToConsole('🎤 Voice recognition started - say commands');
    }

    handleVoiceCommand(command) {
        this.logToConsole(`🎤 Voice command: "${command}"`);

        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'voice_command',
                command: command
            }));
        }
    }

    async executeCommand() {
        const input = document.getElementById('console-command');
        const command = input.value.trim();

        if (!command) return;

        this.logToConsole(`> ${command}`);
        input.value = '';

        // Handle special commands
        if (command.startsWith('/')) {
            this.handleSpecialCommand(command);
        } else {
            // Send as regular command
            this.logToConsole('⚡ Command executed');
        }
    }

    handleSpecialCommand(command) {
        const parts = command.split(' ');
        const cmd = parts[0];

        switch (cmd) {
            case '/help':
                this.logToConsole('📖 Available commands:');
                this.logToConsole('  /help - Show this help');
                this.logToConsole('  /agents - List all agents');
                this.logToConsole('  /stats - Show system stats');
                this.logToConsole('  /clear - Clear console');
                break;

            case '/agents':
                this.logToConsole('🤖 Available agents:');
                this.agents.forEach(agent => {
                    this.logToConsole(`  ${agent.name} (${agent.status})`);
                });
                break;

            case '/stats':
                this.logToConsole('📊 System Statistics:');
                this.logToConsole(`  CPU: ${this.systemStats.cpu?.percent?.toFixed(1) || '--'}%`);
                this.logToConsole(`  Memory: ${this.systemStats.memory?.percent?.toFixed(1) || '--'}%`);
                this.logToConsole(`  Agents: ${this.systemStats.agents?.running || 0}/${this.systemStats.agents?.total || 0}`);
                break;

            case '/clear':
                document.getElementById('console-output').innerHTML = '';
                break;

            default:
                this.logToConsole(`❌ Unknown command: ${cmd}`);
        }
    }

    logToConsole(message) {
        const console = document.getElementById('console-output');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        console.appendChild(logEntry);
        console.scrollTop = console.scrollHeight;
    }

    async startPolling() {
        // Poll for system stats every 5 seconds
        setInterval(async () => {
            try {
                const response = await fetch('/api/system/stats');
                const stats = await response.json();
                this.updateSystemStats(stats);
            } catch (error) {
                console.error('Failed to fetch system stats:', error);
            }
        }, 5000);
    }
}

// Browser control functions
async function navigateTo() {
    const url = document.getElementById('browser-url').value;
    try {
        const response = await fetch('/api/browser/control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'navigate',
                params: { url: url }
            })
        });
        const result = await response.json();
        dashboard.logToConsole(`🌐 Navigated to: ${url}`);
    } catch (error) {
        dashboard.logToConsole(`❌ Navigation failed: ${error.message}`);
    }
}

async function takeScreenshot() {
    try {
        const response = await fetch('/api/browser/control', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'screenshot',
                params: {}
            })
        });
        const result = await response.json();
        dashboard.logToConsole(`📸 Screenshot taken (${result.result?.size || 0} bytes)`);
    } catch (error) {
        dashboard.logToConsole(`❌ Screenshot failed: ${error.message}`);
    }
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new UnifiedDashboard();
});

// Service worker registration for PWA support
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/static/sw.js')
        .then(() => console.log('Service Worker registered'))
        .catch(err => console.log('Service Worker registration failed'));
}
        '''

        (static_dir / "dashboard.js").write_text(js_content, encoding='utf-8')

        # Create PWA manifest
        manifest_content = '''
{
    "name": "Unified AI Control Hub",
    "short_name": "AI Hub",
    "description": "Comprehensive AI Agent Control Center",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#1a1a1a",
    "theme_color": "#00ff00",
    "icons": [
        {
            "src": "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%2300ff00'/%3E%3Ctext x='50' y='60' text-anchor='middle' font-size='40' fill='%23000'%3E🤖%3C/text%3E%3C/svg%3E",
            "sizes": "192x192",
            "type": "image/svg+xml"
        }
    ]
}
        '''

        (static_dir / "manifest.json").write_text(manifest_content, encoding='utf-8')

# Main execution
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Unified AI Agent Control Hub")
    parser.add_argument("--host", default="0.0.0.0", help="Host address")
    parser.add_argument("--port", type=int, default=8080, help="Port number")
    args = parser.parse_args()

    # Install required dependencies if not available
    try:
        import playwright
    except ImportError:
        print("🔧 Installing Playwright for browser automation...")
        subprocess.run([sys.executable, "-m", "pip", "install", "playwright"])
        subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"])

    try:
        import psutil
    except ImportError:
        print("🔧 Installing psutil for system monitoring...")
        subprocess.run([sys.executable, "-m", "pip", "install", "psutil"])

    try:
        import uvicorn
        import fastapi
    except ImportError:
        print("🔧 Installing FastAPI and Uvicorn...")
        subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn", "websockets"])

    # Create and run the hub
    hub = UnifiedAIControlHub()
    hub.run(host=args.host, port=args.port)
