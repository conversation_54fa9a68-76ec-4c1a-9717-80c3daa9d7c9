
import vertexai
from vertexai.generative_models import GenerativeModel

# Initialize the Vertex AI SDK
# Make sure to replace 'your-gcp-project-id' with the actual project ID
# and 'your-gcp-location' with the region where you enabled the API (e.g., "us-central1").
vertexai.init(project="gen-lang-client-0774612276", location="us-central1")

# Load the Gemini Pro model
model = GenerativeModel("gemini-1.0-pro")

# Start a chat session
chat = model.start_chat()

print("Welcome to the Gemini Agent!")
print("You can start chatting. Type 'exit' or 'quit' to end the conversation.")

while True:
    user_input = input("You: ")
    if user_input.lower() in ["exit", "quit"]:
        break

    # Send the user's message to the model
    response = chat.send_message(user_input)

    # Print the model's response
    print(f"Gemini: {response.text}")

print("Chat session ended.")
