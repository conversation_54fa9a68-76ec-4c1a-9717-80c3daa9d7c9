#!/usr/bin/env python
"""
Setup script for AI Agent System with Deep Agent integration.
"""
import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('setup_deep_agent.log')
    ]
)

logger = logging.getLogger("setup")

def ensure_venv():
    """Ensure we're in the virtual environment."""
    if "VIRTUAL_ENV" not in os.environ:
        logger.error("Not running in a virtual environment. Please activate .venvAI first.")
        logger.info("You can activate the environment with: .venvAI\\Scripts\\activate")
        return False
    return True

def install_requirements():
    """Install required packages."""
    try:
        logger.info("Installing requirements...")
        subprocess.run([
            sys.executable,
            "-m",
            "pip",
            "install",
            "-r",
            "deep_agent_requirements.txt"
        ], check=True)
        logger.info("Requirements installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error installing requirements: {e}")
        return False

def check_cloudflared():
    """Check if cloudflared is installed."""
    try:
        logger.info("Checking cloudflared installation...")
        result = subprocess.run(["cloudflared", "--version"], capture_output=True, text=True)
        logger.info(f"cloudflared is installed: {result.stdout}")
        return True
    except FileNotFoundError:
        logger.warning("cloudflared is not installed or not in PATH.")
        return False

def install_cloudflared():
    """Install cloudflared if not already installed."""
    if check_cloudflared():
        return True

    system = platform.system().lower()

    try:
        if system == "windows":
            # Try to install from the MSI file if it exists
            msi_path = Path("cloudflared.msi")
            if msi_path.exists():
                logger.info("Installing cloudflared from MSI...")
                subprocess.run(["msiexec", "/i", str(msi_path), "/quiet"], check=True)
                logger.info("cloudflared installed successfully.")
                return True
            else:
                logger.error("cloudflared.msi not found.")
                return False
        else:
            logger.error(f"Unsupported system: {system}")
            return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Error installing cloudflared: {e}")
        return False

def create_cloudflare_config_dir():
    """Create .cloudflared directory."""
    try:
        logger.info("Creating .cloudflared directory...")
        os.makedirs(".cloudflared", exist_ok=True)
        logger.info(".cloudflared directory created.")
        return True
    except Exception as e:
        logger.error(f"Error creating .cloudflared directory: {e}")
        return False

def main():
    """Main function."""
    logger.info("Starting setup for AI Agent System with Deep Agent integration...")

    if not ensure_venv():
        return 1

    if not install_requirements():
        logger.error("Failed to install requirements. Setup aborted.")
        return 1

    if not install_cloudflared():
        logger.warning("Failed to install cloudflared. You may need to install it manually.")

    if not create_cloudflare_config_dir():
        logger.warning("Failed to create .cloudflared directory.")

    logger.info("Setup completed successfully.")
    logger.info("You can now run the system with: python launch_dashboard_with_tunnel.py")

    return 0

if __name__ == "__main__":
    sys.exit(main())
