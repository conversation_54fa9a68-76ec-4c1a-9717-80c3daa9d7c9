@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\web-integration\bin\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\web-integration\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\node_modules;C:\Users\<USER>\Documents\augment-projects\node_modules;C:\Users\<USER>\Documents\node_modules;C:\Users\<USER>\node_modules;C:\Users\<USER>\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\web-integration\bin\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\web-integration\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\packages\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\node_modules;C:\Users\<USER>\Documents\augment-projects\node_modules;C:\Users\<USER>\Documents\node_modules;C:\Users\<USER>\node_modules;C:\Users\<USER>\node_modules;C:\Users\<USER>\Documents\augment-projects\Ai Agent System\midscene\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@midscene\web\bin\midscene-playground" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@midscene\web\bin\midscene-playground" %*
)
